import React, { Component } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { approvalStatusMap, genStatusMap } from '@/utils/settings/sales/customer';
import { DeptmentSelector, mapToSelectors, mapToSwitch } from '@/components/Selectors';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance } from '@/components/Writable';
import { Button, Modal, Form, FormInstance, Card, Tabs } from 'antd';
import {
  receiveMonthListMap,
  yesNoMap,
  invoiceTypeMap,
} from '@/utils/settings/emphiresep/sendorder/ManageTemplate';
import { isEmpty } from 'lodash';
import { msgErr, msgOk } from '@/utils/methods/message';
import Detail, { TMode } from './components/Detail';
import History from './components/History';
import ConfigFrequency from './components/ConfigFrequency';
import SetBankCard from './components/SetBankCard';
import ImportHistory from './components/ImportHistory';
import { downloadFile } from '@/utils/methods/file';
import ConfigTemplate from './components/ConfigTemplate';
import ImportForm from '@/components/UploadForm/ImportForm';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import ConfirmButton, { AsyncButton } from '@/components/Forms/Confirm';
import {
  BranchTitleSpeSelector,
  ContractSubTypeSelector,
  ContractTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { ReCsPop } from '@/components/StandardPop/InnerUserPop';

const service = API.emphiresep.receivable.queryReceivableTemplateList;

interface ViewState {
  addVisible: boolean;
  mode: TMode;
  historyVisible: boolean;
  frequencyVisible: boolean;
  bankCardVisible: boolean;
  importHistoryVisible: boolean;
  batchCreateVisible: boolean;
  configTemplateVisible: boolean;
  contractType: string | null;
}

class View extends Component<POVO, ViewState> {
  private _options: WritableInstance | undefined;

  private exportService = API.emphiresep.receivable.exportFile;

  private formRef = React.createRef<FormInstance>();

  constructor(props: POVO) {
    super(props);
    this.state = {
      addVisible: false,
      mode: 'ADD',
      historyVisible: false,
      frequencyVisible: false,
      bankCardVisible: false,
      importHistoryVisible: false,
      batchCreateVisible: false,
      configTemplateVisible: false,
      contractType: null,
    };
  }

  formColumns: EditeFormProps[] = [
    { label: '客户编号', fieldName: 'custCode', inputRender: 'string' },
    { label: '客户名称', fieldName: 'custName', inputRender: 'string' },
    {
      label: '付款方编号',
      fieldName: 'custPayerCode',
      inputRender: 'string',
      rules: [{ pattern: /^\d+$|^\d+[.]?\d+$/, message: '仅能输入数字查询' }],
    },
    { label: '付款方', fieldName: 'custPayerName', inputRender: 'string' },
    {
      label: '帐单模板编号',
      fieldName: 'receivableTempltId',
      inputRender: 'string',
      rules: [{ pattern: /^\d+$|^\d+[.]?\d+$/, message: '仅能输入数字查询' }],
    },
    { label: '帐单模板名称', fieldName: 'receivableTempltName', inputRender: 'string' },
    {
      label: '开票方式',
      fieldName: 'invoiceType',
      inputRender: () => mapToSelectors(invoiceTypeMap),
    },
    { label: '状态', fieldName: 'isDeleted', inputRender: () => mapToSelectors(genStatusMap) },
    {
      label: '审批状态',
      fieldName: 'approvalStatus',
      inputRender: () => mapToSelectors(approvalStatusMap),
    },
    {
      label: '账单方',
      fieldName: 'payerId',
      inputRender: () => <DeptmentSelector allowClear skipEmptyParam />,
    },
    {
      label: '签约方公司抬头',
      fieldName: 'titleId',
      inputRender: () => <BranchTitleSpeSelector skipEmptyParam />,
    },
    {
      label: '合同大类',
      fieldName: 'contractType',
      inputRender: () => <ContractTypeSelector onChange={this.onContractTypeChange} />,
    },
    {
      label: '合同小类',
      fieldName: 'contractSubType',

      inputRender: () => (
        <ContractSubTypeSelector skipEmptyParam params={{ svcTypeId: this.state.contractType }} />
      ),
    },
    {
      label: '责任客服',
      fieldName: 'liabilityCs',
      inputRender: () => {
        return (
          <ReCsPop
            rowValue="liabilityCs-liabilityCsName"
            keyMap={{ liabilityCs: 'USERID', liabilityCsName: 'REALNAME' }}
          />
        );
      },
    },
  ];

  columns: WritableColumnProps<any>[] = [
    { title: '账单模板名称', dataIndex: 'receivableTempltName', fixed: 'left' },
    { title: '账单模板编号', dataIndex: 'receivableTempltId', fixed: 'left' },
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '大合同编号', dataIndex: 'contractCode' },
    { title: '大合同名称', dataIndex: 'contractName' },
    { title: '合同大类', dataIndex: 'contractTypeName' },
    { title: '合同小类', dataIndex: 'contractSubTypeName' },
    { title: '开票方式', dataIndex: 'invoiceTypeName' },
    {
      title: '状态',
      dataIndex: 'isDeleted',
      render: (value) => <span>{value && genStatusMap.get(+value)}</span>,
    },
    { title: '付款方编号', dataIndex: 'custPayerCode' },
    { title: '付款方', dataIndex: 'custPayerName' },
    { title: '账单方', dataIndex: 'payerName' },
    { title: '收款方', dataIndex: 'payeeName' },
    { title: '收款方开户名', dataIndex: 'bankName' },
    { title: '收款方所属银行', dataIndex: 'bankIdName' },
    { title: '收款方银行账号', dataIndex: 'bankAcct' },
    { title: '收款方开户银行', dataIndex: 'bank' },
    { title: '签约方公司抬头', dataIndex: 'signBranchTitleName' },
    { title: '约定帐单生成日', dataIndex: 'agreedBillGenDt' },
    { title: '约定帐单锁定日', dataIndex: 'agreedBillLockDt' },
    { title: '约定数据提交日/增减员截止日', dataIndex: 'agreedCommitDt' },
    { title: '约定到款日(天)', dataIndex: 'agreedWageArriveDay' },
    {
      title: '到款所属月',
      dataIndex: 'amtReceivedMon',

      render: (value) => <span>{value && receiveMonthListMap.get(value.toString())}</span>,
    },
    { title: '约定工资发放日', dataIndex: 'agreedPayDt' },
    {
      title: '是否社保计算总额',
      dataIndex: 'isSsExclued',

      render: (value) => <span>{value && yesNoMap.get(value.toString())}</span>,
    },
    {
      title: '是否公积金计算总额',
      dataIndex: 'isPfExclued',

      render: (value) => <span>{value && yesNoMap.get(value.toString())}</span>,
    },
    {
      title: '是否工资计算总额',
      dataIndex: 'isWageExclued',

      render: (value) => <span>{value && yesNoMap.get(value.toString())}</span>,
    },
    {
      title: '是否个税计算总额',
      dataIndex: 'isTaxExclued',

      render: (value) => <span>{value && yesNoMap.get(value.toString())}</span>,
    },
    {
      title: '员工按服务费年月分行显示',
      dataIndex: 'isShowType',

      render: (value) => <span>{value && yesNoMap.get(value.toString())}</span>,
    },
  ];

  // 合同大类选择
  onContractTypeChange = (contractType: string) => {
    this.formRef.current?.setFieldsValue({ contractSubType: null });
    this.setState({
      contractType: contractType || null,
    });
  };

  /** 刷新表格 */
  refreshTable = () => {
    if (this._options) this._options!.queries.current = this._options?.queries.pageNum;
    this._options?.request();
  };

  /** 新增/修改/查看详情 */
  onAdd = (mode: TMode) => {
    const row = this._options?.selectedSingleRow;
    if (mode === 'UPDATE' || mode === 'VIEW') if (isEmpty(row)) return msgErr('请先选择数据!');
    if (mode === 'UPDATE') {
      if (!(row.isDeleted !== '1' && (row.approvalStatus == '1' || row.approvalStatus == '5')))
        return msgErr('审批状态为待提交、审批通过 且状态不为“无效”时，可以修改');
    }
    this.setState({ mode, addVisible: true });
  };

  /** 查看历史详情 */
  viewHistory = () => {
    const row = this._options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据!');
    this.setState({ historyVisible: true });
  };

  /** 配置账单模板 */
  configTemplate = () => {
    const row = this._options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据!');
    if (row.isDeleted === '1') return msgErr('记录已经置为无效,不能操作');
    this.setState({ configTemplateVisible: true });
  };

  /** 收费频率设置 */
  frequencyConfig = () => {
    const row = this._options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据!');
    if (row.isDeleted === '1') return msgErr('记录已经置为无效,不能操作');
    this.setState({ frequencyVisible: true });
  };

  /** 设置无效 */
  invalid = () => {
    const selectedSingleRow = this._options?.selectedSingleRow;
    if (isEmpty(selectedSingleRow)) return msgErr('请先选择数据!');
    const isDelete = selectedSingleRow?.isDeleted?.toString();
    if (isDelete === '1') return msgErr('记录已经置为无效,不能操作');
    Modal.confirm({
      content: '选中的数据将被设置无效，是否继续?',
      onOk: async () => {
        if (isDelete === '0') {
          await API.emphiresep.receivable.commitWageOffSetFlow.requests(selectedSingleRow);
          msgOk('操作成功');
          this.refreshTable();
        } else return msgErr('不能无效已经删除的账单模板');
      },
    });
  };

  /** 导出数据 */
  exportData = async () => {
    await this._options?.handleExport(
      {
        service: this.exportService,
        data: (val: POJO) => {
          return {
            ...val,
            headStr: (val.headStr as string).replace(/'/g, ''),
          };
        },
      },
      { columns: this.columns, fileName: '客户账单模板管理.xlsx', condition: { type: '1' } },
    );
  };

  /** 设置账单银行账号 */
  setBankCard = () => {
    const row = this._options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据!');
    if (row.isDeleted === '1') return msgErr('记录已经置为无效,不能操作');
    this.setState({ bankCardVisible: true });
  };

  /** 批量创建账单模板 */
  batchCreate = () => {
    const selectedSingleRow = this._options?.selectedSingleRow;
    if (isEmpty(selectedSingleRow)) return msgErr('请先选择数据!');
    this.setState({ batchCreateVisible: true });
  };

  renderButtons = (options: WritableInstance) => {
    this._options = options;
    return (
      <React.Fragment>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => this.onAdd('VIEW')}>查看详细</Button>
        <Button onClick={this.viewHistory}>查看历史</Button>
        <AuthButtons funcId="********">
          <Button onClick={this.invalid}>设置无效</Button>
        </AuthButtons>
        <AuthButtons funcId="********">
          <Button onClick={this.configTemplate}>配置账单模板</Button>
        </AuthButtons>
        <AuthButtons funcId="********">
          <Button onClick={this.frequencyConfig}>收费频率配置</Button>
        </AuthButtons>
        <AsyncButton onClick={this.exportData}>导出数据</AsyncButton>
      </React.Fragment>
    );
  };

  renderModals = () => {
    const {
      addVisible,
      mode,
      historyVisible,
      frequencyVisible,
      bankCardVisible,
      importHistoryVisible,
      configTemplateVisible,
    } = this.state;
    const fill =
      mode === 'ADD'
        ? {
            isPfExclued: '0',
            isShowType: '1',
            isSsExclued: '0',
            isTaxExclued: '0',
            isWageExclued: '0',
          }
        : this._options?.selectedSingleRow;
    return (
      <React.Fragment>
        <Detail
          visible={addVisible}
          mode={mode}
          data={fill}
          hideHandle={(refresh) => {
            this.setState({ addVisible: false });
            refresh && this.refreshTable();
          }}
        />
        <History
          visible={historyVisible}
          data={this._options?.selectedSingleRow}
          hideHandle={() => this.setState({ historyVisible: false })}
        />
        <ConfigFrequency
          visible={frequencyVisible}
          receivableTempltId={this._options?.selectedSingleRow?.receivableTempltId}
          hideHandle={() => this.setState({ frequencyVisible: false })}
        />
        <SetBankCard
          visible={bankCardVisible}
          receivableTempltId={this._options?.selectedSingleRow?.receivableTempltId}
          hideHandle={() => this.setState({ bankCardVisible: false })}
        />
        <ImportHistory
          visible={importHistoryVisible}
          hideHandle={() => this.setState({ importHistoryVisible: false })}
        />
        <ConfigTemplate
          visible={configTemplateVisible}
          receivableTempltId={this._options?.selectedSingleRow?.receivableTempltId}
          data={this._options?.selectedSingleRow}
          hideHandle={() => this.setState({ configTemplateVisible: false })}
        />
      </React.Fragment>
    );
  };

  render() {
    return (
      <React.Fragment>
        <CachedPage
          service={service}
          formColumns={this.formColumns}
          columns={this.columns}
          renderButtons={this.renderButtons}
          notShowRowSelection
          cached
          formRef={this.formRef}
        />
        {this.renderModals()}
      </React.Fragment>
    );
  }
}

export default View;
