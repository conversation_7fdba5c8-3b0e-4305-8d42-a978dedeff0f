import React, { useEffect } from 'react';
import Codal from '@/components/Codal';
import { Form } from 'antd';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm/index';
import {
  CommonBaseDataSelector,
  CustPayerListSelector,
  mapToSelectors,
} from '@/components/Selectors';
import {
  yesNoMap,
  receiveMonthListMap,
  invoiceTypeMap,
} from '@/utils/settings/emphiresep/sendorder/ManageTemplate';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { AsyncButton } from '@/components/Forms/Confirm';
import { BankInfoSelectPop } from '@/components/StandardPop/BankInfoSelectPop';
import { msgErr, msgOk } from '@/utils/methods/message';
import { getUserName } from '@/utils/model';

interface BillingTemplateApproveFormProps {
  visible: boolean;
  recordData?: POVO;
  hideHandle: (refresh?: boolean) => void;
}

const BillingTemplateApproveForm = (props: BillingTemplateApproveFormProps) => {
  const { recordData } = props;
  const [form] = Form.useForm();

  const kNumberRegex = /^[0-9]*$/;
  const userName = getUserName();
  const approveDisabled = recordData?.approvalStatus == '2';

  const formColumns: EditeFormProps[] = [
    {
      label: '客户名称',
      fieldName: 'custName',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '大合同名称',
      fieldName: 'contractName',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '大合同编号',
      fieldName: 'contractCode',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '合同大类',
      fieldName: 'contractTypeName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '合同小类',
      fieldName: 'contractSubTypeName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '账单模板名称',
      fieldName: 'receivableTempltName',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: 'string',
      formOptions: {
        rules: [{ required: true, message: '请输入账单模板名称' }],
      },
    },
    {
      label: '付款方',
      fieldName: 'custPayerId',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => (
        <CustPayerListSelector skipEmptyParam params={{ custId: recordData?.custId }} />
      ),
      formOptions: {
        rules: [{ required: true, message: '请选择付款方' }],
      },
    },
    {
      label: '账单方',
      fieldName: 'payerName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '收款方',
      fieldName: 'payeeName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '收款方开户名',
      fieldName: 'bankName',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => (
        <BankInfoSelectPop
          title="选择收款方开户名"
          rowValue="bankAcctId-bankId-bankAcct-bank-bankName"
          keyMap={{
            bankAcctId: 'bankAcctId',
            bankId: 'bankId',
            bankAcct: 'bankAcct',
            bank: 'bank',
            bankName: 'bankName',
          }}
          fixedValues={{
            providerId: recordData?.payeeId,
            providerName: recordData?.payeeName,
            providerType: '1',
          }}
        />
      ),
      rules: [{ required: true, message: '请选择收款方开户名' }],
    },
    {
      label: '收款方所属银行',
      fieldName: 'bankId',
      inputProps: {
        disabled: true,
      },
      inputRender: () => <CommonBaseDataSelector params={{ type: '911' }} />,
    },
    {
      label: '收款方银行账号',
      fieldName: 'bankAcct',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '收款方开户银行',
      fieldName: 'bank',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '签约方公司抬头id',
      fieldName: 'signBranchTitleId',
      hiddenForm: true,
    },
    {
      label: '签约方公司抬头',
      fieldName: 'signBranchTitleName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '约定账单生成日',
      fieldName: 'agreedBillGenDt',
      inputRender: 'number',
      inputProps: {
        disabled: approveDisabled,
        precision: 0,
      },
      formOptions: {
        rules: [
          { required: true, max: 2, pattern: kNumberRegex, message: '约定账单生成日的范围 1-31' },
        ],
      },
    },
    {
      label: '约定账单锁定日',
      fieldName: 'agreedBillLockDt',
      inputRender: 'number',
      inputProps: {
        disabled: approveDisabled,
        precision: 0,
      },
      formOptions: {
        rules: [
          { required: true, max: 2, pattern: kNumberRegex, message: '约定账单锁定日的范围 1-31' },
        ],
      },
    },
    {
      label: '约定数据提交日/增减员截止日',
      fieldName: 'agreedCommitDt',
      inputRender: 'number',
      inputProps: {
        disabled: approveDisabled,
        precision: 0,
      },
      formOptions: {
        rules: [
          { pattern: kNumberRegex, max: 2, message: '约定数据提交日/增减员截止日的范围 1-31' },
        ],
      },
    },
    {
      label: '约定到款日(天)',
      fieldName: 'agreedWageArriveDay',
      inputRender: 'number',
      inputProps: {
        disabled: approveDisabled,
        precision: 0,
      },
      formOptions: {
        rules: [{ pattern: kNumberRegex, max: 2, message: '约定到款日(天)的范围 1-31' }],
      },
    },
    {
      label: '到款所属月',
      fieldName: 'amtReceivedMon',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => mapToSelectors(receiveMonthListMap, { allowClear: false }),
    },
    {
      label: '约定工资发放日',
      fieldName: 'agreedPayDt',
      inputRender: 'number',
      inputProps: {
        disabled: approveDisabled,
        precision: 0,
      },
      formOptions: {
        rules: [{ pattern: kNumberRegex, max: 2, message: '约定工资发放日的范围 1-31' }],
      },
      rules:
        recordData?.isIssuingSalary && recordData?.isIssuingSalary == '1'
          ? [{ required: true, message: '大合同是否代发薪资为是，此项必填' }]
          : [],
    },
    {
      label: '是否社保计算总额',
      fieldName: 'isSsExclued',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '是否公积金计算总额',
      fieldName: 'isPfExclued',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '是否工资计算总额',
      fieldName: 'isWageExclued',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '是否个税计算总额',
      fieldName: 'isTaxExclued',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '员工按服务费年月分行显示',
      fieldName: 'isShowType',
      inputProps: {
        disabled: approveDisabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '开票方式',
      fieldName: 'invoiceType',
      inputRender: () => mapToSelectors(invoiceTypeMap, { allowClear: true }),
      inputProps: {
        disabled: approveDisabled,
      },
    },
    {
      label: '历史备注',
      fieldName: 'additionalNotes',
      inputRender: 'text',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '备注',
      fieldName: 'newAdditionalNotes',
      inputRender: 'text',
      hidden: recordData?.approvalStatus != '4',
      rules: [{ required: true, message: '请填写补充说明' }],
    },
    {
      label: '附件',
      fieldName: 'fileId',
      inputRender: 'upload',
      inputProps: {
        disabled: recordData?.approvalStatus != '4',
        bizType: '********',
      },
    },
    {
      label: '账套审批过程',
      fieldName: 'approvalProcess',
      inputRender: 'text',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '审批意见',
      fieldName: 'approvalRemarks',
      inputRender: 'text',
      hidden: recordData?.approvalStatus == '4',
      rules: [{ required: true, message: '请填写审批意见' }],
    },
  ];

  useEffect(() => {
    if (props.visible) {
      form.setFieldsValue({ ...recordData, bankId: String(recordData?.bankId || '') });
    } else {
      form.resetFields();
    }
  }, [props.visible]);

  const checkParams = (values: POJO<string>) => {
    if (values.receivableTempltName.length > 100) return '账单模板名称长度不能超过100';
    const agreedBillGenDt = values.agreedBillGenDt;
    const agreedBillGenDtNum = +agreedBillGenDt;
    if (agreedBillGenDt.length > 2 || agreedBillGenDtNum < 1 || agreedBillGenDtNum > 31)
      return '约定账单生成日的范围 1-31';
    const agreedBillLockDt = values.agreedBillLockDt;
    const agreedBillLockDtNum = +agreedBillLockDt;
    if (agreedBillLockDt.length > 2 || agreedBillLockDtNum < 1 || agreedBillLockDtNum > 31)
      return '约定账单锁定日的范围 1-31';
    const agreedCommitDt = values.agreedCommitDt;
    if (agreedCommitDt !== null && agreedCommitDt !== undefined && agreedCommitDt !== '') {
      const agreedCommitDtNum = +agreedCommitDt;
      if (agreedCommitDtNum < 1 || agreedCommitDtNum > 31)
        return '约定数据提交日/增减员截止日的范围 1-31';
    }
    const agreedWageArriveDay = values.agreedWageArriveDay;
    if (
      agreedWageArriveDay !== null &&
      agreedWageArriveDay !== undefined &&
      agreedWageArriveDay !== ''
    ) {
      const agreedWageArriveDayNum = +agreedWageArriveDay;
      if (agreedWageArriveDayNum < 1 || agreedWageArriveDayNum > 31)
        return '约定到款日(天)的范围 1-31';
    }
    const agreedPayDt = values.agreedPayDt;
    if (agreedPayDt !== null && agreedPayDt !== undefined && agreedPayDt !== '') {
      const agreedPayDtNum = +agreedPayDt;
      if (agreedPayDtNum < 1 || agreedPayDtNum > 31) return '约定工资发放日的范围 1-31';
    }
    return undefined;
  };

  const onApprove = async () => {
    const values = await form.validateFields();
    if (recordData?.approvalStatus != '1') {
      const error = checkParams(values);
      if (error) return msgErr(error);
      if (values.agreedWageArriveDay === null || values.agreedWageArriveDay === undefined)
        values.agreedWageArriveDay = '';
      if (values.agreedCommitDt === null || values.agreedCommitDt === undefined)
        values.agreedCommitDt = '';
      if (values.agreedPayDt === null || values.agreedPayDt === undefined) values.agreedPayDt = '';
      values.receivableTempltName = values.receivableTempltName
        .replace(/（/g, '(')
        .replace(/）/g, ')');
    }
    values.additionalNotes =
      (recordData?.additionalNotes || '') +
      '\r\n' +
      values.newAdditionalNotes +
      ' ' +
      userName +
      ' ' +
      new Date().toLocaleString();
    await API.emphiresep.receivable.updateReceivableTemplateApprove.requests({
      ...recordData,
      ...values,
    });
    msgOk('操作成功');
    props.hideHandle(true);
  };

  const onBack = async () => {
    const values = await form.validateFields();
    await API.emphiresep.receivable.backReceivableTemplate.requests({ ...recordData, ...values });
    msgOk('驳回成功');
    props.hideHandle(true);
  };

  const renderFooter = () => {
    return (
      <div style={{ textAlign: 'center' }}>
        {recordData?.approvalStatus == '4' && (
          <AsyncButton onClick={onApprove}>提交审批</AsyncButton>
        )}
        {recordData?.approvalStatus != '4' && (
          <>
            <AsyncButton onClick={onApprove}>审批通过</AsyncButton>
            <AsyncButton onClick={onBack}>驳回</AsyncButton>
          </>
        )}
      </div>
    );
  };

  return (
    <Codal
      title="账单模板确认流程"
      visible={props.visible}
      onCancel={() => props.hideHandle()}
      footer={renderFooter()}
      width={1200}
      destroyOnClose
    >
      <FormElement3 form={form}>
        <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns} />
      </FormElement3>
    </Codal>
  );
};

export default BillingTemplateApproveForm;
