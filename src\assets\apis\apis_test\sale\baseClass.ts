class BaseEntity {
  /** activityNameEn */
  activityNameEn = '';

  /** add */
  add = false;

  /** 审批人 */
  approveBy = '';

  /** 流程审批日期 */
  approveDt = '';

  /** 流程审批意见 */
  approveOpinion = '';

  /** 流程审批状态  0:待提交 1:待审批  2:审批通过 -1:驳回 -2：终止 */
  approveStatus = '';

  /** approveStatusName */
  approveStatusName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户id */
  custCode = '';

  /** 客户id */
  custId = undefined;

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否纳入统计 0:是 1:否 */
  isCount = '';

  /** isCountName */
  isCountName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 责任客服 */
  liabilityName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** activityNameEn */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程id */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备案所属月 */
  recDt = '';

  /** 备案id */
  recId = undefined;

  /** 备案类型 0:长期 1:按月 */
  recType = '';

  /** recTypeName */
  recTypeName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 未使用EOS原因详述 */
  unuseReason = '';

  /** 未使用EOS原因类型(base_data_code=9010) */
  unuseType = '';

  /** unuseTypeName */
  unuseTypeName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workItemId */
  workItemId = '';
}

class BatchSaveOrUpdateCustPayerSJVO {
  /** 新增集合 */
  insertList = [];

  /** 更新集合 */
  updateList = [];
}

class BatchSaveOrUpdateCustPayerSalaryVO {
  /** 新增薪资相关约定 */
  insertList = new CustomerPayerSalary();

  /** 更新薪资相关约定集合 */
  updateList = [];
}

class BatchSaveOrUpdateCustPayerVO {
  /** 新增客户付款方 */
  insertList = new CustomerPayerDTO();

  /** 更新客户付款方信息集合 */
  updateList = [];
}

class CRMProductlineMapping {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.DELETD_BACTH           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  deletdBacth = undefined;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** getDate */
  getDate = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.IS_VALID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  isValid = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_COMPANY_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineCompanyId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_MAPPING_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineMappingId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_NAME           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_SALER_AREA_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineSalerAreaId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_SALER_AREANAME           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineSalerAreaname = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_SALER_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineSalerId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_SALER_PROVIDER_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineSalerProviderId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_SALER_PROVIDERNAME           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineSalerProvidername = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_SALERNAME           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineSalername = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_TYPE           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineType = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_PRODUCTLINE_MAPPING.PRODUCTLINE_VISIT_ID           ibatorgenerated Mon Nov 28 14:09:02 CST 2011 */
  productlineVisitId = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new InsuranceType();

  /** message */
  message = '';

  /** t */
  t = new InsuranceType();
}

class Contract {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agereedAmtReceiveMon = '';

  /** agreedPayDt */
  agreedPayDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** areaId */
  areaId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** attTypeDraftId */
  attTypeDraftId = '';

  /** attTypeDraftName */
  attTypeDraftName = '';

  /** attTypeLegalId */
  attTypeLegalId = '';

  /** attTypeLegalName */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  billDt = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** isIssuingSalary */
  contractCategeryName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartA = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartB = '';

  /** contractProductLineIds */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractRetrieveDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStatus = '';

  /** contractStatusName */
  contractStatusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSubType = '';

  /** 合同类别（子类）name */
  contractSubTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** contractSvcStateName */
  contractSvcStateName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractTemplateId = '';

  /** contractTerminationDate */
  contractTerminationDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 合同类别 */
  contractVersionType = '';

  /** isIssuingSalary */
  contractVersionTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByName */
  createByName = '';

  /** createByParty */
  createByParty = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  createType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  creditPeriod = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  custPayerId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custSealDt = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerSize */
  customerSize = '';

  /** defStatus */
  defStatus = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** draftRemark */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** estimateFirstBillDate */
  estimateFirstBillDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectedIncrease */
  expectedIncrease = '';

  /** expectedIncreaseOld */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务name */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /** firstWgApproveId */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区Name */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司Name */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** private String contractSvcStateName; */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** governingBranchName */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** fileId */
  importFileId = '';

  /** fileName */
  importFileName = '';

  /** inId */
  inId = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** invoiceMoney */
  invoiceMoney = '';

  /** NP-8564 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
  isAdjustRenewContract = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  isAssign = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** isDefer */
  isDefer = '';

  /** isDeferName */
  isDeferName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** isIssuingSalary */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** isSameInsur */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** isSecondaryDev */
  isSecondaryDev = '';

  /** isSecondaryDevName */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApprovalStatus = '';

  /** legalRemark */
  legalRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** nextContractId */
  nextContractId = '';

  /** nextContractName */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** noChange */
  noChange = false;

  /** 非标合同审批单:NON_STA_COCT_APPR_ID */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  parentContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  payCollectPoint = '';

  /** payMonth */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  payerName = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApprovalStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  processInstanceId = '';

  /** productLineIdLogs */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectPlanRequest = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectRemark = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerType */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 报价单集合id */
  quoIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  renewedContractNum = '';

  /** reportElEvacuatedDate */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** reportGlEvacuatedDate */
  reportGlEvacuatedDate = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** roleCode */
  roleCode = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增存量标识 （手工） */
  salFlagManual = '';

  /** salFlagManualName */
  salFlagManualName = '';

  /** salFlagName */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApprove = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealApproveStatus = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealOpinion = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signArea */
  signArea = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** signBranchTitleIdAreaId */
  signBranchTitleIdAreaId = '';

  /** signBranchTitleIdBranchId */
  signBranchTitleIdBranchId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 撤单原因 */
  stopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcRegion = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadFileName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadUrl = '';

  /** upt */
  upt = false;

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** workitemId */
  workitemId = '';
}

class ContractDTO {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** 流程节点表的活动英文名称 */
  activityNameEn = '';

  /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
  activityStatus = '';

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** 约定到款月 */
  agereedAmtReceiveMon = '';

  /** 薪资发放日/约定薪资发放日 */
  agreedPayDt = '';

  /** 约定到款日 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** 审批通过时间 */
  approveDt = '';

  /** 审批通过时间到 */
  approveDtEnd = '';

  /** 审批通过时间从 */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** 大区ID */
  areaId = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaType = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaTypeName = '';

  /** 终稿 */
  attTypeDraftId = '';

  /** 终稿name */
  attTypeDraftName = '';

  /** 法务 */
  attTypeLegalId = '';

  /** 法务name */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 账单日期 */
  billDt = '';

  /** 城市 */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** 已经确认的工作流程 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 签约人均金额 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** contractCategeryName */
  contractCategeryName = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** 合同文件名 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** 签约人数 */
  contractHeadcount = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 合同甲方 */
  contractPartA = '';

  /** 合同乙方 */
  contractPartB = '';

  /** 合同产品线id集合 */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** 客户盖章后，合同回收时间 */
  contractRetrieveDt = '';

  /** 合同起始日期 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** 合同状态：0 初始；1 审批中；2 审批通过；3 退回修改；4 驳回终止 */
  contractStatus = '';

  /** 合同状态name */
  contractStatusName = '';

  /** 合同结束日期 */
  contractStopDate = '';

  /** 合同终止原因 */
  contractStopReason = '';

  /** 合同小类 */
  contractSubType = '';

  /** 合同类别（子类）名称 */
  contractSubTypeName = '';

  /** 合同服务状态：0新签1续签2过期3终止服务 */
  contractSvcState = '';

  /** 合同服务状态name */
  contractSvcStateName = '';

  /** 合同模板编号 */
  contractTemplateId = '';

  /** 新平台合同终止时填写的终止时间 */
  contractTerminationDate = '';

  /** 合同大类 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** contractVersionTypeName */
  contractVersionTypeName = '';

  /** 创建人 */
  createByName = '';

  /** 创建人 */
  createByParty = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 合同生成方式 */
  createType = '';

  /** 账期（天） */
  creditPeriod = '';

  /** 客服审批 */
  csApproval = '';

  /** 客服审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** 必须是同一个客户，当前执行的合同的编号，如果续签多次，这个编号是最新的合同编号 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户盖章时间 */
  custSealDt = '';

  /** 供应商ID */
  departmentId = '';

  /** 供应商名称 */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** 草稿备注 */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** 预估首次账单日期 */
  estimateFirstBillDate = '';

  /** 预计12个月内可达到人数 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 预计增长人数 */
  expectedIncrease = '';

  /** 原预计增长人数 */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** 合同审批的首个法务 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务名称 */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /**  合同审批的首个易薪税审批人员 */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区名称 */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司名称 */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** 未来商机 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** 现销售所属大区名称 */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 现销售所属分公司名称 */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** 导入文件ID */
  importFileId = '';

  /** 导入文件名称 */
  importFileName = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** 开票金额 */
  invoiceMoney = '';

  /** 开票张数 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？ */
  isAdjustRenewContract = '';

  /** 是否派单 */
  isAssign = '';

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** 是否降价、垫付、账期延期 1:是,0:否 */
  isDefer = '';

  /** 是否降价、垫付、账期延期名称 */
  isDeferName = '';

  /** isDeleted */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** 是否代发薪资 */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** 是否二次开发 1:是,0:否 */
  isSecondaryDev = '';

  /** 是否二次开发名称 */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 法务审批 */
  legalApproval = '';

  /** 法务审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  legalApprovalStatus = '';

  /** 合同附件备注 */
  legalRemark = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** 备注 */
  memo = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** 续签合同ID, 存放续签的大合同ID */
  nextContractId = '';

  /** 续签合同名称 */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** 非标合同审批单 */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 父合同id编号 */
  parentContractId = '';

  /** 付款和收款要点 */
  payCollectPoint = '';

  /** 薪资发放月 */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** 垫款额度 */
  prepayAmt = '';

  /** 垫付审批 */
  prepayApproval = '';

  /** 垫款审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  prepayApprovalStatus = '';

  /** 流程定义id */
  processDefId = '';

  /** 对应的流程实例ID */
  processInstanceId = '';

  /** 产品线id集合 */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** 项目前期计划或实施要求 */
  projectPlanRequest = '';

  /** 全国项目交接表单 */
  projectRemark = '';

  /** 供应商类型 */
  providerType = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 报价单集合id */
  quoIds = '';

  /** 被续签的旧合同号 */
  renewedContractNum = '';

  /** 客服反馈撤单预警日期大于等于 */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** 客服反馈撤单预警日期小于等于 */
  reportGlEvacuatedDate = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增/存量标识 （手工） */
  salFlagManual = '';

  /** 1 纯新增,2 存量,3 纯新增/存量,4 纯新增+滚动存量,5 滚动存量,6 滚动存量+存量 */
  salFlagManualName = '';

  /** 新增/存量标识 （系统）：1历史纯新增、2滚动新增、3存量、4当月启动纯新增、-1未有首版账单 */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** 销售审批 */
  salesApprove = '';

  /** 销售报价审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** 用章审核状态 0：初始态3：通过 -3：退回修改 -4：驳回终止 */
  sealApproveStatus = '';

  /** 公司盖章时间 */
  sealDt = '';

  /** 用章审核 */
  sealOpinion = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** 撤单原因 */
  stopReason = '';

  /** 终止服务系统操作时间 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** 服务区域 */
  svcRegion = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** 交接单流程ID */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** 销售--客服交接单 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 交接上传文件名 */
  uploadFileName = '';

  /** 交接上传URL */
  uploadUrl = '';

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** 工作流id */
  workitemId = '';
}

class ContractFile {
  /** 审批节点 */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同附件ID */
  contractFileId = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 附件ID */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 附件路径 */
  filePath = '';

  /** 附件类型 */
  fileType = '';

  /** 附件类型name */
  fileTypeName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程defId */
  processDefId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传步骤 */
  uploadStep = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractQuery {
  /** 流程节点ID */
  activityDefId = '';

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 审批通过日期到 */
  approveDtEnd = '';

  /** 审批通过日期从 */
  approveDtStart = '';

  /** 区域类型 */
  areaType = '';

  /** 合同类别 */
  contractCategery = '';

  /** 合同编号 */
  contractCode = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同名称 */
  contractName = '';

  /** 开始时间到 */
  contractStartDateEnd = '';

  /** 开始时间从 */
  contractStartDateStart = '';

  /** 合同审批状态 */
  contractStatus = '';

  /** 合同小类 */
  contractSubType = '';

  /** 合同状态 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** 合同大类 */
  contractType = '';

  /** 合同类别 */
  contractVersionType = '';

  /** 创建人 */
  createByName = '';

  /** 创建人查询条件 */
  createByParty = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 现销售 */
  currentSales = '';

  /** 客户编号 */
  custCode = '';

  /** 客户名称 */
  custName = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售 */
  formerSales = '';

  /** 现销售所属大区 */
  governingArea = '';

  /** 现销售所属分公司 */
  governingBranch = '';

  /** 是否为已有客户所推荐 */
  isCustRecommend = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** processDefId */
  processDefId = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 客服反馈撤单预警日期大于等于 */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单预警日期小于等于 */
  reportGlEvacuatedDate = '';

  /** 卡权限(1：客户权限，3：订单权限也就是小合同权限) */
  restrictType = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** 签约方公司抬头 */
  signBranchTitleId = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 终止服务日期从 */
  stopSvcDt = '';

  /** 终止服务日期到 */
  stopSvcEndDt = '';

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];
}

class ContractRetiree {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 人员姓名 */
  bz = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 人员姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 身份证号 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 大合同退休人员主键 */
  retireeId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractVistMemoDTO {
  /** 大合同ID号 */
  contractId = '';

  /** 本次填写的回访内容 */
  currReturnMemo = '';

  /** 填写时间戳 */
  currTimeStamp = '';

  /** 本次填写人ID */
  currVistorID = '';

  /** 本次填写人名 */
  currVistorName = '';

  /** 最后一次记录回访人ID */
  lastVistorId = '';

  /** 回访内容历史记录 */
  returnVisitMemoHis = '';
}

class CustArchives {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户档案id */
  custArchivesId = '';

  /** 客户编号 */
  custCode = '';

  /**  客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /**  文件名称 */
  fileName = '';

  /**  文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /**  备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustArchivesShare {
  /** add */
  add = false;

  /** 主键 */
  archivesShareId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户档案id */
  custArchivesId = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 集团id */
  groupId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustAttDTO {
  /** 附件类型，1文件2图标 */
  attType = '';

  /** 客户id */
  custId = '';

  /** 附件id */
  fileId = '';

  /** isDeleted */
  isDeleted = '';

  /** 备注 */
  remark = '';
}

class CustMaterialDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 客户材料id */
  custMaterialId = undefined;

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 材料id */
  materialId = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustMaterialQuery {
  /** 客户id */
  custId = '';

  /** 文件名 */
  fileName = '';

  /** 备注 */
  remark = '';
}

class CustPlayerAssertQuery {
  /** 审批状态 */
  approveStatus = '';

  /** 提交审批人员来源：1数据中心、2客服  */
  approverSource = '';

  /** 发票抬头 */
  checkTitle = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户中文名称 */
  custName = '';

  /** 付款方编号 */
  custPayerId = '';

  /** endIndex */
  endIndex = undefined;

  /** 所属大区 */
  governingArea = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 删除状态 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 付款方名称 */
  payerName = '';

  /** startIndex */
  startIndex = undefined;

  /** 提交审批人员 */
  subApprover = '';
}

class CustPlayerSalaryQuery {
  /** 审批状态 */
  approveStatus = '';

  /** 薪资预订编号 */
  bdCustPayerSalaryId = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户中文名称 */
  custName = '';

  /** 薪资预订标识 */
  disburseWageFlag = '';

  /** endIndex */
  endIndex = undefined;

  /** 删除状态 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class CustQuery {
  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户规模 */
  customerSize = '';

  /** 分公司id */
  departmentId = '';

  /** endIndex */
  endIndex = undefined;

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 删除状态 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 大区id */
  regionId = '';

  /** 销售姓名 */
  salesName = '';

  /** startIndex */
  startIndex = undefined;
}

class CustReportShare {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 集团id */
  groupId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 服务月报id */
  reportAttachmentId = '';

  /** 共享表id */
  reportShareId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustShareQuery {
  /** 所属客户id */
  belongCustId = '';

  /** 必传客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** 集团id */
  groupId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 选中的记录id */
  relationId = '';

  /** startIndex */
  startIndex = undefined;

  /** 上传时间小于等于 */
  uploadDtEnd = '';

  /** 上传时间大于等于 */
  uploadDtStart = '';
}

class CustUseRecApproveQuery {
  /** add */
  add = false;

  /** 流程审批状态  0:待提交 1:待审批  2:审批通过 -1:驳回 -2：终止 */
  approveStatus = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = undefined;

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否纳入统计 0:是 1:否 */
  isCount = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 责任客服id */
  liabilityCs = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 参与人 */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程定义id */
  processDefId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备案所属月 */
  recDt = '';

  /** 备案id */
  recId = undefined;

  /** 备案类型 0:长期 1:按月 */
  recType = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class Customer {
  /** add */
  add = false;

  /** 客户档案最大上传时间 */
  archivesMaxDt = '';

  /** 银行账号 */
  bankAcct = '';

  /** 开户行名称 */
  bankName = '';

  /** 银行行号 */
  bankNum = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 大客户申请人 */
  bigCompanyApplyMan = '';

  /** 大客户申请时间 */
  bigCompanyApplyString = '';

  /** 大客户审批意见 */
  bigCompanyAudit = '';

  /** 大客户审核人 */
  bigCompanyAuditMan = '';

  /** 大客户审核时间 */
  bigCompanyAuditString = '';

  /** 大客户处理原由 */
  bigCompanyCause = '';

  /** 大客户处理流程实例id */
  bigCompanyProcessId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务结束时间 */
  bizEndDt = '';

  /** 业务范围 */
  bizScope = '';

  /** 业务开始时间 */
  bizStartDt = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 渠道ID */
  channelId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 公司所在城市编码 */
  companyCityId = '';

  /** 公司所在城市 */
  companyCityName = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人手机2 */
  contactCell2 = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 核心业务需求 */
  coreBizReqId = '';

  /** 核心业务需求 */
  coreBizReqName = '';

  /** 钉钉客户编号 */
  corpId = '';

  /** 国家ID */
  countryId = '';

  /** 国家Name */
  countryName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户资信ID */
  creditHisId = '';

  /** 客户资信Name */
  creditHisName = '';

  /** 客户办公地址所在区县ID （前期可不用） */
  csOfficeCountyId = '';

  /** 客户办公地址所在区县Name */
  csOfficeCountyName = '';

  /** 客户所在城市 */
  custCityName = '';

  /** 客户编码 */
  custCode = '';

  /** 客户英文名称 */
  custEnglishName = '';

  /** 客户id */
  custId = '';

  /** 客户中文名称 */
  custName = '';

  /** 缴费实体统计 */
  custPayEntityCount = '';

  /** 客户缩写名称 */
  custShortName = '';

  /** 客户规模 */
  custSize = '';

  /** 新增从陌生拜访转移 */
  custTransferStatus = '';

  /** 客户类型:0 直销 1 渠道 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 分公司id */
  departmentId = '';

  /** 企业性质ID */
  ePropertyId = '';

  /** 企业性质Name */
  ePropertyName = '';

  /** 企业规模ID */
  eSizeId = '';

  /** 企业规模Name */
  eSizeName = '';

  /** 联系人邮件 */
  email = '';

  /** 联系人邮件2 */
  email2 = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 联系人传真 */
  fax = '';

  /** 联系人传真2 */
  fax2 = '';

  /** 附件id */
  fileId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 如果与company_id相同，表示自己是同一个集团，如果是不一样的，表示是某个集团下的一个成员，程序需要控制只有两层关系 */
  groupId = '';

  /** 集团名称 */
  groupName = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** 导入批次号 */
  impBatchId = '';

  /** inId */
  inId = '';

  /** 所属行业ID */
  industryId = '';

  /** 所属行业Name */
  industryName = '';

  /** 保险联系人 */
  insuranceContact = '';

  /** 保险联系电话 */
  insuranceContactTel = '';

  /** 情况简介 */
  introduction = '';

  /** 是否是大公司 */
  isBigCompany = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否签约合同 */
  isContract = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否删除文本 */
  isDeletedText = '';

  /** 是否财务专用_SAMI */
  isFinnace = '';

  /** 是否关联客户 */
  isInternalCust = '';

  /** 是否关联客户text */
  isInternalCustText = '';

  /** 是否上市ID */
  isPublicTradedId = '';

  /** 是否上市Name */
  isPublicTradedName = '';

  /** 1:正在跟进,0:删除区2:共享区  */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 法人代表 */
  legalRep = '';

  /** 材料最后添加时间 */
  materialMaxDt = '';

  /** 备注 */
  memo = '';

  /** 会议最后上传时间 */
  mettingMaxDt = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 组织机构代码 */
  organizationCode = '';

  /** 是否上市Name */
  organizationRemark = '';

  /** 是否外包,1是,0否 */
  outSvcProviderId = '';

  /** 是否外包名称 */
  outSvcProviderName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 支付明细类型 */
  payDetailType = '';

  /** 政策大全类型 */
  policyType = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 来自陌生拜访客户id */
  prospectiveCustId = '';

  /** 联系人1职位 */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 注册地址 */
  regAddress = '';

  /** 注册资金（万） */
  regCapital = '';

  /** 注册到期日期 */
  regEndDt = '';

  /** 注册开始日期 */
  regStartDt = '';

  /** 大区 */
  regionId = '';

  /** 相关业务需求 */
  relaReqId = '';

  /** 相关业务需求 */
  relaReqName = '';

  /** 服务月报最大上传时间 */
  reportMaxDt = '';

  /** 获得需求时间 */
  reqAquirementDt = '';

  /** 需求获得方式ID */
  reqAquirementTypeId = '';

  /** 需求获得方式Name */
  reqAquirementTypeName = '';

  /** 需求备注 */
  reqRemark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 销售人员所在城市id */
  salesCityId = '';

  /** 销售人员所在城市 */
  salesCityName = '';

  /** 所属销售人员 */
  salesCode = '';

  /** 销售组 */
  salesGroup = '';

  /** 销售id */
  salesId = '';

  /** 销售人员名字 */
  salesName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 客户来源ID */
  sourceId = '';

  /** 客户来源Name */
  sourceName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 已经恢复次数 */
  timesOfRecovery = '';

  /** 转移时间 */
  transTime = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 用户角色id */
  userRoleId = '';

  /** 用户角色类型 */
  userRoleType = '';

  /** 访问状态 */
  visitStatus = '';

  /** 薪资计算方式类型 */
  wageCalculateType = '';

  /** 薪资计算方式文本 */
  wageCalculateTypeText = '';

  /** 网址 */
  website = '';

  /** 办公地址 */
  workAddress = '';

  /** 邮政编码（办公地址） */
  zipCode = '';
}

class CustomerListDTO {
  /** 新增list */
  addList = [];

  /** 更新list */
  uptList = [];
}

class CustomerPayer {
  /** accountName */
  accountName = '';

  /** add */
  add = false;

  /** approveDt */
  approveDt = '';

  /** approveStatus */
  approveStatus = '';

  /** approver */
  approver = '';

  /** 审批人员（显示） */
  approverName = '';

  /** 提交审批人员来源：1数据中心、2客服  */
  approverSource = '';

  /** 提交审批人员来源（显示） */
  approverSourceName = '';

  /** bankAcct */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** bankType */
  bankType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** billMon */
  billMon = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** checkTitle */
  checkTitle = '';

  /** cityId */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** contact */
  contact = '';

  /** contactAddress */
  contactAddress = '';

  /** contactEmail */
  contactEmail = '';

  /** contactFax */
  contactFax = '';

  /** contactTel1 */
  contactTel1 = '';

  /** contactTel2 */
  contactTel2 = '';

  /** contactZipCode */
  contactZipCode = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custAptitude */
  custAptitude = '';

  /** custAptitudeText */
  custAptitudeText = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** custPayerId */
  custPayerId = '';

  /** 客户付款方收件表id */
  custPayerRecId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 快递公司 */
  expCompany = '';

  /** 快递单号 */
  expNo = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 附件id */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 所属大区 */
  governingArea = '';

  /** governingAreaName */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 所属分公司名字 */
  governingBranchName = '';

  /** inId */
  inId = '';

  /** invoiceAddress */
  invoiceAddress = '';

  /** invoiceDesc */
  invoiceDesc = '';

  /** 电子发票邮箱 */
  invoiceEmail = '';

  /** 开票收件信息ID */
  invoiceRecId = '';

  /** invoiceTel */
  invoiceTel = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否默认 0 是 1 否 */
  isDefault = '';

  /** 是否默认 0 是 1 否 */
  isDefaultCN = '';

  /** isDeleted */
  isDeleted = '';

  /** isInvoice */
  isInvoice = '';

  /** 是否同步: 0未同步 1已同步 */
  isSync = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 寄件类型 1 顺丰快递 2 自行邮寄  */
  mailType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** payerName */
  payerName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** provinceId */
  provinceId = '';

  /** proxyBy */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 收件人详细地址 */
  receiveAddress = '';

  /** 收件人市 */
  receiveCity = '';

  /** 收件人市 */
  receiveCityCN = '';

  /** 收件公司 */
  receiveCompany = '';

  /** 收件人区 */
  receiveCounty = '';

  /** 收件人姓名 */
  receiveName = '';

  /** 收件人省 */
  receiveProvince = '';

  /** 收件人省 */
  receiveProvinceCN = '';

  /** 收件人电话 */
  receiveTel = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 提交审批人员 */
  subApprover = '';

  /** 提交审批人员（显示） */
  subApproverName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** taxpayerIdentifier */
  taxpayerIdentifier = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustomerPayerDTO {
  /** 开户名 */
  accountName = '';

  /** 审核日期 */
  approveDt = '';

  /** 审核状态:0待修改,1待审批,2审批通过 */
  approveStatus = '';

  /** 审核人 */
  approver = '';

  /** 帐号 */
  bankAcct = '';

  /** 开户行名称 */
  bankName = '';

  /** 1：工行  0：他行 */
  bankType = '';

  /** 帐单年月 0 收当月 -1收上月 */
  billMon = '';

  /** 发票抬头 */
  checkTitle = '';

  /** 城市id */
  cityId = '';

  /** 联系人 */
  contact = '';

  /** 联系地址 */
  contactAddress = '';

  /** 电子邮件 */
  contactEmail = '';

  /** 传真 */
  contactFax = '';

  /** 联系电话 */
  contactTel1 = '';

  /** 联系电话 */
  contactTel2 = '';

  /** 邮政编码 */
  contactZipCode = '';

  /** 客户资质:1,一般纳税人、2小规模纳税人 */
  custAptitude = '';

  /** 客户资质Name */
  custAptitudeText = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户付款方表id */
  custPayerId = '';

  /** 附件id */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 发票地址 */
  invoiceAddress = '';

  /** 特殊开发票说明 */
  invoiceDesc = '';

  /** 电子发票邮箱 */
  invoiceEmail = '';

  /** 发票电话 */
  invoiceTel = '';

  /** 删除标志 */
  isDeleted = '';

  /** 是否开特殊发票0: 否1:是 */
  isInvoice = '';

  /** 付款方名称 */
  payerName = '';

  /** 省份id */
  provinceId = '';

  /** 代理人 */
  proxyBy = '';

  /** 备注 */
  remark = '';

  /** 纳税人识别号 */
  taxpayerIdentifier = '';
}

class CustomerPayerInterDTO {
  /** 联系人 */
  contact = '';

  /** 付款方地址 */
  contactAddress = '';

  /** 客户付款方ID */
  custPayerId = '';

  /** id */
  id = '';

  /** 语种 */
  languageType = '';

  /** 客户付款方名称 */
  payeName = '';
}

class CustomerPayerSJDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 开票日期 */
  createDate = '';

  /** 创建日期 */
  createDt = '';

  /** 开票起始日小于等于 */
  createDtEd = '';

  /** 开票起始日小于等于 */
  createDtSt = '';

  /** 快递单号 */
  custId = '';

  /** 客户付款方表id */
  custPayerId = '';

  /** 客户付款方收件表id */
  custPayerRecId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 快递公司 */
  expCompany = '';

  /** 快递单号 */
  expNo = '';

  /** 长度  */
  expOrder = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 开票ID */
  invoiceId = '';

  /** 开票收件信息ID */
  invoiceRecId = '';

  /** 是否默认 0 是 1 否 */
  invoiceURL = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否默认 0 是 1 否 */
  isDefault = '';

  /** 是否默认 0 是 1 否 */
  isDefaultCN = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 长度  */
  length = undefined;

  /** 运单号  */
  mailNo = '';

  /**  寄件类型 1 顺丰快递 2 自行邮寄 */
  mailType = '';

  /** 模拟人 */
  mimicBy = '';

  /** 手机号后四位  */
  mobileNo = '';

  /** noChange */
  noChange = false;

  /** 揽件时间 */
  packAgeDt = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 收件人详细地址 */
  receiveAddress = '';

  /** 收件人市 */
  receiveCity = '';

  /** 收件人市 */
  receiveCityCN = '';

  /** 收件公司 */
  receiveCompany = '';

  /** 收件人区 */
  receiveCounty = '';

  /** 收件人姓名 */
  receiveName = '';

  /** 收件人省 */
  receiveProvince = '';

  /** 收件人省 */
  receiveProvinceCN = '';

  /** 收件人电话 */
  receiveTel = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sendAddress */
  sendAddress = '';

  /** sendMobile */
  sendMobile = '';

  /** sendName */
  sendName = '';

  /** sendTel */
  sendTel = '';

  /** 长度  */
  sfMsg = '';

  /** 开始页  */
  start = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 类型 1 寄件运单, 2 , 外部顺丰运单 */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustomerPayerSalary {
  /** add */
  add = false;

  /** agreedBillConfirmDay */
  agreedBillConfirmDay = '';

  /** agreedBillGenerateDay */
  agreedBillGenerateDay = '';

  /** agreedDataSubmitDay */
  agreedDataSubmitDay = '';

  /** agreedDisburseDay */
  agreedDisburseDay = '';

  /** agreedDisburseMon */
  agreedDisburseMon = '';

  /** agreedPayDay */
  agreedPayDay = '';

  /** approveDt */
  approveDt = '';

  /** approveStatus */
  approveStatus = '';

  /** approver */
  approver = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** bdCustPayerSalaryId */
  bdCustPayerSalaryId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** disburseCalculation */
  disburseCalculation = '';

  /** disburseWageFlag */
  disburseWageFlag = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDeleted */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** proxyBy */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustomerPayerSalaryDTO {
  /**  约定账单锁定日 */
  agreedBillConfirmDay = '';

  /** 约定账单生成日 */
  agreedBillGenerateDay = '';

  /** 约定数据提交日 */
  agreedDataSubmitDay = '';

  /** 约定客户打款日 */
  agreedDisburseDay = '';

  /** 约定客户打款所属月 */
  agreedDisburseMon = '';

  /** 约定发放日 */
  agreedPayDay = '';

  /** 审批日期 */
  approveDt = '';

  /** 状态 */
  approveStatus = '';

  /**  审批状态 0 待修改  1 待审批 2 审批通过 */
  approver = '';

  /** 薪资相关约定ID */
  bdCustPayerSalaryId = '';

  /** 客户编号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 发放 0 当月发放当月计算 1 当月发放上月计算 2 当月发放上上月计算 */
  disburseCalculation = '';

  /** 薪资预订标识 */
  disburseWageFlag = '';

  /** 是否有效 0 有效 1 无效 */
  isDeleted = '';

  /** 代理人 */
  proxyBy = '';
}

class CustomerPayrollDTO {
  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 客户编码 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 生效人 */
  effectBy = '';

  /** 生效日期 */
  effectDt = '';

  /** 逻辑删除标识 0：正常 1：已删除  默认值为0 */
  isDeleted = '';

  /** 记录类型 */
  recordType = '';

  /** 工资单发送列表ID */
  sendCustId = '';

  /** 状态,0：未生效，1：生效，2：无效 */
  status = '';

  /** 状态说明 */
  statusName = '';
}

class CustomerRecordDTO {
  /** 创建结束时间 */
  createDtEnd = '';

  /** 创建开始时间 */
  createDtStart = '';

  /** 客户编码 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 生效人 */
  effectBy = '';

  /** 生效时间 */
  effectDt = '';

  /** id */
  id = '';

  /** recordType */
  recordType = '';

  /** 状态 */
  status = '';

  /** 状态描述 */
  statusName = '';
}

class CustomerRecordQuery {
  /** 创建结束日期 */
  createDtEnd = '';

  /** 创建开始日期 */
  createDtStart = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户中文名称 */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class EosUser {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** eos账号id */
  eosUserId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 手机号码 */
  phoneNumber = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 是否需要认证 */
  requiredCertification = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class FilterEntity {
  /** activityNameCn */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByStr */
  createByStr = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** disaBatchId */
  disaBatchId = '';

  /** disaReasonId */
  disaReasonId = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** reasonBz */
  reasonBz = '';

  /** reasonId */
  reasonId = '';

  /** reasonStr */
  reasonStr = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workitemId */
  workitemId = '';
}

class Group {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** createEmp */
  createEmp = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 下属客户数量 */
  custNum = '';

  /** 单立户缴费实体count */
  custPayEntityCount = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** 客户规模text */
  customerSizeText = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** groupId */
  groupId = '';

  /** groupName */
  groupName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class GroupCompanyQuery {
  /** 创建人 */
  createBy = '';

  /** 创建时间大于等于 */
  createTimeGteq = '';

  /** 创建时间小于等于 */
  createTimeLteq = '';

  /** 客户编号 */
  custCode = '';

  /** 客户规模 */
  customerSize = '';

  /** endIndex */
  endIndex = undefined;

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class ImpRuleQuery {
  /** 调基任务号 */
  adjTaskId = '';

  /** 基础数据编码 */
  baseDataCode = '';

  /** 批次id */
  batchId = '';

  /** 列名 */
  columns = '';

  /** 薪资批次id */
  currentRecordId = '';

  /** 客户id */
  custId = '';

  /** 结束时间 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误类型 */
  errorType = '';

  /** 导入结果 0：成功 1：失败 */
  impTag = '';

  /** 导入人姓名 */
  impUserName = '';

  /** 是否有效 */
  isEffective = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 导入批次中做详细筛选的id值 */
  recordId = '';

  /** 规则id */
  ruleId = '';

  /** 规则名称 */
  ruleName = '';

  /** 前端传入的参数，判断不同的情况查询语句 */
  selByAuth = '';

  /** 开始时间 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 表名 */
  tableName = '';

  /** 类型id */
  typeId = '';

  /** 薪资类别ID */
  wageClassId = '';
}

class InsuranceProvider {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 主键 */
  biProviderId = '';

  /** 供应商名称 */
  biProviderName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 联系人 */
  contactBy = '';

  /** 手机 */
  contactPhone = '';

  /** 联系电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 描述 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class InsuranceProviderDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 主键 */
  biProviderId = '';

  /** 供应商名称 */
  biProviderName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 联系人 */
  contactBy = '';

  /** 手机 */
  contactPhone = '';

  /** 联系电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 描述 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class InsuranceType {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 险种编号 */
  biTypeCode = '';

  /** 主键 */
  biTypeId = '';

  /** 险种名称 */
  biTypeName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 生效时间 */
  effectiveDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 保额 */
  insuredSum = '';

  /** 失效时间 */
  invalidDt = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否含子女 */
  isChildren = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否含配偶 */
  isSpouse = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 商保产品 */
  productId = '';

  /** 供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 子产品id */
  subProductId = '';

  /** 子产品name */
  subProductName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class InsuranceTypeDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 险种编号 */
  biTypeCode = '';

  /** 主键 */
  biTypeId = '';

  /** 险种名称 */
  biTypeName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 生效时间 */
  effectiveDt = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 保额 */
  insuredSum = '';

  /** 失效时间 */
  invalidDt = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否含子女 */
  isChildren = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否含配偶 */
  isSpouse = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 商保产品 */
  productId = '';

  /** 供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 状态 */
  status = '';

  /** 子产品id */
  subProductId = '';

  /** 子产品name */
  subProductName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class InsuranceTypeQuery {
  /** 险种编号 */
  biTypeCode = '';

  /** 险种名称 */
  biTypeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品id */
  productId = '';

  /** 供应商 */
  providerId = '';

  /** startIndex */
  startIndex = undefined;

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  status = '';

  /** 子产品 */
  subProductId = '';
}

class MarketActivityInfoDTO {
  /** 举办城市区域 */
  activityArea = '';

  /** 主键 */
  activityId = '';

  /** 市场活动名称 */
  activityName = '';

  /** 市场活动编号 */
  activityNo = '';

  /** 城市名称 */
  cityName = '';

  /** 结束日期 */
  endDate = '';

  /** 电脑端内容 */
  pcContent = '';

  /** 手机端内容 */
  phoneContent = '';

  /** 手机端完整URL */
  phoneContentRealurl = '';

  /** 手机端简要URL */
  phoneContentSimpleurl = '';

  /** 说明 */
  remark = '';

  /** 开始日期 */
  startDate = '';

  /** status */
  status = '';
}

class MarketActivityInfoQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class MaterialQuery {
  /** endIndex */
  endIndex = undefined;

  /** 材料名 */
  materialName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class MonthlyReportDTO {
  /** 客户id */
  custId = '';

  /** 附件id */
  fileId = '';

  /** isDeleted */
  isDeleted = '';

  /** 备注 */
  remark = '';

  /** 服务年月 */
  serviceMon = '';

  /** status */
  status = '';
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class ProductDTO {
  /** add */
  add = false;

  /** addTemplateAreasList */
  addTemplateAreasList = [];

  /** addTemplateInfectList */
  addTemplateInfectList = [];

  /** 审批价格 */
  approvePrice = '';

  /** atr */
  atr = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 提供给公用查询产品界面使用,必须传城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 详述 */
  detail = '';

  /** 生效日期 */
  effectiveDt = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否支持城市系数打折 */
  ifCityDiscount = '';

  /** 是否支持城市系数打折名称 */
  ifCityDiscountName = '';

  /** inId */
  inId = '';

  /** 失效日期 */
  invalidDt = '';

  /** 是否代办 */
  isAgency = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** 是否按天计算 */
  isCalculateByDay = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否删除. 0 否, 1 已删除，默认值为0 */
  isDeleted = '';

  /** 是否存在有效日期 */
  isEffectDt = '';

  /** 是否存在有效期限名称 */
  isEffectDtName = '';

  /** 是否费用返还产品 */
  isFundReturn = '';

  /** 是否服务人数限制 */
  isLimitSvcNum = '';

  /** 是否服务人数限制名称 */
  isLimitSvcNumName = '';

  /** 是否可补收 */
  isMakeupCollect = '';

  /** 是否一次性付款 */
  isOneTimePay = '';

  /** 非社保公积金类产品并且是一次性产品或者代收代付产品 */
  isOneTimePayAndAgency = '';

  /** 是否区域限制 */
  isRegionRestrict = '';

  /** 是否区域限制名称 */
  isRegionRestrictName = '';

  /** 是否可以报销 */
  isReimbursement = '';

  /** 是否可退费 */
  isReturns = '';

  /** 是否虚产品 */
  isVirtual = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 最大服务人数 */
  maxSvcHeadcount = '';

  /** 模拟人 */
  mimicBy = '';

  /** 最小成本 */
  minCost = '';

  /** 最小服务人数 */
  minSvcHeadcount = '';

  /** 报岗时可修改 */
  modifiableAtRptSep = '';

  /** newSaleId */
  newSaleId = '';

  /** noChange */
  noChange = false;

  /** start */
  pageNum = undefined;

  /** limit */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品大类 */
  productCategory = '';

  /** 产品大类名称 */
  productCategoryName = '';

  /** 产品编号 */
  productCode = '';

  /** productCost */
  productCost = '';

  /** productDesc */
  productDesc = '';

  /** 财务产品大类 */
  productFinanceCategory = '';

  /** 财务产品大类名称 */
  productFinanceCategoryName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLineId = '';

  /** 产品线名称 */
  productLineName = '';

  /** 服务产品名称 */
  productName = '';

  /** productPrice */
  productPrice = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 查询类型 用于区别报价单模板和其他查询产品的条件 */
  queryType = '';

  /** 产品比例细想id */
  quotationId = '';

  /** 产品比例细想id */
  quotationItemId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salesPrice */
  salesPrice = '';

  /** salesPriceNoTax */
  salesPriceNoTax = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** 提供给公用查询界面使用,必须传城市id,次属性为模糊查找 */
  ssGroupName = '';

  /** 标准价格 */
  standardPrice = '';

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  status = '';

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** suppltMedInsurHeadcount */
  suppltMedInsurHeadcount = '';

  /** upTemplateAreasList */
  upTemplateAreasList = [];

  /** upTemplateInfectList */
  upTemplateInfectList = [];

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = '';

  /** vatr */
  vatr = '';
}

class ProductData {
  /** add */
  add = false;

  /** 审批价格 */
  approvePrice = '';

  /** atr */
  atr = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 提供给公用查询产品界面使用,必须传城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 详述 */
  detail = '';

  /** 生效日期 */
  effectiveDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否支持城市系数打折 */
  ifCityDiscount = '';

  /** 是否支持城市系数打折名称 */
  ifCityDiscountName = '';

  /** inId */
  inId = '';

  /** 失效日期 */
  invalidDt = '';

  /** 是否代办 */
  isAgency = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** 是否按天计算 */
  isCalculateByDay = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否删除. 0 否, 1 已删除，默认值为0 */
  isDeleted = '';

  /** 是否存在有效日期 */
  isEffectDt = '';

  /** 是否存在有效期限名称 */
  isEffectDtName = '';

  /** 是否费用返还产品 */
  isFundReturn = '';

  /** 是否服务人数限制 */
  isLimitSvcNum = '';

  /** 是否服务人数限制名称 */
  isLimitSvcNumName = '';

  /** 是否可补收 */
  isMakeupCollect = '';

  /** 是否一次性付款 */
  isOneTimePay = '';

  /** 非社保公积金类产品并且是一次性产品或者代收代付产品 */
  isOneTimePayAndAgency = '';

  /** 是否区域限制 */
  isRegionRestrict = '';

  /** 是否区域限制名称 */
  isRegionRestrictName = '';

  /** 是否可以报销 */
  isReimbursement = '';

  /** 是否可退费 */
  isReturns = '';

  /** 是否虚产品 */
  isVirtual = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 最大服务人数 */
  maxSvcHeadcount = '';

  /** 模拟人 */
  mimicBy = '';

  /** 最小成本 */
  minCost = '';

  /** 最小服务人数 */
  minSvcHeadcount = '';

  /** 报岗时可修改 */
  modifiableAtRptSep = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品大类 */
  productCategory = '';

  /** 产品大类名称 */
  productCategoryName = '';

  /** 产品编号 */
  productCode = '';

  /** productCost */
  productCost = '';

  /** productDesc */
  productDesc = '';

  /** 财务产品大类 */
  productFinanceCategory = '';

  /** 财务产品大类名称 */
  productFinanceCategoryName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLineId = '';

  /** 产品线名称 */
  productLineName = '';

  /** 服务产品名称 */
  productName = '';

  /** productPrice */
  productPrice = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 查询类型 用于区别报价单模板和其他查询产品的条件 */
  queryType = '';

  /** 产品比例细想id */
  quotationId = '';

  /** 产品比例细想id */
  quotationItemId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salesPrice */
  salesPrice = '';

  /** salesPriceNoTax */
  salesPriceNoTax = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** 提供给公用查询界面使用,必须传城市id,次属性为模糊查找 */
  ssGroupName = '';

  /** 标准价格 */
  standardPrice = '';

  /** startIndex */
  startIndex = undefined;

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  status = '';

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = '';

  /** vatr */
  vatr = '';

  /** virtualProductId */
  virtualProductId = '';
}

class ProductInterDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** id */
  id = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 语种 */
  languageType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品ID */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ProductTypeDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品大类 */
  productCategory = '';

  /** 产品类型编号 */
  productTyCode = '';

  /** 产品类型 */
  productType = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ProjectMembers {
  /** 公司地址 */
  address = '';

  /** 联系电话 */
  contactTel = '';

  /** custId */
  custId = '';

  /** 邮箱地址 */
  eMail = '';

  /** 接单城市 */
  jdfCityName = '';

  /** 接单方分公司 */
  jdfName = '';

  /** jdkfEmpId */
  jdkfEmpId = '';

  /** 接单客服姓名 */
  jdkfName = '';
}

class QuoTemplTrdCost {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市 */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 第3方外部供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 第3方外部供应商name */
  providerName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单方案第三方成本维护id */
  quotationTemplTrdCost = '';

  /** 报价单方案id */
  quotationTemplateId = '';

  /** 报价单方案name */
  quotationTemplateName = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QuotationItem {
  /** add */
  add = false;

  /** approvePrice */
  approvePrice = undefined;

  /** 附加税 */
  at = undefined;

  /** 附加税的税率% */
  ator = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 预计数量 */
  countNum = undefined;

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 毛利率 */
  gmr = undefined;

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.IS_ORDER_UPDATE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isOrderUpdate = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** isYearsToPay */
  isYearsToPay = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 标准售价 */
  priceAt = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productCost = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productPrice = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationGroupItemId */
  quotationGroupItemId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ITEM_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationItemId = undefined;

  /** quotationItemType */
  quotationItemType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 标准售价（含附加税） */
  salesPrice = undefined;

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 总成本 */
  totalCost = undefined;

  /** 总售价 */
  totalPrice = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;

  /** virtualProductId */
  virtualProductId = undefined;
}

class QuotationItemDetail {
  /** add */
  add = false;

  /** approvePrice */
  approvePrice = undefined;

  /** 附加税 */
  at = undefined;

  /** 附加税的税率% */
  ator = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 预计数量 */
  countNum = undefined;

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.QUOTATION_ITEM_DETAIL_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  detailType = undefined;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 毛利率 */
  gmr = undefined;

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.IF_FOR_THIRDPART	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifForThirdpart = undefined;

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.IS_YEARS_TO_PAY	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isYearsToPay = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 标准售价 */
  priceAt = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productCost = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_DESC	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productDesc = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_LINE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productLineId = undefined;

  /** productLineName */
  productLineName = '';

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productPrice = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationGroupItemDetailId */
  quotationGroupItemDetailId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** quotationItemDetailId */
  quotationItemDetailId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ITEM_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationItemId = undefined;

  /** quotationItemType */
  quotationItemType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_ITEM_DETAIL.QUOTATION_TEMPLATE_INFECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTemplateInfectId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_ITEM_DETAIL.QUOTATION_TEMPLT_CITY_FACTOR	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTempltCityFactor = undefined;

  /** quotationTempltName */
  quotationTempltName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SALES_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  salesPrice = undefined;

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** subLadder */
  subLadder = [];

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SUPLMT_MED_END_DT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suplmtMedEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SUPLMT_MED_START_DT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suplmtMedStartDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SVC_AREA	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  svcArea = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.TEMPLATE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  templateId = undefined;

  /** templtScope */
  templtScope = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.TEMPLT_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  templtType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.THIRD_PARTY_PROVIDER_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  thirdPartyProviderId = undefined;

  /** thirdPartyProviderName */
  thirdPartyProviderName = '';

  /** 总成本 */
  totalCost = undefined;

  /** 总售价 */
  totalPrice = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;

  /** virtualProductId */
  virtualProductId = undefined;
}

class QuotationLadder {
  /** add */
  add = false;

  /** 附加税 */
  at = undefined;

  /** 附加税的税率% */
  ator = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.BEGIN_NUMBER	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  beginNumber = undefined;

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.END_NUMBER	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  endNumber = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  price = undefined;

  /** 标准售价（含附加税） */
  priceAt = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productId */
  productId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_TEMPLATE_INFECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTemplateInfectId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationType = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;
}

class QuotationSpecial {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.BPO_PROJECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  bpoProjectId = undefined;

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  price = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_SPECIAL_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationType = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QuotationSpecialProperty {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.AMT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  amt = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.CUSTOMIZE_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  customizeType = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_SPECIAL_PROPERTY_HIS	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialPropertyHis = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_SPECIAL_PROPERTY_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialPropertyId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  type = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QuotationTempl {
  /** add */
  add = false;

  /** 审批价格 */
  approvePrice = '';

  /** 区域限制 */
  areaLimit = '';

  /** 区域限制名称 */
  areaLimitName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建者name */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** 客户code */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户name */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 发布人 */
  effectiveBy = '';

  /** 生效日期 */
  effectiveDt = '';

  /** 发布时间 */
  effectiveTime = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 第三方供应商 */
  ifForThirdpart = '';

  /** inId */
  inId = '';

  /** 保险公司id */
  insuranceCompanyId = '';

  /** 失效人 */
  invalidBy = '';

  /** 失效日期 */
  invalidDt = '';

  /** 失效时间 */
  invalidTime = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否支持城市系数打折 */
  isCityDiscount = '';

  /** 删除标记 */
  isDeleted = '';

  /** 服务人数限制 */
  isLimitedBySvcHeadcount = '';

  /** 服务人数限制名称 */
  isLimitedBySvcHeadcountName = '';

  /** 是否发布 */
  isRelease = '';

  /** 页面是否是修改状态 */
  isUpdateStatus = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 最大服务人数 */
  maxSvcHeadCount = '';

  /** 模拟人 */
  mimicBy = '';

  /** 最小服务人数 */
  minSvcHeadCount = '';

  /** newSaleId */
  newSaleId = '';

  /** 职场健康类型 */
  newType = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品线id */
  productLineId = '';

  /** 产品线名称 */
  productLineName = '';

  /** 产品name */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 模板产品线id */
  quotationProductLineId = '';

  /** 专属客户id */
  quotationTemplCfgId = '';

  /** 报价单方案编号 */
  quotationTempltCode = '';

  /** 普通模版的成本 */
  quotationTempltCost = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 报价单方案名字 */
  quotationTempltName = '';

  /** 报价单方案名字（校验唯一） */
  quotationTempltNameEx = '';

  /** 普通模版的标准价格 */
  quotationTempltPrice = '';

  /** 报价单模版的使用范围 */
  quotationTempltScope = '';

  /** 报价单模版的使用范围名称 */
  quotationTempltScopeName = '';

  /** 报价单模版的类型 */
  quotationTempltType = '';

  /** 报价单模版的类型名称 */
  quotationTempltTypeName = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 生效状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 有效期限 */
  validDtType = '';

  /** 有效期限名称 */
  validDtTypeName = '';

  /** 虚产品id */
  virtualProductId = '';
}

class QuotationTemplInsuranceType {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 险种编号 */
  biTypeCode = '';

  /** 险种id */
  biTypeId = '';

  /** 险种名称 */
  biTypeName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 生效时间 */
  effectiveDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 保额 */
  insuredSum = '';

  /** 失效时间 */
  invalidDt = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否含子女 */
  isChildren = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否含配偶 */
  isSpouse = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 标准报价 */
  standardQuotation = '';

  /** startIndex */
  startIndex = undefined;

  /** 子产品id */
  subProductId = '';

  /** 子产品name */
  subProductName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 报价单模板险种id */
  templateInsuranceTypeId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QuotationTemplItem {
  /** add */
  add = false;

  /** 审批价格 */
  approvePrice = '';

  /** 附加税 */
  at = '';

  /** 附加税的税率% */
  ator = '';

  /** 附加税率% */
  atr = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 保额 */
  biAcountRemark = '';

  /** 保司 */
  biCompany = '';

  /** 险种  2001 */
  biType = '';

  /** 险种  2001 */
  biTypeName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateType = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateTypeName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 成本 */
  cost = '';

  /** 数量 */
  countNum = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 预估成本 */
  estCost = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 进项税率% */
  iptr = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 标准售价（含附加税） */
  priceAt = '';

  /** 标准售价（含增值税附加税） */
  priceVat = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品大类 */
  productCategory = '';

  /** 产品大类名称 */
  productCategoryName = '';

  /** 产品id */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 供应商 */
  provider = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 采购物品描述 */
  purchaseRemark = '';

  /** 报价单模板子项id */
  quotationTemplItemId = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 报价单子项的备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 标准报价（不含税） */
  standardQuotation = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 总成本 */
  totalCost = '';

  /** 总售价 */
  totalPrice = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 增值税 */
  vat = '';

  /** 增值税率% */
  vatr = '';
}

class QuotationTemplateAreas {
  /** add */
  add = false;

  /** 大区ID */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市Id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 有效的分公司或办事处 */
  departmentId = '';

  /** 有效的分公司或办事处 名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 0:有效 1：删除 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** area_type=0时存报价单模板号quotation_template_id；area_type=1时存产品号product_id */
  quotationTempltId = '';

  /** 类型 说明 0:模版1:产品 */
  quotationTempltType = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 主键 */
  salesQuotationTemplateAreas = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QuotationTemplateInfect {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 起始年龄 */
  beginNumber = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 截止年龄 */
  endNumber = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 0:有效 1：删除 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 年龄浮动价格 */
  price = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单模板人数折扣设置id */
  quotationTemplateInfectId = '';

  /** 报价单模板信息表id */
  quotationTempltId = '';

  /** 0:普通产品1:普通模版2:补医保雇主责任险家财险模版 */
  quotationTempltType = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ServiceSubType {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** pinYinCode */
  pinYinCode = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** svcSubtypeId */
  svcSubtypeId = undefined;

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeId */
  svcTypeId = undefined;

  /** svcTypeSubCode */
  svcTypeSubCode = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SubProductDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 所属产品id */
  productId = '';

  /** 所属产品name */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 描述 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  status = '';

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  statusName = '';

  /** 子产品id */
  subProductId = '';

  /** 子产品name */
  subProductName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SvcReport {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /**  客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 文件id */
  fileId = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 发布日期 */
  publishDate = '';

  /** 备注 */
  remark = '';

  /**  null */
  reportAttachmentId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 月份 */
  serviceMon = '';

  /** 状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传人 */
  uploadBy = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SvcReportQuery {
  /** 客户id */
  custId = '';

  /** 文件名 */
  fileName = '';

  /** 备注 */
  remark = '';

  /** 服务月份 */
  serviceMon = '';
}

class WithholdAgentCustNoCheck {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建日期到 */
  createDtEt = '';

  /** 创建日期从 */
  createDtSt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 主键id */
  id = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态名称 */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 扣缴义务人ID */
  withholdAgentId = '';

  /** 扣缴义务人 */
  withholdAgentName = '';

  /** 扣缴义务人类型 */
  withholdAgentType = '';
}

class bpoProjectRuleDTO {
  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.BPO_PROJECT_RULE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  bpoProjectRuleId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.CURRENT_HEADCOUNT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  currentHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.CUST_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  custId = '';

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.IS_SETTLE_BY_ACTUAL           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isSettleByActual = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_CODE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_DESC           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectDesc = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_NAME           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_SETTLEMENT_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectSettlementType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_TOTAL_AMT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectTotalAmt = '';
}

class bpoProjectRuleQuery {
  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.BPO_PROJECT_RULE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  bpoProjectRuleId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.CURRENT_HEADCOUNT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  currentHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.CUST_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  custId = '';

  /** custName */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.IS_SETTLE_BY_ACTUAL           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isSettleByActual = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_CODE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_DESC           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectDesc = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_NAME           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_SETTLEMENT_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectSettlementType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_BPO_PROJECT_RULE.PROJECT_TOTAL_AMT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  projectTotalAmt = '';

  /** startIndex */
  startIndex = undefined;
}

class contractVO {
  /** 审批步骤 */
  activityNameCn = '';

  /** 流程节点表的活动英文名称 */
  activityNameEn = '';

  /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
  activityStatus = '';

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** 约定到款月 */
  agereedAmtReceiveMon = '';

  /** 薪资发放日/约定薪资发放日 */
  agreedPayDt = '';

  /** 约定到款日 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** 审批通过时间 */
  approveDt = '';

  /** 审批通过时间到 */
  approveDtEnd = '';

  /** 审批通过时间从 */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** 大区ID */
  areaId = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaType = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaTypeName = '';

  /** 终稿 */
  attTypeDraftId = '';

  /** 终稿name */
  attTypeDraftName = '';

  /** 法务 */
  attTypeLegalId = '';

  /** 法务name */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 账单日期 */
  billDt = '';

  /** 城市 */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** 已经确认的工作流程 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 签约人均金额 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** 合同编号 */
  contractCode = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同文件名 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** 签约人数 */
  contractHeadcount = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 合同甲方 */
  contractPartA = '';

  /** 合同乙方 */
  contractPartB = '';

  /** 合同产品线id集合 */
  contractProductLineIds = '';

  /** 客户盖章后，合同回收时间 */
  contractRetrieveDt = '';

  /** 合同起始日期 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** 合同状态：0 初始；1 审批中；2 审批通过；3 退回修改；4 驳回终止 */
  contractStatus = '';

  /** 合同状态name */
  contractStatusName = '';

  /** 合同结束日期 */
  contractStopDate = '';

  /** 合同终止原因 */
  contractStopReason = '';

  /** 合同小类 */
  contractSubType = '';

  /** 合同类别（子类）名称 */
  contractSubTypeName = '';

  /** 合同服务状态：0新签1续签2过期3终止服务 */
  contractSvcState = '';

  /** 合同服务状态name */
  contractSvcStateName = '';

  /** 合同模板编号 */
  contractTemplateId = '';

  /** 新平台合同终止时填写的终止时间 */
  contractTerminationDate = '';

  /** 合同大类 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 创建人 */
  createByName = '';

  /** 创建人 */
  createByParty = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 合同生成方式 */
  createType = '';

  /** 账期（天） */
  creditPeriod = '';

  /** 客服审批 */
  csApproval = '';

  /** 客服审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** 必须是同一个客户，当前执行的合同的编号，如果续签多次，这个编号是最新的合同编号 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户盖章时间 */
  custSealDt = '';

  /** 供应商ID */
  departmentId = '';

  /** 供应商名称 */
  departmentName = '';

  /** 草稿备注 */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** 预估首次账单日期 */
  estimateFirstBillDate = '';

  /** 预计12个月内可达到人数 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 预计增长人数 */
  expectedIncrease = '';

  /** 原预计增长人数 */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** 合同审批的首个法务 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务名称 */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /**  合同审批的首个易薪税审批人员 */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区名称 */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司名称 */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** 未来商机 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** 现销售所属大区名称 */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 现销售所属分公司名称 */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** 导入文件ID */
  importFileId = '';

  /** 导入文件名称 */
  importFileName = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** 开票金额 */
  invoiceMoney = '';

  /** 开票张数 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？ */
  isAdjustRenewContract = '';

  /** 是否派单 */
  isAssign = '';

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** 是否降价、垫付、账期延期 1:是,0:否 */
  isDefer = '';

  /** 是否降价、垫付、账期延期名称 */
  isDeferName = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** 是否代发薪资 */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否抢单 */
  isRob = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** 是否二次开发 1:是,0:否 */
  isSecondaryDev = '';

  /** 是否二次开发名称 */
  isSecondaryDevName = '';

  /** 法务审批 */
  legalApproval = '';

  /** 法务审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  legalApprovalStatus = '';

  /** 合同附件备注 */
  legalRemark = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** 备注 */
  memo = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** 续签合同ID, 存放续签的大合同ID */
  nextContractId = '';

  /** 续签合同名称 */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** 非标合同审批单 */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 父合同id编号 */
  parentContractId = '';

  /** 付款和收款要点 */
  payCollectPoint = '';

  /** 薪资发放月 */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** 垫款额度 */
  prepayAmt = '';

  /** 垫付审批 */
  prepayApproval = '';

  /** 垫款审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  prepayApprovalStatus = '';

  /** 流程定义id */
  processDefId = '';

  /** 对应的流程实例ID */
  processInstanceId = '';

  /** 产品线id集合 */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** 项目前期计划或实施要求 */
  projectPlanRequest = '';

  /** 全国项目交接表单 */
  projectRemark = '';

  /** 供应商类型 */
  providerType = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 报价单集合id */
  quoIds = '';

  /** 被续签的旧合同号 */
  renewedContractNum = '';

  /** 客服反馈撤单预警日期大于等于 */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** 客服反馈撤单预警日期小于等于 */
  reportGlEvacuatedDate = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增/存量标识 （手工） */
  salFlagManual = '';

  /** 1 纯新增,2 存量,3 纯新增/存量,4 纯新增+滚动存量,5 滚动存量,6 滚动存量+存量 */
  salFlagManualName = '';

  /** 新增/存量标识 （系统）：1历史纯新增、2滚动新增、3存量、4当月启动纯新增、-1未有首版账单 */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** 销售审批 */
  salesApprove = '';

  /** 销售报价审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  salesApproveStatus = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** 用章审核状态 0：初始态3：通过 -3：退回修改 -4：驳回终止 */
  sealApproveStatus = '';

  /** 公司盖章时间 */
  sealDt = '';

  /** 用章审核 */
  sealOpinion = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 撤单原因 */
  stopReason = '';

  /** 终止服务系统操作时间 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 服务区域 */
  svcRegion = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** 交接单流程ID */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** 销售--客服交接单 */
  transferRemark = '';

  /** 交接上传文件名 */
  uploadFileName = '';

  /** 交接上传URL */
  uploadUrl = '';

  /** 工作流id */
  workitemId = '';
}

class contractVersionDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** contractVersion */
  contractVersion = '';

  /** contractVersionType */
  contractVersionType = '';

  /** contractVersionTypeName */
  contractVersionTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** effectBy */
  effectBy = '';

  /** effectDt */
  effectDt = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** failureBy */
  failureBy = '';

  /** failureDt */
  failureDt = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDeleted */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** slContractVersionId */
  slContractVersionId = '';

  /** status */
  status = '';

  /** statusName */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class contractVersionQuery {
  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** contractVersion */
  contractVersion = '';

  /** contractVersionType */
  contractVersionType = '';

  /** contractVersionTypeName */
  contractVersionTypeName = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** effectBy */
  effectBy = '';

  /** effectDt */
  effectDt = '';

  /** endIndex */
  endIndex = undefined;

  /** failureBy */
  failureBy = '';

  /** failureDt */
  failureDt = '';

  /** isDeleted */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** remark */
  remark = '';

  /** slContractVersionId */
  slContractVersionId = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** statusName */
  statusName = '';
}

class customerQuery {
  /** add */
  add = false;

  /** bankAcct */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** bankNum */
  bankNum = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** bigCompanyApplyMan */
  bigCompanyApplyMan = '';

  /** bigCompanyApplyString */
  bigCompanyApplyString = '';

  /** bigCompanyAudit */
  bigCompanyAudit = '';

  /** bigCompanyAuditMan */
  bigCompanyAuditMan = '';

  /** bigCompanyAuditString */
  bigCompanyAuditString = '';

  /** bigCompanyCause */
  bigCompanyCause = '';

  /** bigCompanyProcessId */
  bigCompanyProcessId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** bizEndDt */
  bizEndDt = '';

  /** bizScope */
  bizScope = '';

  /** bizStartDt */
  bizStartDt = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** channelId */
  channelId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** companyCityId */
  companyCityId = '';

  /** companyCityName */
  companyCityName = '';

  /** contactCell */
  contactCell = '';

  /** contactCell2 */
  contactCell2 = '';

  /** contactTel */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** coreBizReqId */
  coreBizReqId = '';

  /** coreBizReqName */
  coreBizReqName = '';

  /** countryId */
  countryId = '';

  /** countryName */
  countryName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** creditHisId */
  creditHisId = '';

  /** creditHisName */
  creditHisName = '';

  /** csOfficeCountyId */
  csOfficeCountyId = '';

  /** csOfficeCountyName */
  csOfficeCountyName = '';

  /** custCityName */
  custCityName = '';

  /** custCode */
  custCode = '';

  /** custEnglishName */
  custEnglishName = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** custShortName */
  custShortName = '';

  /** custSize */
  custSize = '';

  /** custTransferStatus */
  custTransferStatus = '';

  /** custType */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departmentId */
  departmentId = '';

  /** ePropertyId */
  ePropertyId = '';

  /** ePropertyName */
  ePropertyName = '';

  /** eSizeId */
  eSizeId = '';

  /** eSizeName */
  eSizeName = '';

  /** email */
  email = '';

  /** email2 */
  email2 = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fax */
  fax = '';

  /** fax2 */
  fax2 = '';

  /** fileId */
  fileId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** groupId */
  groupId = '';

  /** groupName */
  groupName = '';

  /** hrContract */
  hrContract = '';

  /** impBatchId */
  impBatchId = '';

  /** inId */
  inId = '';

  /** industryId */
  industryId = '';

  /** industryName */
  industryName = '';

  /** insuranceContact */
  insuranceContact = '';

  /** insuranceContactTel */
  insuranceContactTel = '';

  /** introduction */
  introduction = '';

  /** isBigCompany */
  isBigCompany = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isContract */
  isContract = '';

  /** 删除标记 */
  isDeleted = '';

  /** isDeletedText */
  isDeletedText = '';

  /** 是否财务专用_SAMI */
  isFinnace = '';

  /** isInternalCust */
  isInternalCust = '';

  /** isInternalCustText */
  isInternalCustText = '';

  /** isPublicTradedId */
  isPublicTradedId = '';

  /** isPublicTradedName */
  isPublicTradedName = '';

  /** isValid */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** legalRep */
  legalRep = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** organizationCode */
  organizationCode = '';

  /** organizationRemark */
  organizationRemark = '';

  /** outSvcProviderId */
  outSvcProviderId = '';

  /** outSvcProviderName */
  outSvcProviderName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 政策大全类型 */
  policyType = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** prospectiveCustId */
  prospectiveCustId = '';

  /** providerId */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** regAddress */
  regAddress = '';

  /** regCapital */
  regCapital = '';

  /** regEndDt */
  regEndDt = '';

  /** regStartDt */
  regStartDt = '';

  /** regionId */
  regionId = '';

  /** relaReqId */
  relaReqId = '';

  /** relaReqName */
  relaReqName = '';

  /** reqAquirementDt */
  reqAquirementDt = '';

  /** reqAquirementTypeId */
  reqAquirementTypeId = '';

  /** reqAquirementTypeName */
  reqAquirementTypeName = '';

  /** reqRemark */
  reqRemark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salesCityId */
  salesCityId = '';

  /** salesCityName */
  salesCityName = '';

  /** salesCode */
  salesCode = '';

  /** salesGroup */
  salesGroup = '';

  /** salesId */
  salesId = '';

  /** salesName */
  salesName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sourceId */
  sourceId = '';

  /** sourceName */
  sourceName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** timesOfRecovery */
  timesOfRecovery = '';

  /** transTime */
  transTime = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** userRoleId */
  userRoleId = '';

  /** userRoleType */
  userRoleType = '';

  /** visitStatus */
  visitStatus = '';

  /** wageCalculateType */
  wageCalculateType = '';

  /** wageCalculateTypeText */
  wageCalculateTypeText = '';

  /** website */
  website = '';

  /** workAddress */
  workAddress = '';

  /** zipCode */
  zipCode = '';
}

class customerVO {
  /** 银行账号 */
  bankAcct = '';

  /** 开户行名称 */
  bankName = '';

  /** 银行行号 */
  bankNum = '';

  /** 大客户申请人 */
  bigCompanyApplyMan = '';

  /** 大客户申请时间 */
  bigCompanyApplyString = '';

  /** 大客户审批意见 */
  bigCompanyAudit = '';

  /** 大客户审核人 */
  bigCompanyAuditMan = '';

  /** 大客户审核时间 */
  bigCompanyAuditString = '';

  /** 大客户处理原由 */
  bigCompanyCause = '';

  /** 大客户处理流程实例id */
  bigCompanyProcessId = '';

  /** 业务结束时间 */
  bizEndDt = '';

  /** 业务范围 */
  bizScope = '';

  /** 业务开始时间 */
  bizStartDt = '';

  /** 渠道ID */
  channelId = '';

  /** 公司所在城市编码 */
  companyCityId = '';

  /** 公司所在城市 */
  companyCityName = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人手机2 */
  contactCell2 = '';

  /** 联系人电话 */
  contactTel = '';

  /** 核心业务需求 */
  coreBizReqId = '';

  /** 核心业务需求 */
  coreBizReqName = '';

  /** 国家ID */
  countryId = '';

  /** 国家Name */
  countryName = '';

  /** 客户资信ID */
  creditHisId = '';

  /** 客户资信Name */
  creditHisName = '';

  /** 客户办公地址所在区县ID （前期可不用） */
  csOfficeCountyId = '';

  /** 客户办公地址所在区县Name */
  csOfficeCountyName = '';

  /** 客户所在城市 */
  custCityName = '';

  /** 客户编码 */
  custCode = '';

  /** 客户英文名称 */
  custEnglishName = '';

  /** 客户id */
  custId = '';

  /** 客户中文名称 */
  custName = '';

  /** 客户缩写名称 */
  custShortName = '';

  /** 客户规模 */
  custSize = '';

  /** 新增从陌生拜访转移 */
  custTransferStatus = '';

  /** 客户类型:0 直销 1 渠道 */
  custType = '';

  /** 分公司id */
  departmentId = '';

  /** 企业性质ID */
  ePropertyId = '';

  /** 企业性质Name */
  ePropertyName = '';

  /** 企业规模ID */
  eSizeId = '';

  /** 企业规模Name */
  eSizeName = '';

  /** 联系人邮件 */
  email = '';

  /** 联系人邮件2 */
  email2 = '';

  /** 联系人传真 */
  fax = '';

  /** 联系人传真2 */
  fax2 = '';

  /** 附件id */
  fileId = '';

  /** 如果与company_id相同，表示自己是同一个集团，如果是不一样的，表示是某个集团下的一个成员，程序需要控制只有两层关系 */
  groupId = '';

  /** 集团名称 */
  groupName = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** 导入批次号 */
  impBatchId = '';

  /** 所属行业ID */
  industryId = '';

  /** 所属行业Name */
  industryName = '';

  /** 保险联系人 */
  insuranceContact = '';

  /** 保险联系电话 */
  insuranceContactTel = '';

  /** 情况简介 */
  introduction = '';

  /** 是否是大公司 */
  isBigCompany = '';

  /** 是否签约合同 */
  isContract = '';

  /** 是否删除文本 */
  isDeletedText = '';

  /** 是否财务专用_SAMI */
  isFinnace = '';

  /** 是否关联客户 */
  isInternalCust = '';

  /** 是否关联客户text */
  isInternalCustText = '';

  /** 是否上市ID */
  isPublicTradedId = '';

  /** 是否上市Name */
  isPublicTradedName = '';

  /** 1:正在跟进,0:删除区2:共享区  */
  isValid = '';

  /** 法人代表 */
  legalRep = '';

  /** 备注 */
  memo = '';

  /** 组织机构代码 */
  organizationCode = '';

  /** 是否上市Name */
  organizationRemark = '';

  /** 是否外包,1是,0否 */
  outSvcProviderId = '';

  /** 是否外包名称 */
  outSvcProviderName = '';

  /** 政策大全类型 */
  policyType = '';

  /** 来自陌生拜访客户id */
  prospectiveCustId = '';

  /** 联系人1职位 */
  providerId = '';

  /** 注册地址 */
  regAddress = '';

  /** 注册资金（万） */
  regCapital = '';

  /** 注册到期日期 */
  regEndDt = '';

  /** 注册开始日期 */
  regStartDt = '';

  /** 大区 */
  regionId = '';

  /** 相关业务需求 */
  relaReqId = '';

  /** 相关业务需求 */
  relaReqName = '';

  /** 获得需求时间 */
  reqAquirementDt = '';

  /** 需求获得方式ID */
  reqAquirementTypeId = '';

  /** 需求获得方式Name */
  reqAquirementTypeName = '';

  /** 需求备注 */
  reqRemark = '';

  /** 销售人员所在城市id */
  salesCityId = '';

  /** 销售人员所在城市 */
  salesCityName = '';

  /** 所属销售人员 */
  salesCode = '';

  /** 销售组 */
  salesGroup = '';

  /** 销售id */
  salesId = '';

  /** 销售人员名字 */
  salesName = '';

  /** 客户来源ID */
  sourceId = '';

  /** 客户来源Name */
  sourceName = '';

  /** 已经恢复次数 */
  timesOfRecovery = '';

  /** 转移时间 */
  transTime = '';

  /** 用户角色id */
  userRoleId = '';

  /** 用户角色类型 */
  userRoleType = '';

  /** 访问状态 */
  visitStatus = '';

  /** 薪资计算方式类型 */
  wageCalculateType = '';

  /** 薪资计算方式文本 */
  wageCalculateTypeText = '';

  /** 网址 */
  website = '';

  /** 办公地址 */
  workAddress = '';

  /** 邮政编码（办公地址） */
  zipCode = '';
}

class differenceQuery {
  /** 接单地 */
  assignee_provider_id = '';

  /** 客户Code */
  custCode = '';

  /** 客户名称 */
  custName = '';

  /** 客户号 */
  cust_id = '';

  /** 雇员唯一号 */
  emp_code = '';

  /** 雇员姓名 */
  emp_name = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 社保产品 */
  product_id = '';

  /** 对比时间点 */
  service_month = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同名称 */
  subcontract_name = '';

  /** 登录人Id */
  user_id = '';
}

class productDataQuery {
  /** 审批价格 */
  approvePrice = '';

  /** atr */
  atr = '';

  /** 提供给公用查询产品界面使用,必须传城市id */
  cityId = '';

  /** 成本 */
  cost = '';

  /** 详述 */
  detail = '';

  /** 生效日期 */
  effectiveDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否支持城市系数打折 */
  ifCityDiscount = '';

  /** 是否支持城市系数打折名称 */
  ifCityDiscountName = '';

  /** 失效日期 */
  invalidDt = '';

  /** 是否代办 */
  isAgency = '';

  /** 是否按天计算 */
  isCalculateByDay = '';

  /** 是否删除. 0 否, 1 已删除，默认值为0 */
  isDeleted = '';

  /** 是否存在有效日期 */
  isEffectDt = '';

  /** 是否存在有效期限名称 */
  isEffectDtName = '';

  /** 是否费用返还产品 */
  isFundReturn = '';

  /** 是否服务人数限制 */
  isLimitSvcNum = '';

  /** 是否服务人数限制名称 */
  isLimitSvcNumName = '';

  /** 是否可补收 */
  isMakeupCollect = '';

  /** 是否一次性付款 */
  isOneTimePay = '';

  /** 非社保公积金类产品并且是一次性产品或者代收代付产品 */
  isOneTimePayAndAgency = '';

  /** 是否区域限制 */
  isRegionRestrict = '';

  /** 是否区域限制名称 */
  isRegionRestrictName = '';

  /** 是否可以报销 */
  isReimbursement = '';

  /** 是否可退费 */
  isReturns = '';

  /** 是否虚产品 */
  isVirtual = '';

  /** 最大服务人数 */
  maxSvcHeadcount = '';

  /** 最小成本 */
  minCost = '';

  /** 最小服务人数 */
  minSvcHeadcount = '';

  /** 报岗时可修改 */
  modifiableAtRptSep = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品大类 */
  productCategory = '';

  /** 产品大类名称 */
  productCategoryName = '';

  /** 产品编号 */
  productCode = '';

  /** productCost */
  productCost = '';

  /** productDesc */
  productDesc = '';

  /** 财务产品大类 */
  productFinanceCategory = '';

  /** 财务产品大类名称 */
  productFinanceCategoryName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLineId = '';

  /** 产品线名称 */
  productLineName = '';

  /** 服务产品名称 */
  productName = '';

  /** productPrice */
  productPrice = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 查询类型 用于区别报价单模板和其他查询产品的条件 */
  queryType = '';

  /** 产品比例细想id */
  quotationId = '';

  /** 产品比例细想id */
  quotationItemId = '';

  /** salesPrice */
  salesPrice = '';

  /** salesPriceNoTax */
  salesPriceNoTax = '';

  /** ssGroupId */
  ssGroupId = '';

  /** 提供给公用查询界面使用,必须传城市id,次属性为模糊查找 */
  ssGroupName = '';

  /** 标准价格 */
  standardPrice = '';

  /** startIndex */
  startIndex = undefined;

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  status = '';

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  statusName = '';

  /** vat */
  vat = '';

  /** vatr */
  vatr = '';
}

class productQuery {
  /** categories */
  categories = '';

  /** 类型 */
  category = '';

  /** cityId */
  cityId = '';

  /** 部门 */
  departmentId = '';

  /** endIndex */
  endIndex = undefined;

  /** isOneTimePay */
  isOneTimePay = '';

  /** 是否一次性 */
  isOneTimePayAndAgency = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品类型 */
  productCategory = '';

  /** 服务产品名称 */
  productName = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 人数折扣体系查询id */
  quotationTempltId = '';

  /** 社保组 */
  ssGroupId = '';

  /** startIndex */
  startIndex = undefined;

  /** 是否发布. 0 否，1发布,2失效，默认是0 */
  status = '';
}

class quoTemplTrdCostDTO {
  /** 新增列表 */
  addList = [];

  /** 修改列表 */
  uptList = [];
}

class quoTemplTrdCostQuery {
  /** 城市 */
  cityId = '';

  /** 成本 */
  cost = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 第3方外部供应商id */
  providerId = '';

  /** 第3方外部供应商name */
  providerName = '';

  /** 报价单方案第三方成本维护id */
  quotationTemplTrdCost = '';

  /** 报价单方案id */
  quotationTemplateId = '';

  /** 报价单方案name */
  quotationTemplateName = '';

  /** 备注 */
  remark = '';

  /** startIndex */
  startIndex = undefined;
}

class quotationDTO {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_PROCESS_INS_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveProcessInsId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** attId */
  attId = '';

  /** attName */
  attName = '';

  /** auditOpinion */
  auditOpinion = '';

  /** 审批类型 */
  auditType = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITE_QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  citeQuotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITY_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** 创建者name */
  createByName = '';

  /** createDtFrom */
  createDtFrom = '';

  /** createDtTo */
  createDtTo = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CURRENCY           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  currency = undefined;

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CUST_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.DEPARTMENT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  departmentId = undefined;

  /** depthMark */
  depthMark = '';

  /** effectBy */
  effectBy = '';

  /** effectDt */
  effectDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.EXCHANGE_RATE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  exchangeRate = undefined;

  /** governingArea */
  governingArea = '';

  /** governingBranch */
  governingBranch = '';

  /** groupType */
  groupType = '';

  /** groupTypeName */
  groupTypeName = '';

  /** ifAf */
  ifAf = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_FIRST_AUDIT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifFirstAudit = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_NATIONWIDE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifNationwide = undefined;

  /** ifSs */
  ifSs = '';

  /** invalidBy */
  invalidBy = '';

  /** invalidDt */
  invalidDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IS_SPECIAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isSpecialPrice = undefined;

  /** ladderQuotationType */
  ladderQuotationType = '';

  /** markType */
  markType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.NEW_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  newSaleId = undefined;

  /** newSaleName */
  newSaleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.OLD_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  oldSaleId = undefined;

  /** participant */
  participant = '';

  /** processDefId */
  processDefId = '';

  /** processInsId */
  processInsId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_CODE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationCode = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationHisId */
  quotationHisId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** quotationItemCiList */
  quotationItemCiList = [];

  /** quotationItemCpList */
  quotationItemCpList = [];

  /** quotationItemDetailBusinessList */
  quotationItemDetailBusinessList = [];

  /** quotationItemDetailBusinessListUpdate */
  quotationItemDetailBusinessListUpdate = [];

  /** quotationItemDetailBusinessType */
  quotationItemDetailBusinessType = '';

  /** quotationItemDetailCiList */
  quotationItemDetailCiList = [];

  /** quotationItemDetailCiType */
  quotationItemDetailCiType = '';

  /** quotationItemDetailCpList */
  quotationItemDetailCpList = [];

  /** quotationItemDetailCpType */
  quotationItemDetailCpType = '';

  /** quotationItemDetailEmployerList */
  quotationItemDetailEmployerList = [];

  /** quotationItemDetailEmployerListUpdate */
  quotationItemDetailEmployerListUpdate = [];

  /** quotationItemDetailEmployerType */
  quotationItemDetailEmployerType = '';

  /** quotationItemDetailHealthList */
  quotationItemDetailHealthList = [];

  /** quotationItemDetailHealthListUpdate */
  quotationItemDetailHealthListUpdate = [];

  /** quotationItemDetailHealthType */
  quotationItemDetailHealthType = '';

  /** quotationItemDetailHmsList */
  quotationItemDetailHmsList = [];

  /** quotationItemDetailHmsType */
  quotationItemDetailHmsType = '';

  /** quotationItemDetailPeList */
  quotationItemDetailPeList = [];

  /** quotationItemDetailPeType */
  quotationItemDetailPeType = '';

  /** quotationItemDetailProductList */
  quotationItemDetailProductList = [];

  /** quotationItemDetailProductListUpdate */
  quotationItemDetailProductListUpdate = [];

  /** quotationItemDetailRpList */
  quotationItemDetailRpList = [];

  /** quotationItemDetailSaleList */
  quotationItemDetailSaleList = [];

  /** quotationItemDetailSaleListUpdate */
  quotationItemDetailSaleListUpdate = [];

  /** quotationItemDetailWelList */
  quotationItemDetailWelList = [];

  /** quotationItemDetailWelType */
  quotationItemDetailWelType = '';

  /** quotationItemHmsList */
  quotationItemHmsList = [];

  /** quotationItemPeList */
  quotationItemPeList = [];

  /** quotationItemRisk */
  quotationItemRisk = new QuotationItem();

  /** quotationItemRiskDetail */
  quotationItemRiskDetail = new QuotationItemDetail();

  /** quotationItemWelList */
  quotationItemWelList = [];

  /** quotationLadderList */
  quotationLadderList = [];

  /** quotationLadderListUpdate */
  quotationLadderListUpdate = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_NAME           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationName = '';

  /** quotationSpecial */
  quotationSpecial = new QuotationSpecial();

  /** quotationSpecialPropertyList */
  quotationSpecialPropertyList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_SPECIAL_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_COST           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalCost = undefined;

  /** 职场健康总售价 */
  quotationTotalOhPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_SAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalSalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.REMARK           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** remarkApp */
  remarkApp = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SUPPLT_MED_INSUR_HEADCOUNT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suppltMedInsurHeadcount = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SVC_AREA           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  svcArea = '';

  /** userId */
  userId = '';

  /** workitemId */
  workitemId = '';
}

class quotationLadderDTO {
  /** atr */
  atr = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION_LADDER.BEGIN_NUMBER           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  beginNumber = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION_LADDER.END_NUMBER           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  endNumber = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION_LADDER.PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  price = undefined;

  /** productId */
  productId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION_LADDER.QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION_LADDER.QUOTATION_TEMPLATE_INFECT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTemplateInfectId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION_LADDER.QUOTATION_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationType = undefined;

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;
}

class quotationQuery {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_PROCESS_INS_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveProcessInsId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** attId */
  attId = '';

  /** attName */
  attName = '';

  /** auditOpinion */
  auditOpinion = '';

  /** 审批类型 */
  auditType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITE_QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  citeQuotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITY_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** 创建者name */
  createByName = '';

  /** createDtFrom */
  createDtFrom = '';

  /** createDtTo */
  createDtTo = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CURRENCY           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  currency = undefined;

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CUST_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.DEPARTMENT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  departmentId = undefined;

  /** depthMark */
  depthMark = '';

  /** effectBy */
  effectBy = '';

  /** effectDt */
  effectDt = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.EXCHANGE_RATE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  exchangeRate = undefined;

  /** governingArea */
  governingArea = '';

  /** governingBranch */
  governingBranch = '';

  /** groupType */
  groupType = '';

  /** groupTypeName */
  groupTypeName = '';

  /** ifAf */
  ifAf = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_FIRST_AUDIT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifFirstAudit = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_NATIONWIDE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifNationwide = undefined;

  /** ifSs */
  ifSs = '';

  /** invalidBy */
  invalidBy = '';

  /** invalidDt */
  invalidDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IS_SPECIAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isSpecialPrice = undefined;

  /** markType */
  markType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.NEW_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  newSaleId = undefined;

  /** newSaleName */
  newSaleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.OLD_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  oldSaleId = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** processDefId */
  processDefId = '';

  /** processInsId */
  processInsId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_CODE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationCode = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationHisId */
  quotationHisId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_NAME           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_SPECIAL_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_COST           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalCost = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_SAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalSalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.REMARK           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SUPPLT_MED_INSUR_HEADCOUNT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suppltMedInsurHeadcount = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SVC_AREA           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  svcArea = '';

  /** workitemId */
  workitemId = '';
}

class quotationTemplCFGQuery {
  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 报价单模板专属客户设置ID */
  quotationTemplCfgId = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** startIndex */
  startIndex = undefined;
}

class quotationTemplDTO {
  /** 新增列表 */
  addList = [];

  /** 新增列表 */
  addTemplateAreasList = [];

  /** 新增列表 */
  addTemplateInfectList = [];

  /** 审批价格 */
  approvePrice = '';

  /** 区域限制 */
  areaLimit = '';

  /** 区域限制名称 */
  areaLimitName = '';

  /** 创建者name */
  createByName = '';

  /** 客户code */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户name */
  custName = '';

  /** 生效日期 */
  effectiveDt = '';

  /** 第三方供应商 */
  ifForThirdpart = '';

  /** 保险公司id */
  insuranceCompanyId = '';

  /** 失效日期 */
  invalidDt = '';

  /** 是否支持城市系数打折 */
  isCityDiscount = '';

  /** 服务人数限制 */
  isLimitedBySvcHeadcount = '';

  /** 服务人数限制名称 */
  isLimitedBySvcHeadcountName = '';

  /** 是否发布 */
  isRelease = '';

  /** 页面是否是修改状态 */
  isUpdateStatus = '';

  /** 最大服务人数 */
  maxSvcHeadCount = '';

  /** 最小服务人数 */
  minSvcHeadCount = '';

  /** newSaleId */
  newSaleId = '';

  /** 产品id */
  productId = '';

  /** 产品线id */
  productLineId = '';

  /** 产品线名称 */
  productLineName = '';

  /** 产品name */
  productName = '';

  /** 修改商业保险的报价单方案内容列表 */
  quoTemplInsuranceTypeAddList = [];

  /** 详情列表 */
  quoTemplItemAddList = [];

  /** 模板产品线id */
  quotationProductLineId = '';

  /** 专属客户id */
  quotationTemplCfgId = '';

  /** 报价单方案编号 */
  quotationTempltCode = '';

  /** 普通模版的成本 */
  quotationTempltCost = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 报价单方案名字 */
  quotationTempltName = '';

  /** 普通模版的标准价格 */
  quotationTempltPrice = '';

  /** 报价单模版的使用范围 */
  quotationTempltScope = '';

  /** 报价单模版的使用范围名称 */
  quotationTempltScopeName = '';

  /** 报价单模版的类型 */
  quotationTempltType = '';

  /** 报价单模版的类型名称 */
  quotationTempltTypeName = '';

  /** 备注 */
  remark = '';

  /** 生效状态 */
  status = '';

  /** 修改列表 */
  upList = [];

  /** 修改列表 */
  upTemplateAreasList = [];

  /** 修改列表 */
  upTemplateInfectList = [];

  /** 有效期限 */
  validDtType = '';

  /** 有效期限名称 */
  validDtTypeName = '';
}

class quotationTemplInsuranceTypeDTO {
  /** 修改商业保险的报价单方案内容列表 */
  addList = [];

  /** 险种编号 */
  biTypeCode = '';

  /** 险种id */
  biTypeId = '';

  /** 险种名称 */
  biTypeName = '';

  /** 成本 */
  cost = '';

  /** 生效时间 */
  effectiveDt = '';

  /** 保额 */
  insuredSum = '';

  /** 失效时间 */
  invalidDt = '';

  /** 是否含子女 */
  isChildren = '';

  /** 是否含配偶 */
  isSpouse = '';

  /** 供应商id */
  providerId = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 备注 */
  remark = '';

  /** 标准报价 */
  standardQuotation = '';

  /** 子产品id */
  subProductId = '';

  /** 子产品name */
  subProductName = '';

  /** 报价单模板险种id */
  templateInsuranceTypeId = '';
}

class quotationTemplItemDTO {
  /** 方案子项列表 */
  addList = [];

  /** approvePrice */
  approvePrice = '';

  /** 附加税 */
  at = '';

  /** 附加税的税率% */
  ator = '';

  /** 附加税率% */
  atr = '';

  /** 保额 */
  biAcountRemark = '';

  /** 保司 */
  biCompany = '';

  /** 险种  2001 */
  biType = '';

  /** 险种  2001 */
  biTypeName = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateType = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateTypeName = '';

  /** 成本 */
  cost = '';

  /** 数量 */
  countNum = '';

  /** 预估成本 */
  estCost = '';

  /** 进项税率% */
  iptr = '';

  /** 标准售价（含附加税） */
  priceAt = '';

  /** 标准售价（含增值税附加税） */
  priceVat = '';

  /** 产品大类 */
  productCategory = '';

  /** 产品大类名称 */
  productCategoryName = '';

  /** 产品id */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 产品类型id */
  productTypeId = '';

  /** 产品类型名称 */
  productTypeName = '';

  /** 供应商 */
  provider = '';

  /** 采购物品描述 */
  purchaseRemark = '';

  /** 报价单模板子项id */
  quotationTemplItemId = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 报价单子项的备注 */
  remark = '';

  /** 标准报价 */
  standardQuotation = '';

  /** 总成本 */
  totalCost = '';

  /** 总售价 */
  totalPrice = '';

  /** 增值税 */
  vat = '';

  /** 增值税率% */
  vatr = '';
}

class quotationTemplQuery {
  /** 审批价格 */
  approvePrice = '';

  /** 区域限制 */
  areaLimit = '';

  /** 区域限制名称 */
  areaLimitName = '';

  /** 创建者name */
  createByName = '';

  /** 客户code */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户name */
  custName = '';

  /** 生效日期 */
  effectiveDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 第三方供应商 */
  ifForThirdpart = '';

  /** 保险公司id */
  insuranceCompanyId = '';

  /** 失效日期 */
  invalidDt = '';

  /** 是否支持城市系数打折 */
  isCityDiscount = '';

  /** 删除标记 */
  isDeleted = '';

  /** 服务人数限制 */
  isLimitedBySvcHeadcount = '';

  /** 服务人数限制名称 */
  isLimitedBySvcHeadcountName = '';

  /** 是否发布 */
  isRelease = '';

  /** 页面是否是修改状态 */
  isUpdateStatus = '';

  /** 最大服务人数 */
  maxSvcHeadCount = '';

  /** 最小服务人数 */
  minSvcHeadCount = '';

  /** 职场健康类型 */
  newType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品id */
  productId = '';

  /** 产品线id */
  productLineId = '';

  /** 产品线名称 */
  productLineName = '';

  /** 产品name */
  productName = '';

  /** 模板产品线id */
  quotationProductLineId = '';

  /** 专属客户id */
  quotationTemplCfgId = '';

  /** 报价单方案编号 */
  quotationTempltCode = '';

  /** 普通模版的成本 */
  quotationTempltCost = '';

  /** 报价单方案信息表id */
  quotationTempltId = '';

  /** 报价单方案名字 */
  quotationTempltName = '';

  /** 报价单方案名字（校验唯一） */
  quotationTempltNameEx = '';

  /** 普通模版的标准价格 */
  quotationTempltPrice = '';

  /** 报价单模版的使用范围 */
  quotationTempltScope = '';

  /** 报价单模版的使用范围名称 */
  quotationTempltScopeName = '';

  /** 报价单模版的类型 */
  quotationTempltType = '';

  /** 报价单模版的类型名称 */
  quotationTempltTypeName = '';

  /** 备注 */
  remark = '';

  /** startIndex */
  startIndex = undefined;

  /** 生效状态 */
  status = '';

  /** 有效期限 */
  validDtType = '';

  /** 有效期限名称 */
  validDtTypeName = '';
}

class quotationTemplateAreasDTO {
  /** 新增列表 */
  addList = [];

  /** 修改列表 */
  uptList = [];
}

class quotationTemplateAreasQuery {
  /** 大区ID */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 城市Id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 有效的分公司或办事处 */
  departmentId = '';

  /** 有效的分公司或办事处 名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 0:有效 1：删除 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** area_type=0时存报价单模板号quotation_template_id；area_type=1时存产品号product_id */
  quotationTempltId = '';

  /** 类型 说明 0:模版1:产品 */
  quotationTempltType = '';

  /** 主键 */
  salesQuotationTemplateAreas = '';

  /** startIndex */
  startIndex = undefined;
}

class quotationTemplateInfectDTO {
  /** 新增列表 */
  addList = [];

  /** 修改列表 */
  uptList = [];
}

class quotationTemplateInfectQuery {
  /** 起始年龄 */
  beginNumber = '';

  /** endIndex */
  endIndex = undefined;

  /** 截止年龄 */
  endNumber = '';

  /** 0:有效 1：删除 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 年龄浮动价格 */
  price = '';

  /** 报价单模板人数折扣设置id */
  quotationTemplateInfectId = '';

  /** 报价单模板信息表id */
  quotationTempltId = '';

  /** 0:普通产品1:普通模版2:补医保雇主责任险家财险模版 */
  quotationTempltType = '';

  /** startIndex */
  startIndex = undefined;
}

class subProductQuery {
  /** 供应商名称 */
  biProviderName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品id */
  productId = '';

  /** startIndex */
  startIndex = undefined;

  /** 子产品状态 */
  status = '';

  /** 子产品名称 */
  subProductName = '';
}

export const sale = {
  BaseEntity,
  BatchSaveOrUpdateCustPayerSJVO,
  BatchSaveOrUpdateCustPayerSalaryVO,
  BatchSaveOrUpdateCustPayerVO,
  CRMProductlineMapping,
  CommonResponse,
  Contract,
  ContractDTO,
  ContractFile,
  ContractQuery,
  ContractRetiree,
  ContractVistMemoDTO,
  CustArchives,
  CustArchivesShare,
  CustAttDTO,
  CustMaterialDTO,
  CustMaterialQuery,
  CustPlayerAssertQuery,
  CustPlayerSalaryQuery,
  CustQuery,
  CustReportShare,
  CustShareQuery,
  CustUseRecApproveQuery,
  Customer,
  CustomerListDTO,
  CustomerPayer,
  CustomerPayerDTO,
  CustomerPayerInterDTO,
  CustomerPayerSJDTO,
  CustomerPayerSalary,
  CustomerPayerSalaryDTO,
  CustomerPayrollDTO,
  CustomerRecordDTO,
  CustomerRecordQuery,
  DropdownList,
  EosUser,
  ExportQuery,
  FilterEntity,
  Group,
  GroupCompanyQuery,
  ImpRuleQuery,
  InsuranceProvider,
  InsuranceProviderDTO,
  InsuranceType,
  InsuranceTypeDTO,
  InsuranceTypeQuery,
  MarketActivityInfoDTO,
  MarketActivityInfoQuery,
  MaterialQuery,
  MonthlyReportDTO,
  Page,
  ProductDTO,
  ProductData,
  ProductInterDTO,
  ProductTypeDTO,
  ProjectMembers,
  QuoTemplTrdCost,
  QuotationItem,
  QuotationItemDetail,
  QuotationLadder,
  QuotationSpecial,
  QuotationSpecialProperty,
  QuotationTempl,
  QuotationTemplInsuranceType,
  QuotationTemplItem,
  QuotationTemplateAreas,
  QuotationTemplateInfect,
  ServiceSubType,
  SubProductDTO,
  SvcReport,
  SvcReportQuery,
  WithholdAgentCustNoCheck,
  bpoProjectRuleDTO,
  bpoProjectRuleQuery,
  contractVO,
  contractVersionDTO,
  contractVersionQuery,
  customerQuery,
  customerVO,
  differenceQuery,
  productDataQuery,
  productQuery,
  quoTemplTrdCostDTO,
  quoTemplTrdCostQuery,
  quotationDTO,
  quotationLadderDTO,
  quotationQuery,
  quotationTemplCFGQuery,
  quotationTemplDTO,
  quotationTemplInsuranceTypeDTO,
  quotationTemplItemDTO,
  quotationTemplQuery,
  quotationTemplateAreasDTO,
  quotationTemplateAreasQuery,
  quotationTemplateInfectDTO,
  quotationTemplateInfectQuery,
  subProductQuery,
};
