/**
 * 普通销售产品
 */
import { useWritable } from '@/components/Writable';
import { ColElementButton, RowElement } from '@/components/Forms/FormLayouts';
import { Writable } from '@/components/Writable';
import { Button, FormInstance, Input, InputNumber, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { WritableColumnProps } from '@/utils/writable/types';
import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { renderListToSelectors } from '@/components/Selectors';
import { handleGetDict, quoteTyMap } from '../../QuotationTempManage/dict';
import { mapToSelectors } from '@/components/Selectors';
import { ProductPop } from '../../QuotationTempManage/productPop';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import Link from 'antd/lib/typography/Link';
import { msgErr, msgWarn } from '@/utils/methods/message';
import { Calculator } from '@/utils/methods/calculator';
import {
  calculateCostDistribution,
  calculateLadderPrice,
  calculatePrices,
  filterOutSelectedRows,
  fixPrecision,
  formatPercentTruncated,
  mergeUniqueData,
  pickData,
  preciseDeleteRows,
  updateTableRowData,
} from '../../QuotationTempManage/calculation';

import { tableIndexKey } from '@/utils/settings/forms';
import OrdinarySales from '../../QuotationTempManage/components/Forms/OrdinarySales';
import OldWefareProduct from './oldWefareProduct';
import Codal from '@/components/Codal';
import NumericInput from '../../QuotationTempManage/NumericInput';
import { isEmpty } from 'lodash';

export interface SalesProductRef {
  getData: () => Promise<any>;
  resetData: () => void;
  validateData: () => Promise<boolean>;
}
interface SalesProductProps {
  [props: string]: any;
  cachedData?: any;
  onDataChange?: (data: any) => void;
}

const service = API.sale.quotation.createQuotation;
const quotationTemplExService = API.sale.quotation.getQuotationTemplEx; //方案数据
const quotationTemplService = API.sale.quotationTempl.getOuotationTemplateById;
const quotationTemplService1700 = API.sale.quotationTempl.getQuotationTemplate1700ById;
const SalesProduct: React.FC<SalesProductProps> = forwardRef<SalesProductRef, SalesProductProps>(
  ({ initialValues, form, cachedData, onDataChange }, ref) => {
    const wriTable = useWritable({ service });
    const wriTable1 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 1 } });
    const [vatrDict, setVatrDict] = useState([]);
    const [atorDict, setAtorDict] = useState([]);
    const [curRow, setCurRow] = useState<POJO>({});
    const [quotationTypeFlag, setQuotationTypeFlag] = useState(false);
    const [salesModal, setSalesModal] = useState(false);
    const [oldWefareModal, setOldWefareModal] = useState(false);
    const [salesDis, setSalesDis] = useState(false);
    const [quotationItemType, setQuotationItemType] = useState(initialValues.quotationItemType);

    useEffect(() => {
      const { quotationItemType } = form.getFieldsValue() || initialValues;
      setQuotationItemType(quotationItemType);
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        // 获取当前数据方法
        getData: async () => {
          try {
            const formValues = form.getFieldsValue([
              'vatr',
              'ator',
              'atr',
              'salesPriceNoTax',
              'priceAt',
            ]);
            const filterFormValues = Object.keys(formValues).filter(
              (key) => formValues[key] !== undefined && formValues[key] !== null,
            );
            const [ladderResult, saleResult] = await Promise.all([
              wriTable.validateFields({ validateAll: true }),
              wriTable1.validateFields({ validateAll: true }),
            ]);
            const { visible: ladderVisible, all: ladderList } = ladderResult;
            const { visible: saleVisible, all: saleList } = saleResult;
            if (
              !isEmpty(filterFormValues) &&
              (saleVisible.length > 0 || ladderVisible.length > 0)
            ) {
              const formValues = await form.validateFields();
              const calculatePricesData = calculatePrices({
                calculateType: initialValues.calculateType,
                priceVat: formValues.priceAt,
                standardQuotation: formValues.salesPriceNoTax,
                vatr: formValues.vatr,
                ator: formValues.ator,
              });
              const saleResultData = saleVisible
                .filter((row: any) => row?.clientOperation !== -1)
                .map((item: any) => ({
                  ...item,
                  ladderQuotationType: quotationItemType,
                  quotationItemType,
                }));
              const ladderResultData =
                ladderVisible.filter((row: any) => row?.clientOperation !== -1) || [];
              return {
                quotationLadderList: ladderResultData,
                quotationItemDetailSaleList: saleResultData || [],
                ladderQuotationType: formValues.quotationItemType,
                ...calculatePricesData,
                salesPrice: calculatePricesData.priceAt,
                priceAt: calculatePricesData.salesPrice,
              };
            } else {
              return {};
            }
          } catch (error) {
            return {};
          }
        },
        // 重置数据方法
        resetData: () => {
          wriTable.setNewData([]);
          wriTable1.setNewData([]);
          form.resetFields(['priceAt', 'salesPriceNoTax', 'vatr', 'ator']);
        },
        validateData: async () => {
          const formValues = await form.getFieldsValue([
            'vatr',
            'ator',
            'atr',
            'salesPriceNoTax',
            'priceAt',
          ]);
          const filterFormValues = Object.keys(formValues).filter(
            (key) => formValues[key] !== undefined && formValues[key] !== null,
          );
          if (!isEmpty(filterFormValues) && filterFormValues.length < 5) {
            return true;
          }
          return false;
        },
      }),
      [wriTable, wriTable1, quotationItemType, onDataChange],
    );

    useEffect(() => {
      (async () => {
        setVatrDict(await handleGetDict('57'));
        setAtorDict(await handleGetDict('2002'));
      })();
    }, []);

    useEffect(() => {
      if (cachedData) {
        if (cachedData?.quotationLadderList) {
          wriTable.setNewData(cachedData?.quotationLadderList);
        }
        if (cachedData?.quotationItemDetailSaleList) {
          wriTable1.setNewData(cachedData?.quotationItemDetailSaleList);
        }
      }
    }, [cachedData?.quotationLadderList, cachedData?.quotationItemDetailSaleList]);

    useEffect(() => {
      if (
        (initialValues.saleDetailList && initialValues.saleDetailList.length > 0) ||
        (initialValues.quotationLadderList && initialValues.quotationLadderList.length > 0)
      ) {
        const data = initialValues.quotationLadderList || [];
        const data1 = initialValues.saleDetailList || [];
        wriTable.setNewData(data);
        wriTable1.setNewData(data1);
        const { vatr, ator, atr } = initialValues.saleDetailList[0] || {};
        form.setFieldsValue({
          vatr: String(vatr || 0),
          ator: String(ator || 0),
          atr: String(atr || 0),
        });
      }
    }, [initialValues.saleDetailList, initialValues.quotationLadderList]);

    const handleQuotationItemType = (value: string, outerForm: FormInstance) => {
      outerForm.resetFields(['priceAt', 'salesPriceNoTax']);
      setQuotationItemType(value);
      if (['3', '2'].includes(value)) {
        setSalesDis(true);
      } else {
        setSalesDis(false);
      }
    };
    const changeQuotationTempData = (data: any[]): any => {
      // 如果数据为空，返回空对象
      if (!data || data.length === 0) {
        return {};
      }
      const item = data[0];
      const fieldMapping: {
        [key: string]: {
          targetField: string;
          convertToString?: boolean;
        };
      } = {
        APPROVEPRICE: { targetField: 'approvePrice' },
        AREALIMIT: { targetField: 'areaLimit', convertToString: true },
        CREATEBY: { targetField: 'createBy' },
        CREATEBYNAME: { targetField: 'createByName' },
        CREATEDT: { targetField: 'createDt' },
        CUSTCODE: { targetField: 'custCode' },
        CUSTID: { targetField: 'custId' },
        CUSTNAME: { targetField: 'custName' },
        EFFECTIVEDT: { targetField: 'effectiveDt' },
        INVALIDDT: { targetField: 'invalidDt' },
        ISCITYDISCOUNT: { targetField: 'isCityDiscount', convertToString: true },
        ISDELETED: { targetField: 'isDeleted' },
        ISLIMITEDBYSVCHEADCOUNT: { targetField: 'isLimitedBySvcHeadcount', convertToString: true },
        ISLIMITEDBYSVCHEADCOUNTNAME: { targetField: 'isLimitedBySvcHeadcountName' },
        ISRELEASE: { targetField: 'isRelease' },
        MINSVCHEADCOUNT: { targetField: 'minSvcHeadCount' },
        MAXSVCHEADCOUNT: { targetField: 'maxSvcHeadCount' },
        PRODUCTLINEID: { targetField: 'productLineId', convertToString: true },
        QUOTATIONTEMPLTCODE: { targetField: 'quotationTempltCode' },
        QUOTATIONTEMPLTCOST: { targetField: 'quotationTempltCost' },
        QUOTATIONTEMPLTID: { targetField: 'quotationTempltId', convertToString: true },
        QUOTATIONTEMPLTNAME: { targetField: 'quotationTempltName' },
        QUOTATIONTEMPLTPRICE: { targetField: 'quotationTempltPrice' },
        QUOTATIONTEMPLTSCOPE: { targetField: 'quotationTempltScope', convertToString: true },
        QUOTATIONTEMPLTSCOPENAME: { targetField: 'quotationTempltScopeName' },
        QUOTATIONTEMPLTTYPE: { targetField: 'quotationTempltType', convertToString: true },
        QUOTATIONTEMPLTTYPENAME: { targetField: 'quotationTempltTypeName' },
        REMARK: { targetField: 'remark' },
        VALIDDTTYPE: { targetField: 'validDtType', convertToString: true },
        VALIDDTTYPENAME: { targetField: 'validDtTypeName' },
      };
      const resultList: any = {};
      // 遍历映射规则，应用到结果对象
      Object.entries(fieldMapping).forEach(([sourceField, config]) => {
        const { targetField, convertToString } = config;
        // 仅处理源对象中存在的字段
        if (item[sourceField] !== undefined) {
          // 根据配置决定是否需要转为字符串
          resultList[targetField] = convertToString ? String(item[sourceField]) : item[sourceField];
        }
      });

      return resultList;
    };
    const changeQuotationTempDataBatch = (dataArray: any[]): any[] => {
      if (!dataArray || dataArray.length === 0) {
        return [];
      }

      return dataArray.map((item) => changeQuotationTempData([item]));
    };

    const handleQuotationDetail = async (record: any) => {
      if (!record) {
        msgErr('无效的记录数据');
        return;
      }

      // 显示加载状态
      const loading = message.loading('正在加载详情...', 0);

      try {
        const templtType = record.quotationTempltType || record.templtType;
        const SALES_TEMPLATE_TYPES = ['1', '6', '3', '4'];
        if (SALES_TEMPLATE_TYPES.includes(String(templtType))) {
          // 获取模板ID（兼容不同的字段名）
          const templateId = record.templateId || record.quotationTempltId;

          if (!templateId) {
            throw new Error('未找到有效的模板ID');
          }

          // 请求详情数据
          const res = await quotationTemplService.requests({
            quotationTempltId: templateId,
          });

          if (!res || !res.list) {
            throw new Error('获取详情数据失败');
          }

          // 转换数据格式
          const detailData = changeQuotationTempDataBatch(res.list || []);

          if (!detailData || detailData.length === 0) {
            throw new Error('详情数据转换失败');
          }
          // 合并原始记录和详情数据，确保数据完整性
          const completeData = {
            ...record, // 基础记录数据
            ...detailData[0], // 详情数据
            templateId, // 确保ID字段一致
            quotationTempltId: templateId, // 保持两个ID字段同步
            type: 'VIEW', // 标记为查看模式
          };
          setCurRow(completeData);
          setTimeout(() => {
            setSalesModal(true);
          }, 0);
        } else {
          setCurRow({ ...record, type: 'VIEW' });
          setOldWefareModal(true);
        }
      } catch (error) {
        setSalesModal(false);
        setOldWefareModal(false);
      } finally {
        loading();
      }
    };
    const handlePrice = async (e: any, outerForm: FormInstance) => {
      const value = e.target.value;
      const { vatr, ator } = outerForm.getFieldsValue();
      const {
        standardQuotation,
        priceAt: salesPrice,
        vat,
        atrPrice,
      } = calculatePrices({
        calculateType: initialValues.calculateType,
        priceVat: value,
        vatr,
        ator,
      });

      outerForm.setFieldsValue({ salesPriceNoTax: standardQuotation, at: atrPrice });
      const {
        all: list,
        updated: updated,
        added: added,
      } = await wriTable1.validateFields({
        validateAll: true,
      });
      const data = calculateCostDistribution(list, 'productCost', [
        { value: value, resultKey: 'priceAt' },
        { value: standardQuotation, resultKey: 'salesPriceNoTax' },
        { value: salesPrice, resultKey: 'salesPrice' },
        { value: vat, resultKey: 'vat' },
        { value: atrPrice, resultKey: 'at' },
      ]);
      wriTable1.updateRows(data);
    };
    /**
     * 处理vatr(增值税率)值变更，确保所有相关数据实时更新
     * @param value 新的vatr值
     * @param outerForm 外部表单实例
     */
    const handleVatr = async (value: any, outerForm: FormInstance) => {
      try {
        // 1. 获取并更新外部表单数据
        const { priceAt, ator, salesPriceNoTax } = outerForm.getFieldsValue();

        // 计算主表单的新值
        const mainCalculatedValues = calculatePrices({
          calculateType: initialValues.calculateType,
          priceVat: priceAt,
          standardQuotation: salesPriceNoTax,
          vatr: value,
          ator,
          _source: 'vatr', // 标记触发源
        });

        const {
          standardQuotation,
          priceVat,
          priceAt: salesPrice,
          vatr,
          vat,
          atr,
          atrPrice,
        } = mainCalculatedValues;

        // 立即更新外部表单字段，不等待异步操作
        outerForm.setFieldsValue({
          salesPriceNoTax: standardQuotation,
          priceAt: priceVat,
          atr,
          vatr: value, // 确保vatr值被正确设置
        });

        // 2. 更新阶梯报价表格
        try {
          // 获取阶梯表格数据
          const { all: ladderList } = await wriTable.validateFields({
            validateAll: true,
          });
          const newLadderList = ladderList.filter((row: any) => row.clientOperation !== -1);

          // 对每一行进行计算
          const updatedLadderList = newLadderList.map((item: any) => {
            // 确保使用最新的vatr值
            const newItem = {
              ...item,
              vatr: value,
              atr,
            };

            // 计算新值
            const calculatedValues = calculatePrices({
              calculateType: initialValues.calculateType,
              priceVat: newItem.priceAt,
              standardQuotation: newItem.salesPriceNoTax,
              vatr: value, // 使用新的vatr值
              ator: newItem.ator,
              _source: 'vatr', // 标记触发源
            });

            // 合并计算结果
            return {
              ...newItem,
              ...calculatedValues,
              salesPrice: calculatedValues.priceAt,
              priceAt: calculatedValues.salesPrice,
            };
          });

          // 立即更新阶梯表格数据
          wriTable.setNewData(updatedLadderList);
        } catch (ladderError) {
          msgErr('更新税率计算失败，请重试');
        }

        // 3. 更新普通报价表格
        try {
          // 获取普通报价表格数据
          const { all: list } = await wriTable1.validateFields({
            validateAll: true,
          });

          // 对每一行进行计算
          const updatedList = list.map((item: any) => {
            // 确保使用最新的vatr值
            const newItem = {
              ...item,
              vatr: value,
              atr,
            };

            // 计算新值
            const calculatedValues = calculatePrices({
              calculateType: initialValues.calculateType,
              priceVat: newItem.priceAt,
              standardQuotation: newItem.salesPriceNoTax,
              vatr: value, // 使用新的vatr值
              ator: newItem.ator,
              _source: 'vatr', // 标记触发源
            });

            // 合并计算结果
            return {
              ...newItem,
              ...calculatedValues,
              salesPrice: calculatedValues.priceAt,
              priceAt: calculatedValues.salesPrice,
            };
          });

          // 计算成本分配
          const data = calculateCostDistribution(updatedList, 'productCost', [
            { value: priceVat, resultKey: 'priceAt' }, // 使用计算的新值
            { value: standardQuotation, resultKey: 'salesPriceNoTax' }, // 使用计算的新值
            { value: salesPrice, resultKey: 'salesPrice' },
            { value: vat, resultKey: 'vat' },
            { value: String(ator), resultKey: 'ator' },
            { value: String(value), resultKey: 'vatr' }, // 确保使用新的vatr值
            { value: atr, resultKey: 'atr' },
            { value: atrPrice, resultKey: 'at' },
          ]);

          // 立即更新普通报价表格数据
          if (data.length > 0) {
            wriTable1.setNewData(data);
          }
        } catch (listError) {
          msgErr('更新税率计算失败，请重试');
        }
      } catch (error) {
        msgErr('更新税率计算失败，请重试');
      }
    };
    /**
     * 处理ator(附加税率)值变更，确保所有相关数据实时更新
     * @param value 新的ator值
     * @param outerForm 外部表单实例
     */
    const handleAtor = async (value: any, outerForm: FormInstance) => {
      try {
        // 1. 获取当前表单数据
        const { priceAt, vatr, salesPriceNoTax } = outerForm.getFieldsValue();

        // 计算主表单的新值 - 使用传入的ator值和当前的vatr值
        const mainCalculatedValues = calculatePrices({
          calculateType: initialValues.calculateType,
          priceVat: priceAt,
          standardQuotation: salesPriceNoTax,
          vatr: vatr, // 使用当前的vatr值
          ator: value, // 使用新的ator值
          _source: 'ator', // 标记触发源为ator
        });

        const {
          standardQuotation,
          priceVat,
          priceAt: salesPrice,
          vatr: currentVatr,
          vat,
          atr,
          atrPrice,
        } = mainCalculatedValues;

        // 立即更新外部表单字段
        outerForm.setFieldsValue({
          salesPriceNoTax: standardQuotation,
          priceAt: priceVat,
          atr,
          ator: value, // 确保ator值被正确设置
        });

        // 2. 更新阶梯报价表格
        try {
          const { all: ladderList } = await wriTable.validateFields({
            validateAll: true,
          });

          const newLadderList = ladderList.filter((row: any) => row.clientOperation !== -1);

          const updatedLadderList = newLadderList.map((item: any) => {
            // 确保使用最新的ator值，但保留当前的vatr值
            const newItem = {
              ...item,
              ator: value, // 更新ator
              atr, // 更新计算后的atr
            };

            // 计算新值
            const calculatedValues = calculatePrices({
              calculateType: initialValues.calculateType,
              priceVat: newItem.priceAt,
              standardQuotation: newItem.salesPriceNoTax,
              vatr: newItem.vatr || currentVatr, // 使用项目自己的vatr或当前vatr
              ator: value, // 使用新的ator值
              _source: 'ator', // 标记触发源
            });

            // 合并计算结果
            return {
              ...newItem,
              ...calculatedValues,
              salesPrice: calculatedValues.priceAt,
              priceAt: calculatedValues.salesPrice,
            };
          });

          // 立即更新阶梯表格数据
          wriTable.setNewData(updatedLadderList);
        } catch (ladderError) {
          msgErr('更新附加税率计算失败，请重试');
        }

        // 3. 更新普通报价表格
        try {
          const { all: list } = await wriTable1.validateFields({
            validateAll: true,
          });

          const updatedList = list.map((item: any) => {
            // 确保使用最新的ator值，但保留当前的vatr值
            const newItem = {
              ...item,
              ator: value, // 更新ator
              atr, // 更新计算后的atr
            };

            // 计算新值
            const calculatedValues = calculatePrices({
              calculateType: initialValues.calculateType,
              priceVat: newItem.priceAt,
              standardQuotation: newItem.salesPriceNoTax,
              vatr: newItem.vatr || currentVatr, // 使用项目自己的vatr或当前vatr
              ator: value, // 使用新的ator值
              _source: 'ator', // 标记触发源
            });

            // 合并计算结果
            return {
              ...newItem,
              ...calculatedValues,
              salesPrice: calculatedValues.priceAt,
              priceAt: calculatedValues.salesPrice,
            };
          });

          // 计算成本分配
          const data = calculateCostDistribution(updatedList, 'productCost', [
            { value: priceVat, resultKey: 'priceAt' },
            { value: standardQuotation, resultKey: 'salesPriceNoTax' },
            { value: salesPrice, resultKey: 'salesPrice' },
            { value: vat, resultKey: 'vat' },
            { value: String(value), resultKey: 'ator' }, // 使用新的ator值
            { value: String(currentVatr), resultKey: 'vatr' }, // 使用当前vatr值
            { value: atr, resultKey: 'atr' },
            { value: atrPrice, resultKey: 'at' },
          ]);

          // 立即更新普通报价表格数据
          if (data.length > 0) {
            wriTable1.setNewData(data);
          }
        } catch (listError) {
          msgErr('更新附加税率计算失败，请重试');
        }
      } catch (error) {
        msgErr('更新附加税率计算失败，请重试');
      }
    };
    const handlePriceNoTax = async (e: any, outerForm: FormInstance) => {
      const value = e.target.value;
      const { vatr, ator, priceAt, salesPriceNoTax, salesPrice, vat, atrPrice } =
        outerForm.getFieldsValue();
      const calculatePricesData: any = calculatePrices({
        calculateType: initialValues.calculateType,
        standardQuotation: value,
        vatr,
        ator,
        _source: 'standardQuotation',
      });
      outerForm.setFieldsValue({ priceAt: calculatePricesData.priceVat });
      const {
        all: list,
        updated: updated,
        added: added,
      } = await wriTable1.validateFields({
        validateAll: true,
      });
      const data = calculateCostDistribution(list, 'productCost', [
        { value: calculatePricesData.priceVat, resultKey: 'priceAt' },
        { value: calculatePricesData.standardQuotation, resultKey: 'salesPriceNoTax' },
        { value: calculatePricesData.priceAt, resultKey: 'salesPrice' },
        { value: calculatePricesData.vat, resultKey: 'vat' },
        { value: Number(calculatePricesData.ator), resultKey: 'ator' },
        { value: Number(calculatePricesData.vatr), resultKey: 'vatr' },
        { value: Number(calculatePricesData.atr), resultKey: 'atr' },
        { value: Number(calculatePricesData.atrPrice), resultKey: 'at' },
      ]);
      wriTable1.setNewData(data);
    };
    /**处理阶梯计算统包价计算 */
    const handleSalesPrice = async (value: any, options: GeneralInputRenderOption<any>) => {
      const { all: list, updated: updated, added: added } = wriTable.getList();
      const currentRow: any = list.find((row: any) => row[tableIndexKey] === options.serial);
      if (!currentRow) return;
      if (!currentRow.beginNumber || !currentRow.endNumber) {
        msgErr('请先填写起始人数和截止人数');
        return;
      }
      const ladderPrice = calculateLadderPrice(
        currentRow,
        initialValues.suppltMedInsurHeadcount,
        quotationItemType,
      );
      const { standardQuotation, priceVat } = calculatePrices({
        calculateType: initialValues.calculateType,
        priceVat: ladderPrice,
        vatr: currentRow.vatr,
        ator: currentRow.ator,
      });
      ladderPrice &&
        (await form.setFieldsValue({ priceAt: priceVat, salesPriceNoTax: standardQuotation }));
      updateTableRowData(options, wriTable, 'priceAt', value, (item) => {
        const countNum = Number(item.countNum) || 1;
        const priceVat = Number(value) || 0;
        const totalPrice = Calculator.multiply(priceVat, countNum);
        const calculatePricesData = calculatePrices({
          calculateType: initialValues.calculateType,
          countNum: item.countNum,
          priceVat: value,
          totalPrice: totalPrice,
          vatr: item.vatr,
          ator: item.ator,
        });
        return {
          ...calculatePricesData,
          vatr: String(item.vatr || 0),
          ator: String(item.ator || 0),
          priceAt: calculatePricesData.salesPrice,
          salesPrice: calculatePricesData.priceAt,
        };
      });
      try {
        // 获取普通报价表格数据
        const { all: list } = await wriTable1.validateFields({
          validateAll: true,
        });

        // 对每一行进行计算
        const updatedList = list.map((item: any) => {
          // 确保使用最新的值
          const newItem = {
            ...item,
            priceAt: value,
          };

          // 计算新值
          const calculatedValues = calculatePrices({
            calculateType: initialValues.calculateType,
            priceVat: newItem.priceAt,
            standardQuotation: newItem.salesPriceNoTax,
          });

          // 合并计算结果
          return {
            ...newItem,
            ...calculatedValues,
            salesPrice: calculatedValues.priceAt,
            priceAt: calculatedValues.salesPrice,
          };
        });
        // 获取表单字段值
        const { vatr, ator, priceAt: priceAtVal, salesPriceNoTax } = form.getFieldsValue();
        const {
          atrPrice,
          vat,
          salesPrice: priceAt,
          priceAt: salesPrice,
          atr,
        } = calculatePrices({
          calculateType: initialValues.calculateType,
          priceVat: priceAtVal,
          standardQuotation: salesPriceNoTax,
          vatr: vatr,
          ator: ator,
        });
        // 计算成本分配
        const data = calculateCostDistribution(updatedList, 'productCost', [
          { value: priceAt, resultKey: 'priceAt' },
          { value: salesPriceNoTax, resultKey: 'salesPriceNoTax' },
          { value: salesPrice, resultKey: 'salesPrice' },
          { value: vat, resultKey: 'vat' },
          { value: String(ator), resultKey: 'ator' },
          { value: String(vatr), resultKey: 'vatr' },
          { value: atr, resultKey: 'atr' },
          { value: atrPrice, resultKey: 'at' }, // 使用正确计算的附加税金额
        ]);

        // 立即更新普通报价表格数据
        if (data.length > 0) {
          wriTable1.setNewData(data);
        }
      } catch (error) {
        msgErr('更新税率计算失败，请重试');
      }
    };
    /**处理阶梯不含税价计算 */
    const handleSalesPriceNoTax = async (value: any, options: GeneralInputRenderOption<any>) => {
      const { all: list, updated: updated, added: added } = wriTable.getList();
      const currentRow: any = list.find((row: any) => row[tableIndexKey] === options.serial);
      if (!currentRow) return;
      if (!currentRow.beginNumber || !currentRow.endNumber) {
        msgErr('请先填写起始人数和截止人数');
        return;
      }
      const ladderPrice = calculateLadderPrice(
        currentRow,
        initialValues.suppltMedInsurHeadcount,
        quotationItemType,
      );
      const { standardQuotation, priceVat } = calculatePrices({
        calculateType: initialValues.calculateType,
        standardQuotation: ladderPrice,
        vatr: currentRow.vatr,
        ator: currentRow.ator,
        _source: 'standardQuotation',
      });
      ladderPrice && form.setFieldsValue({ priceAt: priceVat, salesPriceNoTax: standardQuotation });
      updateTableRowData(options, wriTable, 'salesPriceNoTax', value, (item) => {
        const calculatePricesData = calculatePrices({
          calculateType: initialValues.calculateType,
          countNum: item.countNum,
          standardQuotation: value,
          vatr: item.vatr,
          ator: item.ator,
          _source: 'standardQuotation',
        });
        return {
          ...calculatePricesData,
          vatr: String(item.vatr || 0),
          ator: String(item.ator || 0),
          priceAt: calculatePricesData.salesPrice,
          salesPrice: calculatePricesData.priceAt,
        };
      });
      try {
        // 获取普通报价表格数据
        const { all: list } = await wriTable1.validateFields({
          validateAll: true,
        });

        // 对每一行进行计算
        const updatedList = list.map((item: any) => {
          // 确保使用最新的值
          const newItem = {
            ...item,
            salesPriceNoTax: value,
          };

          // 计算新值
          const calculatedValues = calculatePrices({
            calculateType: initialValues.calculateType,
            priceVat: newItem.priceAt,
            standardQuotation: newItem.salesPriceNoTax,
            _source: 'standardQuotation',
          });

          // 合并计算结果
          return {
            ...newItem,
            ...calculatedValues,
            salesPrice: calculatedValues.priceAt,
            priceAt: calculatedValues.salesPrice,
          };
        });
        // 获取表单字段值
        const { vatr, ator, priceAt: priceAtVal, salesPriceNoTax } = form.getFieldsValue();
        const {
          atrPrice,
          vat,
          salesPrice: priceAt,
          priceAt: salesPrice,
          atr,
        } = calculatePrices({
          calculateType: initialValues.calculateType,
          priceVat: priceAtVal,
          standardQuotation: salesPriceNoTax,
          vatr: vatr,
          ator: ator,
          _source: 'standardQuotation',
        });
        // 计算成本分配
        const data = calculateCostDistribution(updatedList, 'productCost', [
          { value: priceAt, resultKey: 'priceAt' },
          { value: salesPriceNoTax, resultKey: 'salesPriceNoTax' },
          { value: salesPrice, resultKey: 'salesPrice' },
          { value: vat, resultKey: 'vat' },
          { value: String(ator), resultKey: 'ator' },
          { value: String(vatr), resultKey: 'vatr' },
          { value: atr, resultKey: 'atr' },
          { value: atrPrice, resultKey: 'at' }, // 使用正确计算的附加税金额
        ]);

        // 立即更新普通报价表格数据
        if (data.length > 0) {
          wriTable1.setNewData(data);
        }
      } catch (error) {
        msgErr('更新税率计算失败，请重试');
      }
    };
    /**处理阶梯起始人数计算 */
    const handleBeginNumber = async (value: any, options: GeneralInputRenderOption<any>) => {
      const { all: list, updated: updated, added: added } = wriTable.getList();
      // Get current row data
      const currentRow: any = list.find((row: any) => row[tableIndexKey] === options.serial);
      if (!currentRow) return;

      // Check if end number is greater than start number
      if (
        currentRow.beginNumber &&
        currentRow.endNumber &&
        Number(currentRow.beginNumber) >= Number(currentRow.endNumber)
      ) {
        // 如果起始人数大于等于截止人数，则提示错误 , 并且清空当前截止人数
        wriTable.updateRows({
          [tableIndexKey]: currentRow[tableIndexKey],
          endNumber: '',
        });
        msgErr('截止人数必须大于起始人数');
        return;
      }

      // Get all other rows (excluding current row)
      const otherRows = list.filter((row: any) => row[tableIndexKey] !== options.serial);

      // 检查是否修改唯一的起始人数为1的记录
      const hasOtherRowStartingWithOne = otherRows.some(
        (row: any) => Number(row.beginNumber) === 1,
      );

      // 如果当前行原来起始人数是1，现在要修改为其他值，且没有其他行的起始人数为1，则阻止修改
      if (
        Number(currentRow.beginNumber) === 1 &&
        Number(value) !== 1 &&
        !hasOtherRowStartingWithOne
      ) {
        msgErr('阶梯报价列表中必须至少有一条起始人数为1的记录');
        // 恢复为1
        wriTable.setFieldsValue(options.serial, {
          beginNumber: '1',
        });
        return;
      }

      // Sort rows by beginNumber for continuity check
      const allRows = [...otherRows, { ...currentRow, beginNumber: value }].sort(
        (a, b) => Number(a.beginNumber) - Number(b.beginNumber),
      );

      // 检查是否存在起始人数为1的记录
      const hasRowStartingWithOne = allRows.some((row) => Number(row.beginNumber) === 1);
      if (!hasRowStartingWithOne) {
        msgErr('阶梯报价列表中必须至少有一条起始人数为1的记录');

        // 如果是添加第一条记录，自动设置为1
        if (list.length === 1 && Number(value) !== 1) {
          wriTable.setFieldsValue(options.serial, {
            beginNumber: '1',
          });
        } else {
          // 恢复原值或拒绝修改
          wriTable.setFieldsValue(options.serial, {
            beginNumber: currentRow.beginNumber,
          });
        }
        return;
      }
      const filteredRows = allRows.filter((row) => row.clientOperation !== -1);

      // Check for overlapping ranges
      for (let i = 0; i < filteredRows.length - 1; i++) {
        if (Number(filteredRows[i].endNumber) >= Number(filteredRows[i + 1].beginNumber)) {
          msgErr('人数区间不能重叠');
          return;
        }
      }

      for (let i = 0; i < filteredRows.length - 1; i++) {
        if (Number(filteredRows[i].endNumber) + 1 !== Number(filteredRows[i + 1].beginNumber)) {
          msgErr('阶梯人数必须连续');
          return;
        }
      }
    };
    /**
     * 处理截止人数变更，并重新计算阶梯价格数据
     *
     * @param value 截止人数值
     * @param options 行选项
     */
    const handleEndNumber = async (value: any, options: GeneralInputRenderOption<any>) => {
      const { all: list } = await wriTable.getList();
      const currentRow: any = list.find((row: any) => row[tableIndexKey] === options.serial);
      if (!currentRow) return;

      // 验证截止人数必须大于起始人数
      if (
        currentRow.beginNumber &&
        currentRow.endNumber &&
        Number(currentRow.beginNumber) >= Number(currentRow.endNumber)
      ) {
        await wriTable.updateRows({
          [tableIndexKey]: currentRow[tableIndexKey],
          endNumber: '',
        });
        msgErr('截止人数必须大于起始人数');
        return;
      }

      // 计算阶梯价格
      const ladderPrice = calculateLadderPrice(
        currentRow,
        initialValues.suppltMedInsurHeadcount,
        quotationItemType,
      );

      const newCurrentData: any = {
        calculateType: initialValues.calculateType,
        vatr: currentRow.vatr,
        ator: currentRow.ator,
      };
      if (initialValues.calculateType === '2') {
        newCurrentData.standardQuotation = ladderPrice;
      } else {
        newCurrentData.priceVat = ladderPrice;
      }
      const { standardQuotation, priceVat } = calculatePrices(newCurrentData);
      // 更新表单字段
      ladderPrice &&
        (await form.setFieldsValue({ priceAt: priceVat, salesPriceNoTax: standardQuotation }));

      // 更新表格行数据
      updateTableRowData(options, wriTable, 'endNumber', value, (item) => {
        const countNum = Number(item.countNum) || 1;
        const priceVat = Number(item.priceAt) || 0;
        const totalPrice = Calculator.multiply(priceVat, countNum);
        const calculatePricesData = calculatePrices({
          calculateType: initialValues.calculateType,
          countNum: item.countNum,
          priceVat: item.priceAt,
          totalPrice: totalPrice,
          vatr: item.vatr,
          ator: item.ator,
        });
        return {
          ...calculatePricesData,
          vatr: String(item.vatr || 0),
          ator: String(item.ator || 0),
          priceAt: calculatePricesData.salesPrice,
          salesPrice: calculatePricesData.priceAt,
          endNumber: value,
        };
      });

      try {
        // 获取普通报价表格数据
        const { all: list } = await wriTable1.validateFields({
          validateAll: true,
        });

        // 对每一行进行计算
        const updatedList = list.map((item: any) => {
          // 确保使用最新的值
          const newItem = {
            ...item,
            endNumber: value,
          };

          // 计算新值
          const calculatedValues = calculatePrices({
            calculateType: initialValues.calculateType,
            priceVat: newItem.priceAt,
            standardQuotation: newItem.salesPriceNoTax,
          });

          // 合并计算结果
          return {
            ...newItem,
            ...calculatedValues,
            salesPrice: calculatedValues.priceAt,
            priceAt: calculatedValues.salesPrice,
          };
        });
        // 获取表单字段值
        const { vatr, ator, priceAt: priceAtVal, salesPriceNoTax } = form.getFieldsValue();
        const newCurrentData2: any = {
          calculateType: initialValues.calculateType,
          priceVat: priceAtVal,
          standardQuotation: salesPriceNoTax,
          vatr: vatr,
          ator: ator,
        };
        if (initialValues.calculateType === '2') {
          newCurrentData2._source = 'standardQuotation';
        }
        const {
          atrPrice,
          vat,
          salesPrice: priceAt,
          priceAt: salesPrice,
          atr,
        } = calculatePrices(newCurrentData2);
        // 计算成本分配
        const data = calculateCostDistribution(updatedList, 'productCost', [
          { value: priceAt, resultKey: 'priceAt' },
          { value: salesPriceNoTax, resultKey: 'salesPriceNoTax' },
          { value: salesPrice, resultKey: 'salesPrice' },
          { value: vat, resultKey: 'vat' },
          { value: String(ator), resultKey: 'ator' },
          { value: String(vatr), resultKey: 'vatr' },
          { value: atr, resultKey: 'atr' },
          { value: atrPrice, resultKey: 'at' },
        ]);

        // 立即更新普通报价表格数据
        if (data.length > 0) {
          wriTable1.setNewData(data);
        }
      } catch (error) {
        msgErr('更新税率计算失败，请重试');
      }
    };
    const formColumns: EditeFormProps[] = [
      //普通报价方式 form
      {
        label: '销售价格(含增值税附加税)',
        fieldName: 'priceAt',
        inputRender: (outerForm: FormInstance) => {
          const { quotationItemType } = outerForm.getFieldsValue();
          return (
            <InputNumber
              onBlur={(e) => handlePrice(e, outerForm)}
              disabled={
                initialValues.calculateType === '2' ||
                ['3', '2'].includes(quotationItemType) ||
                quotationTypeFlag
              }
            />
          );
        },
        rules: [
          {
            required: !['3', '2'].includes(quotationItemType),
            message: '请输入销售价格(含增值税附加税)',
          },
          {
            pattern: new RegExp(/^\d+(\.\d{0,5})?$/, 'g'),
            message: '只能输入大于等于0数字，且保留五位小数',
          },
          {
            validator: (rule: any, value: any) => {
              const { visible: data } = wriTable1.getList();
              const quotationTempltType = data?.[0]?.templtType;
              if (data.length === 1 && quotationTempltType === '6') {
                return Promise.resolve();
              }
              if (
                (initialValues.calculateType === '1' || !['3', '2'].includes(quotationItemType)) &&
                value <= 0
              ) {
                return Promise.reject('销售价格(含增值税附加税)必须大于0');
              }
              return Promise.resolve();
            },
          },
        ],
        shouldUpdate: (prevValues, curValues) => {
          return prevValues.quotationItemType !== curValues.quotationItemType;
        },
      },
      {
        label: '不含税价',
        fieldName: 'salesPriceNoTax',
        inputRender: (outerForm: FormInstance) => {
          const { quotationItemType } = outerForm.getFieldsValue();
          return (
            <InputNumber
              onBlur={(e) => handlePriceNoTax(e, outerForm)}
              disabled={
                initialValues.calculateType === '1' ||
                ['3', '2'].includes(quotationItemType) ||
                quotationTypeFlag
              }
            />
          );
        },
        rules: [
          {
            required: !['3', '2'].includes(quotationItemType),
            message: '请输入不含税价',
          },
          {
            pattern: new RegExp(/^\d+(\.\d{0,5})?$/, 'g'),
            message: '只能输入大于等于0数字，且保留五位小数',
          },
          {
            validator: (rule: any, value: any) => {
              const { visible: data } = wriTable1.getList();
              const quotationTempltType = data?.[0]?.templtType;
              if (data.length === 1 && quotationTempltType === '6') {
                return Promise.resolve();
              }
              if (
                (initialValues.calculateType === '1' || !['3', '2'].includes(quotationItemType)) &&
                value <= 0
              ) {
                return Promise.reject('不含税价必须大于0');
              }
              return Promise.resolve();
            },
          },
        ],
        shouldUpdate: (prevValues, curValues) => {
          return prevValues.quotationItemType !== curValues.quotationItemType;
        },
      },
      {
        label: '报价方式',
        fieldName: 'quotationItemType',
        inputRender: (outerForm: FormInstance) =>
          mapToSelectors(quoteTyMap, {
            onChange: (value: any) => handleQuotationItemType(value, outerForm),
            disabled: quotationTypeFlag,
            allowClear: false,
          }),
      },
      {
        label: '增值税率%',
        fieldName: 'vatr',
        inputRender: (outerForm: FormInstance) =>
          renderListToSelectors(vatrDict, ['key', 'shortName'], {
            placeholder: '请选择',
            onChange: (value: any) => handleVatr(value, outerForm),
          }),
        rules: [{ required: true, message: '请输入增值税率%' }],
      },
      {
        label: '附加税的税率%',
        fieldName: 'ator',
        inputRender: (outerForm: FormInstance) =>
          renderListToSelectors(atorDict, ['key', 'shortName'], {
            placeholder: '请选择',
            onChange: (value: any) => handleAtor(value, outerForm),
          }),
        rules: [{ required: true, message: '请输入附加税的税率' }],
      },
      {
        label: '附加税率%',
        fieldName: 'atr',
        inputProps: { disabled: true },
        inputRender: (outerForm: FormInstance) => {
          const { vatr, ator, atr } = outerForm.getFieldsValue();
          const atrValue = formatPercentTruncated(atr, 2, true);
          // Update the form field value when dependencies change
          React.useEffect(() => {
            if (ator && vatr) {
              outerForm.setFieldsValue({ atr: atrValue });
            }
          }, [vatr, ator, outerForm]);

          return <InputNumber value={atr} disabled />;
        },
        rules: [{ required: true, message: '附加税率必填' }],
        shouldUpdate: (prevValues, curValues) => {
          return prevValues.vatr !== curValues.vatr || prevValues.ator !== curValues.ator;
        },
      },
    ];
    /**
     * 普通方案售价 正常报价
     */
    const normalColumns: WritableColumnProps<any>[] = [
      { title: '普通销售方案', dataIndex: 'quotationTempltName' },
      { title: '标准价格', dataIndex: 'productPrice' },
      {
        title: '销售价格(含增值税附加税)',
        dataIndex: 'priceAt',
      },
      {
        title: '销售价格(不含税)',
        dataIndex: 'salesPriceNoTax',
        rules: [{ required: true, message: '请填写销售价格(不含税)' }],
      },
      {
        title: '增值税率%',
        dataIndex: 'vatr',
        render: (v) => formatPercentTruncated(v, 2, true),
      },
      {
        title: '附加税的税率%',
        dataIndex: 'ator',
        render: (v) => formatPercentTruncated(v, 2, true),
      },
      {
        title: '附加税率%',
        dataIndex: 'atr',
        render: (v) => formatPercentTruncated(v, 2, true),
      },
      { title: '销售价格（含税）', dataIndex: 'salesPrice', inputProps: { disabled: true } },
      { title: '增值税', dataIndex: 'vat', inputProps: { disabled: true } },
      {
        title: '方案明细',
        dataIndex: 'schemeDetail',
        inputRender: (options: any) => {
          const data = options.record;
          return <Link onClick={() => handleQuotationDetail(data)}>查看</Link>;
        },
      },
    ];
    const ladderColumns: WritableColumnProps<any>[] = [
      //普通下面 人数form
      {
        title: '起始人数',
        dataIndex: 'beginNumber',
        inputRender: (options: GeneralInputRenderOption<any>) => {
          return ['VIEW', 'ADOPT'].includes(initialValues.type) ? (
            <InputNumber readOnly />
          ) : (
            <InputNumber onBlur={(e) => handleBeginNumber(e.target.value, options)} />
          );
        },
        rules: [
          { required: true, message: '请填写起始人数' },
          {
            pattern: new RegExp(/^(([1-9]\d+)|([1-9]))$/, 'g'),
            message: '只能输入大于1的数字',
          },
        ],
      },
      {
        title: '截止人数',
        dataIndex: 'endNumber',
        inputRender: (options: GeneralInputRenderOption<any>) => {
          return ['VIEW', 'ADOPT'].includes(initialValues.type) ? (
            <InputNumber readOnly />
          ) : (
            <InputNumber onBlur={(e) => handleEndNumber(e.target.value, options)} />
          );
        },
        rules: [
          { required: true, message: '请填写截止人数' },
          {
            pattern: new RegExp(/^(([1-9]\d+)|([1-9]))$/, 'g'),
            message: '只能输入大于1的数字',
          },
        ],
      },
      {
        title: '销售价格(含增值税附加税)',
        dataIndex: 'priceAt',
        inputRender: (options: GeneralInputRenderOption<any>) => {
          return ['VIEW', 'ADOPT'].includes(initialValues.type) ||
            initialValues.calculateType !== '1' ? (
            <Input readOnly />
          ) : (
            <NumericInput
              maxDecimalPlaces={5}
              allowNegative={false}
              onBlur={(e) => handleSalesPrice(e.target.value, options)}
            />
          );
        },
        rules: [
          {
            required: true,
            message: '请输入标准售价(含增值税附加税)',
          },
        ],
      },
      {
        title: '不含税价',
        dataIndex: 'salesPriceNoTax',
        inputRender: (options: GeneralInputRenderOption<any>) => {
          return ['VIEW', 'ADOPT'].includes(initialValues.type) ||
            initialValues.calculateType !== '2' ? (
            <Input readOnly />
          ) : (
            <NumericInput
              maxDecimalPlaces={5}
              allowNegative={false}
              onBlur={(e) => handleSalesPriceNoTax(e.target.value, options)}
            />
          );
        },
        rules: [{ required: true, message: '请输入不含税价' }],
      },
      {
        title: '增值税率%',
        dataIndex: 'vatr',
        render: (v) => formatPercentTruncated(v, 2, true),
      },
      {
        title: '附加税的税率%',
        dataIndex: 'ator',
        render: (v) => formatPercentTruncated(v, 2, true),
      },
      {
        title: '附加税率%',
        dataIndex: 'atr',
        render: (v) => formatPercentTruncated(v, 2, true),
      },
      {
        title: '销售价格(含附加税)',
        dataIndex: 'salesPrice',
        inputProps: { disabled: true },
      },
      { title: '增值税', dataIndex: 'vat', inputProps: { disabled: true } },
    ];
    const handleLadderCol = async () => {
      const value = await form.getFieldsValue([
        'priceAt',
        'salesPriceNoTax',
        'vatr',
        'ator',
        'atr',
        'quotationItemType',
      ]);
      // 如果ator 是百分比，则转换为小数
      if (value.atr?.toString()?.includes('%')) {
        value.atr = parseFloat(value.atr?.toString()?.replace('%', '')) / 100;
      }
      wriTable.addRows({
        vatr: value.vatr,
        ator: value.ator,
        atr: value.atr,
        quotationItemType: value.quotationItemType,
        ladderQuotationType: value.quotationItemType,
        productId: '101',
        beginNumber: '',
        endNumber: '',
        priceAt: '',
        salesPriceNoTax: '',
        salesPrice: '',
        vat: '',
      });
    };
    const renderladderQuote = () => {
      //阶梯总价 or 阶梯人均
      return (
        <>
          <RowElement>
            <ColElementButton>
              <Button
                key="addLadder"
                type="primary"
                onClick={handleLadderCol}
                disabled={['VIEW', 'ADOPT'].includes(initialValues.type)}
              >
                新增
              </Button>
              <Button
                key="cancelLadder"
                onClick={() => {
                  preciseDeleteRows(wriTable.selectedRows, wriTable);
                }}
                disabled={['VIEW', 'ADOPT'].includes(initialValues.type)}
              >
                删除
              </Button>
            </ColElementButton>
          </RowElement>
          <Writable
            service={service}
            columns={ladderColumns}
            notShowPagination
            noDeleteButton
            noAddButton
            wriTable={wriTable}
          />
        </>
      );
    };
    const PopColumns: EditeFormProps[] = [
      {
        label: '方案名称',
        fieldName: 'quotationTempltName',
        inputRender: 'string',
      },
    ];

    const prodTableColumns: WritableColumnProps<any>[] = [
      { title: '方案名称', dataIndex: 'quotationTempltName' },
      { title: '方案类型', dataIndex: 'quotationTempltTypeName' },
      { title: '报价单方案编号', dataIndex: 'quotationTempltCode' },
      { title: '产品线', dataIndex: 'productLineName' },
      {
        title: '是否存在有效期限',
        dataIndex: 'validDtTypeName',
      },
      { title: '生效日期', dataIndex: 'effectiveDt' },
      { title: '失效日期', dataIndex: 'invalidDt' },
      { title: '服务人数限制', dataIndex: 'isLimitedBySvcHeadcountName' },
      { title: '最小服务人数', dataIndex: 'minSvcHeadCount' },
      { title: '最大服务人数', dataIndex: 'maxSvcHeadCount' },
      { title: '创建时间', dataIndex: 'createDt' },
      { title: '创建者', dataIndex: 'createByName' },
      { title: '方案适用客户范围', dataIndex: 'quotationTempltScopeName' },
      { title: '设置方案区域', dataIndex: 'areaLimitName' },
    ];
    const handleAddQuotationTemplEx = () => {
      return (
        <>
          <RowElement>
            <ColElementButton>
              <ProductPop
                rowValue="productId-productName-productLineId-productLineName"
                keyMap={{
                  productId: 'productId',
                  productName: 'productName',
                  productLineId: 'productLineId',
                  productLineName: 'productLineName',
                }}
                modalTitle="选择方案名称"
                formColumns={PopColumns}
                columns={prodTableColumns}
                rowKey="quotationTempltId"
                service={quotationTemplExService}
                staticData={[]}
                fixedValues={{
                  quotationTempltType: '1,6',
                  suppltMedInsurHeadcount: initialValues.suppltMedInsurHeadcount,
                  newSaleId: initialValues.newSaleId,
                  custId: initialValues.custId,
                }}
                handdleMultiConfirm={handdleMultiConfirmProduct}
              >
                <Button
                  type="primary"
                  disabled={
                    ['VIEW', 'ADOPT'].includes(initialValues.type) ||
                    Object.keys(
                      pickData(
                        form.getFieldsValue(['priceAt', 'salesPriceNoTax', 'vatr', 'ator', 'atr']),
                      ),
                    ).length < 4 ||
                    quotationTypeFlag
                  }
                >
                  添加
                </Button>
              </ProductPop>
              <Button
                disabled={['VIEW', 'ADOPT'].includes(initialValues.type)}
                onClick={() => {
                  handleDeleteQuotationTemplEx();
                  setQuotationTypeFlag(false);
                  wriTable1.deleteRows();
                  wriTable1.resetFields();
                }}
              >
                删除
              </Button>
            </ColElementButton>
          </RowElement>
          <Writable
            service={{ ...service, cacheKey: service.cacheKey + 1 }}
            columns={normalColumns}
            notShowPagination
            noDeleteButton
            noAddButton
            wriTable={wriTable1}
          />
        </>
      );
    };
    const handdleMultiConfirmProduct = async (products?: defs.sale.quotationTemplDTO[] | any) => {
      // 判断是否选择了多个外包项目模板
      const formValue = await form.getFieldsValue();
      const { visible: currentList } = await wriTable1.getList();
      const duplicates = currentList.map((item) => item).filter(Boolean);
      const uniqueData = mergeUniqueData(duplicates, products);
      const outsourcingTemplates = uniqueData?.filter(
        (item: any) => item.quotationTempltType === '6',
      );
      const types = uniqueData.map((item) => item.quotationTempltType).filter(Boolean);
      const hasDifferentTypes = new Set(types).size > 1;
      if (hasDifferentTypes) {
        msgErr('请选择相同类型的方案模板');
        return;
      }
      if (outsourcingTemplates && outsourcingTemplates.length > 1) {
        return msgErr('外包项目的普通销售方案仅能选择一个模板');
      }
      if (products && products.length > 1) {
        const templateTypes = products.map((item: any) => item.quotationTempltType);
        const uniqueTypes = [...new Set(templateTypes)];

        if (uniqueTypes.length > 1) {
          return msgErr('请选择相同类型的方案模板');
        }
      }

      if (
        outsourcingTemplates &&
        outsourcingTemplates.length > 1 &&
        uniqueData.some((item: any) => item.quotationTempltType === '6')
      ) {
        return msgErr('有岗位外包模版就不允许添加其它的类型的模版');
      }
      // //判断 outsourcingTemplates.length ===1 情况下岗位外包情况下 报价方式disabled
      if (uniqueData.every((item: any) => item.quotationTempltType === '6')) {
        await form.setFieldsValue({
          quotationItemType: '1',
          priceAt: 0,
          salesPriceNoTax: 0,
        });
        setQuotationTypeFlag(true);
        setQuotationItemType('1');
        setSalesDis(false);
        wriTable.setNewData([]);
        const data = uniqueData.map((item: any) => {
          return {
            ...item,
            ...formValue,
            priceAt: 0,
            salesPriceNoTax: 0,
            salesPrice: 0,
            vat: 0,
            productPrice: 0,
            atr: formValue.ator * formValue.vatr,
            at: 0,
          };
        });
        wriTable1.setNewData(data);
      } else {
        setQuotationTypeFlag(false);
        setSalesDis(false);
        const { priceAt, salesPriceNoTax, ator, vatr } = formValue;
        const calculatePricesData = calculatePrices({
          calculateType: initialValues.calculateType,
          priceVat: priceAt,
          standardQuotation: salesPriceNoTax,
          vatr,
          ator,
          _source: 'standardQuotation',
        });
        const data = calculateCostDistribution(uniqueData, 'productCost', [
          { value: calculatePricesData.priceVat, resultKey: 'priceAt' },
          { value: calculatePricesData.standardQuotation, resultKey: 'salesPriceNoTax' },
          { value: calculatePricesData.priceAt, resultKey: 'salesPrice' },
          { value: calculatePricesData.vat, resultKey: 'vat' },
          { value: Number(calculatePricesData.ator), resultKey: 'ator' },
          { value: Number(calculatePricesData.vatr), resultKey: 'vatr' },
          { value: Number(calculatePricesData.atr), resultKey: 'atr' },
          { value: Number(calculatePricesData.atrPrice), resultKey: 'at' },
        ]);
        wriTable1.setNewData(data);
      }
    };

    const handleDeleteQuotationTemplEx = async () => {
      const formValue = await form.getFieldsValue();
      const { visible: currentList, selected } = (await wriTable1.getList()) as any;
      const uniqueData = filterOutSelectedRows(currentList, selected, 'templateId');
      const { priceAt, salesPriceNoTax, ator, vatr } = formValue;
      const calculatePricesData = calculatePrices({
        calculateType: initialValues.calculateType,
        priceVat: priceAt,
        standardQuotation: salesPriceNoTax,
        vatr,
        ator,
        _source: initialValues.calculateType === '2' ? 'standardQuotation' : '',
      });
      const data = calculateCostDistribution(uniqueData, 'productCost', [
        { value: calculatePricesData.priceVat, resultKey: 'priceAt' },
        { value: calculatePricesData.standardQuotation, resultKey: 'salesPriceNoTax' },
        { value: calculatePricesData.priceAt, resultKey: 'salesPrice' },
        { value: calculatePricesData.vat, resultKey: 'vat' },
        { value: Number(calculatePricesData.ator), resultKey: 'ator' },
        { value: Number(calculatePricesData.vatr), resultKey: 'vatr' },
        { value: Number(calculatePricesData.atr), resultKey: 'atr' },
        { value: Number(calculatePricesData.atrPrice), resultKey: 'at' },
      ]);
      wriTable1.setNewData(data);
    };
    return (
      <>
        <EnumerateFields
          outerForm={form}
          formColumns={formColumns}
          colNumber={3}
          disabled={['VIEW', 'ADOPT'].includes(initialValues.type)}
        />
        {quotationItemType !== '1' && renderladderQuote()}
        {handleAddQuotationTemplEx()}
        <OrdinarySales modal={[salesModal, setSalesModal]} initialValues={curRow} width={1200} />
        <Codal
          visible={oldWefareModal}
          title="福利方案"
          width={1200}
          onCancel={() => setOldWefareModal(false)}
          destroyOnClose
        >
          <OldWefareProduct initialValues={curRow} />
        </Codal>
      </>
    );
  },
);

export default SalesProduct;
