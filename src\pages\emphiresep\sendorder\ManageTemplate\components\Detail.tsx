import React, { useEffect, useState } from 'react';
import Codal from '@/components/Codal';
import { Button, Form } from 'antd';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm/index';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { CommonBaseDataSelector, mapToSelectors } from '@/components/Selectors';
import {
  yesNoMap,
  receiveMonthListMap,
  invoiceTypeMap,
} from '@/utils/settings/emphiresep/sendorder/ManageTemplate';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { msgErr, msgOk } from '@/utils/methods/message';
import CustContract from './CustContract';
import { tableLineStateMap } from '@/utils/settings/forms';
import { AsyncButton } from '@/components/Forms/Confirm';
import { ContractSelectPop } from '@/components/StandardPop/ContractSelectPop';
import { BankInfoSelectPop } from '@/components/StandardPop/BankInfoSelectPop';
import { getUserName } from '@/utils/model';

export type TMode = 'ADD' | 'UPDATE' | 'VIEW';

interface DetailProps {
  visible: boolean;
  mode: TMode;
  data?: POVO;
  hideHandle: (refresh?: boolean) => void;
}

const Detail = (props: DetailProps) => {
  const [form] = Form.useForm();

  const kNumberRegex = /^[0-9]*$/;

  const [custPayerMap, setCustPayerMap] = useState<Map<string, string>>(new Map()); // 付款方数据
  const [providerMap, setProviderMap] = useState<Map<string, string>>(new Map()); // 账单方数据
  const [contractVisible, setContractVisible] = useState<boolean>(false);
  const [agreedPayDtIsRequire, setAgreedPayDtIsRequire] = useState<boolean>(false);
  const [custData, setCustData] = useState<POJO>({});
  const [payeeData, setPayeeData] = useState<POJO>({});
  const userName = getUserName();

  const disabled = props.mode === 'VIEW';

  const formColumns: EditeFormProps[] = [
    {
      label: '客户名称',
      fieldName: 'custName',
      inputProps: {
        disabled: disabled || props.mode === 'UPDATE',
      },
      inputRender: () => (
        <CustomerPop
          modalTitle="选择客户"
          title="客户名称"
          outerForm={form}
          rowValue="custId-custCode-custName"
          keyMap={{
            custId: 'custId',
            custCode: 'custCode',
            custName: 'custName',
          }}
          fixedValues={{ queryType: 2 }}
          handdleConfirm={(values) => {
            setCustData(values || {});
            custChanged(values?.custId);
          }}
        />
      ),
      formOptions: {
        rules: [{ required: true, message: '请选中客户名称' }],
      },
    },
    {
      label: '大合同名称',
      fieldName: 'contractName',
      inputProps: {
        disabled:
          disabled || props.mode === 'UPDATE' || !custData?.custCode || custData?.custCode == '',
      },
      inputRender: () => (
        <ContractSelectPop
          title="选择大合同"
          rowValue="contractId-contractCode-contractTypeName-agreedWageArriveDay-agreedPayDt-agereedAmtReceiveMon-contractSubTypeName-signBranchTitleId-signBranchTitleName-isIssuingSalary-signBranchTitleId-billDt-contractName"
          keyMap={{
            contractId: 'contractId',
            contractCode: 'contractCode',
            contractTypeName: 'contractType',
            contractSubTypeName: 'contractSubType',
            contractName: 'contractName',
            signBranchTitleId: 'signBranchTitleId',
            signBranchTitleName: 'signBranchTitle',
            isIssuingSalary: 'isIssuingSalary',
            departmentId: 'departmentId',
            agreedBillGenDt: 'billDt',
            agreedWageArriveDay: 'agreedWageArriveDay',
            agereedAmtReceiveMon: 'agereedAmtReceiveMon',
            agreedPayDt: 'agreedPayDt',
          }}
          fixedValues={{
            custCode: custData?.custId,
            custViewCode: custData?.custCode,
            custName: custData?.custName,
            queryType: '1',
          }}
          handdleConfirm={async (value: POJO) => {
            if (value.isIssuingSalary && value.isIssuingSalary == '1') {
              setAgreedPayDtIsRequire(true);
            } else {
              setAgreedPayDtIsRequire(false);
            }
            if (value?.contractId && value?.contractId !== null) {
              // 付款方查询的接口
              const { list: _list } =
                await API.emphiresep.receivable.queryReceivablePayerList.requests({
                  contractId: value?.contractId,
                  custId: custData?.custId,
                  custName: custData?.custName,
                });
              if (_list && _list?.length === 1) {
                form.setFieldsValue({ custPayerId: _list?.[0].custPayerId });
              }
            } else {
              form.setFieldsValue({ custPayerId: null });
            }
            const ret: POJO = {};
            if (+(value.billDt || '') + 5 > +value.agreedWageArriveDay)
              ret.agreedBillLockDt = value.agreedWageArriveDay;
            else ret.agreedBillLockDt = `${+(value.billDt || '') + 5}`;
            ret.amtReceivedMon = value.agereedAmtReceiveMon;
            const agreedPayDt = value.agreedPayDt;
            if (/^[1-9]$|^[1-2][0-9]$|^3[0-1]$/i.test(agreedPayDt)) ret.agreedPayDt = agreedPayDt;
            else ret.agreedPayDt = '';
            form.setFieldsValue({
              isSsExclued: '1',
              isPfExclued: '1',
              isWageExclued: '0',
              isTaxExclued: '0',
              isShowType: '1',
              ...ret,
              payerId: value.departmentId?.toString(),
              payeeId: value.departmentId?.toString(),
              payeeName: providerMap.get(value.departmentId?.toString()),
            });
            setPayeeData({
              payeeId: value.departmentId?.toString(),
              payeeName: providerMap.get(value.departmentId?.toString()),
            });
          }}
          distroyOnClose
        />
      ),
      rules: [{ required: true, message: '请选择大合同名称' }],
    },
    {
      label: '大合同编号',
      fieldName: 'contractCode',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '合同大类',
      fieldName: 'contractTypeName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '合同小类',
      fieldName: 'contractSubTypeName',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '账单模板名称',
      fieldName: 'receivableTempltName',
      inputProps: {
        disabled,
      },
      inputRender: 'string',
      formOptions: {
        rules: [{ required: true, message: '请输入账单模板名称' }],
      },
    },
    {
      label: '付款方',
      fieldName: 'custPayerId',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(custPayerMap, { showSearch: true, allowClear: true }),
      formOptions: {
        rules: [{ required: true, message: '请选择付款方' }],
      },
    },
    {
      label: '账单方',
      fieldName: 'payerId',
      inputProps: {
        disabled: disabled || props.mode === 'UPDATE',
      },
      inputRender: () => mapToSelectors(providerMap, { showSearch: true, allowClear: true }),
      formOptions: {
        rules: [{ required: true, message: '请选择账单方' }],
      },
    },
    {
      label: '收款方',
      fieldName: 'payeeId',
      inputProps: {
        disabled: disabled || props.mode === 'UPDATE',
      },
      inputRender: () =>
        mapToSelectors(providerMap, {
          showSearch: true,
          allowClear: true,
          onChange: (payeeId, option) => {
            setPayeeData({ payeeId, payeeName: option?.children });
            form.setFieldsValue({
              bankName: null,
              bankId: null,
              bankAcct: null,
              bank: null,
            });
          },
        }),
      formOptions: {
        rules: [{ required: true, message: '请选择收款方' }],
      },
    },
    {
      label: '收款方开户名',
      fieldName: 'bankName',
      inputProps: {
        disabled: disabled || !payeeData?.payeeId || payeeData?.payeeId == '',
      },
      inputRender: () => (
        <BankInfoSelectPop
          title="选择收款方开户名"
          rowValue="bankAcctId-bankId-bankAcct-bank-bankName"
          keyMap={{
            bankAcctId: 'bankAcctId',
            bankId: 'bankId',
            bankAcct: 'bankAcct',
            bank: 'bank',
            bankName: 'bankName',
          }}
          fixedValues={{
            providerId: payeeData?.payeeId,
            providerName: payeeData?.payeeName,
            providerType: '1',
          }}
          distroyOnClose
        />
      ),
      rules: [{ required: true, message: '请选择收款方开户名' }],
    },
    {
      label: '收款方所属银行',
      fieldName: 'bankId',
      inputProps: {
        disabled: true,
      },
      inputRender: () => <CommonBaseDataSelector params={{ type: '911' }} />,
    },
    {
      label: '收款方银行账号',
      fieldName: 'bankAcct',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '收款方开户银行',
      fieldName: 'bank',
      inputProps: {
        disabled: true,
      },
      inputRender: 'string',
    },
    {
      label: '签约方公司抬头id',
      fieldName: 'signBranchTitleId',
      inputRender: 'string',
      hiddenForm: true,
    },
    {
      label: '签约方公司抬头',
      fieldName: 'signBranchTitleName',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '约定账单生成日',
      fieldName: 'agreedBillGenDt',
      inputRender: 'number',
      inputProps: {
        disabled,
        precision: 0,
      },
      formOptions: {
        rules: [
          { required: true, max: 2, pattern: kNumberRegex, message: '约定账单生成日的范围 1-31' },
        ],
      },
    },
    {
      label: '约定账单锁定日',
      fieldName: 'agreedBillLockDt',
      inputRender: 'number',
      inputProps: {
        disabled,
        precision: 0,
      },
      formOptions: {
        rules: [
          { required: true, max: 2, pattern: kNumberRegex, message: '约定账单锁定日的范围 1-31' },
        ],
      },
    },
    {
      label: '约定数据提交日/增减员截止日',
      fieldName: 'agreedCommitDt',
      inputRender: 'number',
      inputProps: {
        disabled,
        precision: 0,
      },
      formOptions: {
        rules: [
          { pattern: kNumberRegex, max: 2, message: '约定数据提交日/增减员截止日的范围 1-31' },
        ],
      },
    },
    {
      label: '约定到款日(天)',
      fieldName: 'agreedWageArriveDay',
      inputRender: 'number',
      inputProps: {
        disabled,
        precision: 0,
      },
      formOptions: {
        rules: [{ pattern: kNumberRegex, max: 2, message: '约定到款日(天)的范围 1-31' }],
      },
    },
    {
      label: '到款所属月',
      fieldName: 'amtReceivedMon',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(receiveMonthListMap, { allowClear: false }),
    },
    {
      label: '约定工资发放日',
      fieldName: 'agreedPayDt',
      inputRender: 'number',
      inputProps: {
        disabled,
        precision: 0,
      },
      formOptions: {
        rules: [{ pattern: kNumberRegex, max: 2, message: '约定工资发放日的范围 1-31' }],
      },
      rules: agreedPayDtIsRequire
        ? [{ required: true, message: '大合同是否代发薪资为是，此项必填' }]
        : [],
    },
    {
      label: '是否社保计算总额',
      fieldName: 'isSsExclued',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '是否公积金计算总额',
      fieldName: 'isPfExclued',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '是否工资计算总额',
      fieldName: 'isWageExclued',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '是否个税计算总额',
      fieldName: 'isTaxExclued',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '员工按服务费年月分行显示',
      fieldName: 'isShowType',
      inputProps: {
        disabled,
      },
      inputRender: () => mapToSelectors(yesNoMap, { allowClear: false }),
    },
    {
      label: '开票方式',
      fieldName: 'invoiceType',
      inputRender: () => mapToSelectors(invoiceTypeMap, { allowClear: true }),
      inputProps: {
        disabled,
      },
    },
    {
      label: '备注',
      fieldName: 'newAdditionalNotes',
      inputRender: 'text',
      inputProps: {
        disabled,
      },
      rules: [{ required: true, message: '请填写备注' }],
    },
    {
      label: '上传附件',
      fieldName: 'fileId',
      inputRender: 'upload',
      inputProps: {
        disabled,
      },
    },
    {
      label: '账套审批过程',
      fieldName: 'approvalProcess',
      inputRender: 'text',
      hidden: props.mode == 'ADD',
      inputProps: {
        disabled: true,
      },
    },
  ];

  useEffect(() => {
    if (props.visible) {
      form.setFieldsValue({ ...(props.data || {}), bankId: String(props.data?.bankId || '') });
      if (props.data?.isIssuingSalary && props.data?.isIssuingSalary == '1') {
        setAgreedPayDtIsRequire(true);
      } else {
        setAgreedPayDtIsRequire(false);
      }
      setCustPayerMap(new Map());
      setProviderMap(new Map());
      if (props.mode !== 'ADD') {
        requestData((props.data || {}).custId as string);
        // API.emphiresep.receivable.queryReceivableTemplateApprovalById
        //   .requests({
        //     receivableTempltAppId: props.data?.receivableTempltAppId,
        //   })
        //   .then((res) => {
        //     form.setFieldsValue({ ...(res || {}), bankId: String(res?.bankId || '') });
        //   });
      } else {
        form.setFieldsValue({
          invoiceType: '1',
        });
      }
    } else {
      form.resetFields();
      setCustData({});
      setPayeeData({});
    }
  }, [props.visible]);

  /** 客户名称改变 */
  const custChanged = (custId?: string) => {
    form.setFieldsValue({
      custPayerId: undefined,
      payerId: undefined,
      payeeId: undefined,
      contractCode: undefined,
      contractTypeName: undefined,
      contractSubTypeName: undefined,
      contractName: undefined,
      signBranchTitleId: undefined,
      signBranchTitleName: undefined,
    });
    if (!custId) return;
    requestData(custId!);
  };

  /** 获取下拉数据 */
  const requestData = async (custId: string) => {
    try {
      const data = await API.emphiresep.receivable.getReceivableUpdateDropdownList.requests({
        custId,
      });
      const custPayerList = data.custPayerList || [];
      const providerList = data.providerList || [];

      const _custPayerMap = new Map<string, string>();
      const _providerMap = new Map<string, string>();
      custPayerList.forEach((e: any) => _custPayerMap.set(e.key, e.shortName));
      providerList.forEach((e: any) => _providerMap.set(e.key, e.shortName));
      setCustPayerMap(_custPayerMap);
      setProviderMap(_providerMap);
    } catch (e) {
      msgErr('获取数据失败');
    }
  };

  /** 填充数据 */
  const onFillData = () => {
    const custId = form.getFieldValue('custId');
    if (!custId) return msgErr('请选择客户后再进行操作');
    setContractVisible(true);
  };

  /** 保存 */
  const onSave = async () => {
    const values = await form.validateFields();
    await save(values);
  };

  // 提交审批
  const onApprove = async () => {
    const values = await form.validateFields();
    await save(values, true);
  };

  /** 保存数据 */
  const save = async (values: POJO<string>, isApprove?: boolean) => {
    const error = checkParams(values);
    if (error) return msgErr(error);
    if (values.agreedWageArriveDay === null || values.agreedWageArriveDay === undefined)
      values.agreedWageArriveDay = '';
    if (values.agreedCommitDt === null || values.agreedCommitDt === undefined)
      values.agreedCommitDt = '';
    if (values.agreedPayDt === null || values.agreedPayDt === undefined) values.agreedPayDt = '';
    values.receivableTempltName = values.receivableTempltName
      .replace(/（/g, '(')
      .replace(/）/g, ')');
    values.additionalNotes =
      (props.data?.additionalNotes || '') +
      '\r\n' +
      values.newAdditionalNotes +
      ' ' +
      userName +
      ' ' +
      new Date().toLocaleString();
    if (props.mode === 'ADD') {
      const data = await API.emphiresep.receivable.checkSameReceivableNameByDiffentId.requests({
        receivableTemplateName: values.receivableTempltName,
      });
      if (data === true) return msgErr('账单模板名称已重复,请重新填写');
    }
    let service: any = '';
    if (isApprove) {
      if (props.data?.approvalStatus == '5') {
        service = API.emphiresep.receivable.updateReceivableTemplateRecreateProcess;
      } else {
        service = API.emphiresep.receivable.insertReceivableTemplateApprove;
      }
    } else {
      service =
        props.mode === 'UPDATE'
          ? API.emphiresep.receivable.updateReceivableTemplateApp
          : API.emphiresep.receivable.insertReceivableTemplate;
    }

    await service.requests({
      ...props.data,
      ...values,
      clientOperation: props.mode === 'UPDATE' ? tableLineStateMap.update : tableLineStateMap.add,
    });
    msgOk('操作成功');
    props.hideHandle(true);
  };

  const checkParams = (values: POJO<string>) => {
    if (values.receivableTempltName.length > 100) return '账单模板名称长度不能超过100';
    const agreedBillGenDt = values.agreedBillGenDt;
    const agreedBillGenDtNum = +agreedBillGenDt;
    if (agreedBillGenDt.length > 2 || agreedBillGenDtNum < 1 || agreedBillGenDtNum > 31)
      return '约定账单生成日的范围 1-31';
    const agreedBillLockDt = values.agreedBillLockDt;
    const agreedBillLockDtNum = +agreedBillLockDt;
    if (agreedBillLockDt.length > 2 || agreedBillLockDtNum < 1 || agreedBillLockDtNum > 31)
      return '约定账单锁定日的范围 1-31';
    const agreedCommitDt = values.agreedCommitDt;
    if (agreedCommitDt !== null && agreedCommitDt !== undefined && agreedCommitDt !== '') {
      const agreedCommitDtNum = +agreedCommitDt;
      if (agreedCommitDtNum < 1 || agreedCommitDtNum > 31)
        return '约定数据提交日/增减员截止日的范围 1-31';
    }
    const agreedWageArriveDay = values.agreedWageArriveDay;
    if (
      agreedWageArriveDay !== null &&
      agreedWageArriveDay !== undefined &&
      agreedWageArriveDay !== ''
    ) {
      const agreedWageArriveDayNum = +agreedWageArriveDay;
      if (agreedWageArriveDayNum < 1 || agreedWageArriveDayNum > 31)
        return '约定到款日(天)的范围 1-31';
    }
    const agreedPayDt = values.agreedPayDt;
    if (agreedPayDt !== null && agreedPayDt !== undefined && agreedPayDt !== '') {
      const agreedPayDtNum = +agreedPayDt;
      if (agreedPayDtNum < 1 || agreedPayDtNum > 31) return '约定工资发放日的范围 1-31';
    }
    return undefined;
  };

  const renderFooter = () => {
    switch (props.mode) {
      case 'ADD':
      case 'UPDATE':
        return (
          <div style={{ textAlign: 'center' }}>
            <AsyncButton onClick={onSave}>保存</AsyncButton>
            <Button onClick={() => props.hideHandle()}>关闭</Button>
            <AsyncButton onClick={onApprove}>提交审批</AsyncButton>
          </div>
        );
      default:
        return (
          <div style={{ textAlign: 'center' }}>
            <Button onClick={() => props.hideHandle()}>关闭</Button>
          </div>
        );
    }
  };

  return (
    <Codal
      title="账单模板维护"
      visible={props.visible}
      onCancel={() => props.hideHandle()}
      footer={renderFooter()}
      width={1200}
      destroyOnClose
    >
      <FormElement3 form={form}>
        <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns} />
      </FormElement3>
      <CustContract
        visible={contractVisible}
        custId={contractVisible ? form.getFieldValue('custId') : undefined}
        hideHandle={(data) => {
          setContractVisible(false);
          if (data) form.setFieldsValue(data);
        }}
        custPayerList={custPayerMap}
      />
    </Codal>
  );
};

export default Detail;
