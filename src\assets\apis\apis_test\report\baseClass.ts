class BizTypeInfo {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** bizCategory */
  bizCategory = '';

  /** bizName */
  bizName = '';

  /** bizTypeId */
  bizTypeId = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDeleted */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** type */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new Page();

  /** message */
  message = '';

  /** t */
  t = new Page();
}

class Contract {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agereedAmtReceiveMon = '';

  /** agreedPayDt */
  agreedPayDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** areaId */
  areaId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** attTypeDraftId */
  attTypeDraftId = '';

  /** attTypeDraftName */
  attTypeDraftName = '';

  /** attTypeLegalId */
  attTypeLegalId = '';

  /** attTypeLegalName */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  billDt = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** isIssuingSalary */
  contractCategeryName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartA = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartB = '';

  /** contractProductLineIds */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractRetrieveDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStatus = '';

  /** contractStatusName */
  contractStatusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSubType = '';

  /** 合同类别（子类）name */
  contractSubTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** contractSvcStateName */
  contractSvcStateName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractTemplateId = '';

  /** contractTerminationDate */
  contractTerminationDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 合同类别 */
  contractVersionType = '';

  /** isIssuingSalary */
  contractVersionTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByName */
  createByName = '';

  /** createByParty */
  createByParty = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  createType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  creditPeriod = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  custPayerId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custSealDt = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerSize */
  customerSize = '';

  /** defStatus */
  defStatus = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** draftRemark */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** estimateFirstBillDate */
  estimateFirstBillDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectedIncrease */
  expectedIncrease = '';

  /** expectedIncreaseOld */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务name */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /** firstWgApproveId */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区Name */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司Name */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** private String contractSvcStateName; */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** governingBranchName */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** fileId */
  importFileId = '';

  /** fileName */
  importFileName = '';

  /** inId */
  inId = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** invoiceMoney */
  invoiceMoney = '';

  /** NP-8564 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
  isAdjustRenewContract = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  isAssign = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** isDefer */
  isDefer = '';

  /** isDeferName */
  isDeferName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** isIssuingSalary */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** isSameInsur */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** isSecondaryDev */
  isSecondaryDev = '';

  /** isSecondaryDevName */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApprovalStatus = '';

  /** legalRemark */
  legalRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** nextContractId */
  nextContractId = '';

  /** nextContractName */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** noChange */
  noChange = false;

  /** 非标合同审批单:NON_STA_COCT_APPR_ID */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  parentContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  payCollectPoint = '';

  /** payMonth */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  payerName = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApprovalStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  processInstanceId = '';

  /** productLineIdLogs */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectPlanRequest = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectRemark = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerType */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 报价单集合id */
  quoIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  renewedContractNum = '';

  /** reportElEvacuatedDate */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** reportGlEvacuatedDate */
  reportGlEvacuatedDate = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** roleCode */
  roleCode = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增存量标识 （手工） */
  salFlagManual = '';

  /** salFlagManualName */
  salFlagManualName = '';

  /** salFlagName */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApprove = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealApproveStatus = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealOpinion = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signArea */
  signArea = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** signBranchTitleIdAreaId */
  signBranchTitleIdAreaId = '';

  /** signBranchTitleIdBranchId */
  signBranchTitleIdBranchId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 撤单原因 */
  stopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcRegion = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadFileName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadUrl = '';

  /** upt */
  upt = false;

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** workitemId */
  workitemId = '';
}

class ContractFile {
  /** 审批节点 */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同附件ID */
  contractFileId = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 附件ID */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 附件路径 */
  filePath = '';

  /** 附件类型 */
  fileType = '';

  /** 附件类型name */
  fileTypeName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程defId */
  processDefId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传步骤 */
  uploadStep = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractRetiree {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 人员姓名 */
  bz = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 人员姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 身份证号 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 大合同退休人员主键 */
  retireeId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class DDCustQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 客户账单年月 */
  billYm = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 客服 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户 */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 销售 */
  salesId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class EvacuateWarning {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 客服竞争对手名称 */
  competitorName = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** waringId */
  waringId = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class FilterEntity {
  /** activityNameCn */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByStr */
  createByStr = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** disaBatchId */
  disaBatchId = '';

  /** disaReasonId */
  disaReasonId = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** reasonBz */
  reasonBz = '';

  /** reasonId */
  reasonId = '';

  /** reasonStr */
  reasonStr = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workitemId */
  workitemId = '';
}

class HashMap {}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class PerfectSearchQuery {
  /** AlterstatusFW */
  AlterstatusFW = '';

  /** addConfirmStat */
  addConfirmStat = '';

  /** addconfirmstatusFW */
  addconfirmstatusFW = '';

  /** alterStat */
  alterStat = '';

  /** alterstatusFW */
  alterstatusFW = '';

  /** 接单方客服 */
  assigneeCsId = '';

  /** assigneeProviderId */
  assigneeProviderId = '';

  /** 派单方客服 */
  assignerCsId = '';

  /** assignerProviderId */
  assignerProviderId = '';

  /** category */
  category = '';

  /** chargeEndDate */
  chargeEndDate = '';

  /** 雇员费用段横表查询用时作为  "查询参考日" */
  chargeStartDate = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** employeeCode */
  employeeCode = '';

  /** employeeName */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 订单完善所要用的参数 */
  governingBranch = '';

  /** 订单完善所要用的参数 */
  governingBranch1 = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品id 多值 */
  productId = '';

  /** productName */
  productName = '';

  /** 产品名称，多值 */
  productNames = '';

  /** providerType */
  providerType = '';

  /** rptHireEndDt */
  rptHireEndDt = '';

  /** rptHireStartDt */
  rptHireStartDt = '';

  /** sepConfirmStat */
  sepConfirmStat = '';

  /** sepconfirmstatusFW */
  sepconfirmstatusFW = '';

  /** 社保公积金组id，多值，雇员费用段横表查询用  */
  ssGroupId = '';

  /** 社保公积金组名称，多值 */
  ssGroupNames = '';

  /** startIndex */
  startIndex = undefined;

  /** subcontractCode */
  subcontractCode = '';

  /** subcontractId */
  subcontractId = '';

  /** subcontractName */
  subcontractName = '';

  /** 产品类型 多值 */
  typeClass = '';

  /** 产品类型名称，多值 */
  typeClassNames = '';

  /** 当前登录人Id */
  userId = '';

  /** 当前登录人Id,报表要传两个userId，参数名不同值相同  */
  userId1 = '';
}

class QaPayRollQuery {
  /** 对应大区 */
  areaId = '';

  /** 大合同签单分公司 */
  branchId = '';

  /** custId */
  custId = '';

  /** 发放名称 */
  disburseName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入人 */
  importerName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** wageMonthEnd */
  wageMonthEnd = '';

  /** wageMonthStart */
  wageMonthStart = '';
}

class RptComplaint {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CASE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  caseBy = '';

  /** caseByName */
  caseByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CASE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  caseDt = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CLOSE_CASE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  closeCase = '';

  /** closeCaseName */
  closeCaseName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COM_HAND_SAT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  comHandSat = '';

  /** comHandSatName */
  comHandSatName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAIN_DATE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  complainDate = '';

  /** complainDateGE */
  complainDateGE = '';

  /** complainDateLE */
  complainDateLE = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAIN_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  complainId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAINT_PERSON           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  complaintPerson = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAINT_REASON           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  complaintReason = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CONTACT_TEL           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractCode */
  contractCode = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CONTRACT_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  contractId = '';

  /** contractName */
  contractName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CREATE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByName */
  createByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CREATE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  createDt = '';

  /** currentSales */
  currentSales = '';

  /** currentSalesName */
  currentSalesName = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CUST_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departmentId */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.DPE_CUST_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  dpeCustDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.EFFECT_TRACK           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  effectTrack = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.EMERGENCY_DEVICE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  emergencyDevice = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.EMERGENCY_GROUP           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  emergencyGroup = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.HANDLE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  handleBy = '';

  /** handleByName */
  handleByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.HANDLE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  handleDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.ID_CARD_NUM           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  idCardNum = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.IF_RECEIVE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  ifReceive = '';

  /** ifReceiveName */
  ifReceiveName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.IS_DELETED           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** liabilityCs */
  liabilityCs = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.MIMIC_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.NOT_CASE_REASON           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  notCaseReason = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.NOTE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  noteBy = '';

  /** noteByList */
  noteByList = [];

  /** noteByName */
  noteByName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** piDt */
  piDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PREVENT_MEASURES           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  preventMeasures = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PRO_DES           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  proDes = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processInsId */
  processInsId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PROCESSING_RESULT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  processingResult = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PROXY_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.QA_DIS           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  qaDis = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receiveBy = '';

  /** receiveByName */
  receiveByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_COMPLAINT_AREA_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receiveComplaintAreaId = '';

  /** receiveComplaintAreaName */
  receiveComplaintAreaName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_COMPLAINT_DEPART_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receiveComplaintDepartId = '';

  /** receiveComplaintDepartName */
  receiveComplaintDepartName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_DEPART_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receiveDepartId = '';

  /** receiveDepartName */
  receiveDepartName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_PE_TYPE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receivePeType = '';

  /** receivePeTypeName */
  receivePeTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_TYPE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receiveType = '';

  /** receiveTypeName */
  receiveTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_WAY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  receiveWay = '';

  /** receiveWayName */
  receiveWayName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.REMARK           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RETURN_VISIT_RESULT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  returnVisitResult = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.ROOT_ANALYSIS           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  rootAnalysis = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.STA_REQ           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  staReq = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.STANDARDIZATION           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  standardization = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.STATUS           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  status = '';

  /** statusName */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';

  /** toHandleDt */
  toHandleDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.TRAIN           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  train = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.UPDATE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  updateBy = '';

  /** updateByName */
  updateByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.UPDATE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workitemId */
  workitemId = '';
}

class RptComplaintNoteBy {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** complainId */
  complainId = '';

  /** complainNoteById */
  complainNoteById = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** noteBy */
  noteBy = '';

  /** noteByName */
  noteByName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class financeRptQuery {
  /** areaId */
  areaId = '';

  /** billType */
  billType = '';

  /** billYM */
  billYM = '';

  /** bill_ym */
  bill_ym = '';

  /** createDtE */
  createDtE = '';

  /** createDtS */
  createDtS = '';

  /** custId */
  custId = '';

  /** cust_id */
  cust_id = '';

  /** departmentId */
  departmentId = '';

  /** deptId */
  deptId = '';

  /** endIndex */
  endIndex = undefined;

  /** feeBackGe */
  feeBackGe = '';

  /** feeBackLe */
  feeBackLe = '';

  /** finRecYm */
  finRecYm = '';

  /** governingArea */
  governingArea = '';

  /** isGroup */
  isGroup = '';

  /** isMerge */
  isMerge = '';

  /** isSelTemplts */
  isSelTemplts = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** payeeId */
  payeeId = '';

  /** print_code */
  print_code = '';

  /** print_prcedure */
  print_prcedure = '';

  /** prvdGroupId */
  prvdGroupId = '';

  /** receivable_templt_id */
  receivable_templt_id = '';

  /** signBranchTitleId */
  signBranchTitleId = '';

  /** spCode */
  spCode = '';

  /** startIndex */
  startIndex = undefined;

  /** userId */
  userId = '';
}

class payrollDisabilityBenefitsQuery {
  /** 客户Id */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 薪资专员 */
  payrollSpecialistName = '';

  /** 查询类型：默认不传查残障金，工会经费传2 */
  queryType = '';

  /** startIndex */
  startIndex = undefined;

  /** 计税月 */
  taxMonth = '';

  /** 扣缴义务人id */
  withholdAgentId = '';
}

class taxDeclarationEmpInfoQuery {
  /** 受理ID */
  acceptId = '';

  /** 申报反馈受理ID */
  backAcceptId = '';

  /** 唯一号 */
  batchId = '';

  /** 确认邀请人员任务ID */
  createBy = '';

  /** decLarationHisId */
  decLarationHisId = '';

  /** 个税申报任务主表ID */
  decLarationId = '';

  /** 作废受理ID */
  delAcceptId = '';

  /** 唯一号 */
  empCode = '';

  /** 唯一号 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 证件号码 */
  idCardNum = '';

  /** 所得类型 */
  incomeType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 受理ID */
  rdecAcceptId = '';

  /** 所得月份 */
  sdyf = '';

  /** 唯一号 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 任务状态 0.已生成任务，1已获取明细数据，2已生成报税数据，3已预报税提交，4已获取预报税结果，5已提交个税申报，6已获取个税申报结果 */
  taskStatus = '';

  /** 所得月份 */
  taskstatus = '';

  /** 排除表ID */
  taxDecOutId = '';

  /** 税差核算任务主表ID */
  taxDiffId = '';

  /** 确认邀请人员任务ID */
  taxInviteId = '';

  /** 计税月 */
  taxMonth = '';

  /** 主键 */
  taxTaskDetailId = '';

  /** 主任务id */
  taxTaskId = '';

  /** 更正受理ID */
  uptAcceptId = '';

  /** 发放ID */
  wageSendId = '';

  /** 扣缴义务人id */
  withholdAgentId = '';

  /** withholdAgentName */
  withholdAgentName = '';

  /** withholdAgentType */
  withholdAgentType = '';

  /** 姓名 */
  xm = '';

  /** 证照号码 */
  zzhm = '';

  /** 姓名 */
  zzlx = '';
}

export const report = {
  BizTypeInfo,
  CommonResponse,
  Contract,
  ContractFile,
  ContractRetiree,
  DDCustQuery,
  DropdownList,
  EvacuateWarning,
  ExportQuery,
  FilterEntity,
  HashMap,
  Page,
  PerfectSearchQuery,
  QaPayRollQuery,
  RptComplaint,
  RptComplaintNoteBy,
  financeRptQuery,
  payrollDisabilityBenefitsQuery,
  taxDeclarationEmpInfoQuery,
};
