/*
 * @Author: 刘双
 * @Email: <EMAIL>
 * @Date: 2021-01-28 17:01:36
 * @LastAuthor: 刘双
 * @LastTime: 2021-04-02 11:13:54
 * @message:
 */
import { useStore } from 'umi';
import { ConnectState } from '@/models/connect';

/**
 * 获取当前用户ID
 */
export const getUserId = () => {
  const userId = useStore<ConnectState>().getState().user.currentUser.profile.userId;
  return userId;
};

export const getUserName = () => {
  const userName = useStore<ConnectState>().getState().user.currentUser.profile.userName;
  return userName;
};

export const getUserGoverningBranch = () => {
  const governingBranch =
    useStore<ConnectState>().getState().user.currentUser.profile?.governingBranch ||
    useStore<ConnectState>().getState().user.currentUser.currentRole?.governingBranch ||
    null;
  return governingBranch;
};
/**
 * 获取当前用户部门id
 */
export const getDepartmentId = () => {
  const departmentId = useStore<ConnectState>().getState().user.currentUser.profile.departmentId;
  return departmentId;
};

/**
 * 获取当前角色
 */
export const getUserRole = () => {
  const role = useStore<ConnectState>().getState().user.currentUser.roleList;
  return role;
};

/**
 * 获取当前登录全部信息
 */
export const getCurrentUser = () => {
  const currentUser = useStore<ConnectState>().getState().user.currentUser;
  return currentUser;
};
// 获取当前用户城市信息
export const getCurrentUserCityId = () => {
  const cityId = useStore<ConnectState>().getState().user.currentUser?.profile?.cityId;
  return cityId;
};

/**
 * 获取当前菜单
 */
export const getCurrentMenu = () => {
  const breadcrumbNameMap = useStore<ConnectState>().getState().menu.breadcrumbNameMap;
  const activeKey = location.pathname;
  return breadcrumbNameMap[activeKey] || {};
};

/**
 * 获取当前菜单
 */
export const getCurrentAuth = () => {
  const breadcrumbNameMap = useStore<ConnectState>().getState().menu.breadcrumbNameMap;
  const activeKey = location.pathname;
  const menu = breadcrumbNameMap[activeKey];
  if (!menu) return {};
  if (menu.funcType !== '0') return {};
  if (!menu.children || menu.children.length === 0) return {};
  return menu.permission || {};
};

export const getDepartInfo = async (departmentId: string | undefined) => {
  // 天大地大，找不到一个获取部门信息的接口。
  if (!departmentId) return {};
  const branchLi = await API.admin.department.queryBranchList.requests({ departmentId });
  if (!branchLi) return {};
  if (!branchLi.list || branchLi.list.length === 0) return {};
  return branchLi.list[0];
};

// 获取proDefId
export const getProDefId = () => {
  const reg = new RegExp('(^|&)proDefId=([^&]*)(&|$)', 'i');
  const r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return '';
  // const proDefId = useStore<ConnectState>().getState().notice.proDefId;
  // return proDefId;
};

// 获取沐融系统配置
export const getMuRong = () => {
  const { token, murongServer } = useStore<ConnectState>().getState().user.currentUser;
  return { token, murongServer };
};

// 获取契约锁系统配置
export const getQys = () => {
  const { qysServer } = useStore<ConnectState>().getState().user.currentUser;
  return { qysServer };
};
