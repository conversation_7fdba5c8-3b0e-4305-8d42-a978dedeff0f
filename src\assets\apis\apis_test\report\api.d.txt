declare namespace defs {
  export namespace report {
    export class BizTypeInfo {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** bizCategory */
      bizCategory: string;

      /** bizName */
      bizName: string;

      /** bizTypeId */
      bizTypeId: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** isDeleted */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** memo */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** type */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class Contract {
      /** 流程节点ID */
      activityDefId: string;

      /** 审批步骤 */
      activityNameCn: string;

      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** add */
      add: boolean;

      /** 新增驳回原因list */
      addSlDisaReasonList: Array<defs.report.FilterEntity>;

      /** 预付款比例 */
      advancePaymentRatio: string;

      /** 代收代付 */
      agentBusiness: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      agereedAmtReceiveMon: string;

      /** agreedPayDt */
      agreedPayDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      agreedWageArriveDay: string;

      /** 非标合同审批单code */
      applyCode: string;

      /** approveDt */
      approveDt: string;

      /** approveDtEnd */
      approveDtEnd: string;

      /** approveDtStart */
      approveDtStart: string;

      /** 页面填写的审核意见 */
      approveOpinion: string;

      /** 合同审核相关的附件 */
      approveRelatedAttachment: string;

      /** 合同审核相关的附件name */
      approveRelatedAttachmentName: string;

      /** areaId */
      areaId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      areaType: string;

      /** areaTypeName */
      areaTypeName: string;

      /** attTypeDraftId */
      attTypeDraftId: string;

      /** attTypeDraftName */
      attTypeDraftName: string;

      /** attTypeLegalId */
      attTypeLegalId: string;

      /** attTypeLegalName */
      attTypeLegalName: string;

      /** 平均价格集合 */
      averageMoneys: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      billDt: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市 */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 提交时间 */
      commitTime: string;

      /** 签约人数集合 */
      compactNumbers: string;

      /** 竞争对手id */
      competitor: string;

      /** 客服竞争对手名称 */
      competitorName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      confirmdWorkFlow: string;

      /** 联系人手机 */
      contactCell: string;

      /** 联系人电话 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractAvgAmt: string;

      /** 合同类别 */
      contractCategery: string;

      /** isIssuingSalary */
      contractCategeryName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractCode: string;

      /** 合同创建人 */
      contractCreateBy: string;

      /** 最终结束日期 */
      contractEndDate: string;

      /** 最终结束日期从 */
      contractEndDateFrom: string;

      /** 最终结束日期到 */
      contractEndDateTo: string;

      /** 合同最终结束日期类型 */
      contractEndDateType: string;

      /** 合同附件集合 */
      contractFileList: Array<defs.report.ContractFile>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractFileName: string;

      /** 合同附件备注 */
      contractFileRemark: string;

      /** 合同附件上传时间 */
      contractFileUploadDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractHeadcount: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractPartA: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractPartB: string;

      /** contractProductLineIds */
      contractProductLineIds: string;

      /** 合同退休人员集合 */
      contractRetireeList: Array<defs.report.ContractRetiree>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractRetrieveDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStartDate: string;

      /** 合同启动时间止 */
      contractStartDateEnd: string;

      /** 合同启动日期起 */
      contractStartDateStart: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStatus: string;

      /** contractStatusName */
      contractStatusName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStopDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStopReason: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractSubType: string;

      /** 合同类别（子类）name */
      contractSubTypeName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractSvcState: string;

      /** 合同状态集合 */
      contractSvcStateList: Array<string>;

      /** contractSvcStateName */
      contractSvcStateName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractTemplateId: string;

      /** contractTerminationDate */
      contractTerminationDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractType: string;

      /** 合同类型名 */
      contractTypeName: string;

      /** 合同版本号 */
      contractVersion: string;

      /** 合同类别 */
      contractVersionType: string;

      /** isIssuingSalary */
      contractVersionTypeName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByName */
      createByName: string;

      /** createByParty */
      createByParty: string;

      /** 创建日期 */
      createDt: string;

      /** createDtEnd */
      createDtEnd: string;

      /** createDtStart */
      createDtStart: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      createType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      creditPeriod: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      csApproval: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      csApprovalStatus: string;

      /** 客服联系人地址 */
      cstScAddress: string;

      /** 客服联系人手机 */
      cstScCall: string;

      /** 客服联系人 */
      cstScContact: string;

      /** 客服联系人邮件 */
      cstScEmail: string;

      /** 客服联系人电话 */
      cstScTel: string;

      /** 客服联系人传真 */
      cstScfax: string;

      /** 客服联系人职位 */
      cstScposition: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      currentExeContractId: string;

      /** 现销售 */
      currentSales: string;

      /** 客户唯一号 */
      custCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      custPayerId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      custSealDt: string;

      /** 客户显示编号 */
      custViewCode: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** customerSize */
      customerSize: string;

      /** defStatus */
      defStatus: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** 驳回原因批次 */
      disaBatchId: string;

      /** draftRemark */
      draftRemark: string;

      /** 联系人邮件 */
      email: string;

      /** endIndex */
      endIndex: number;

      /** 是否增强型代理 */
      enhancedAgent: string;

      /** EOS账号停用 */
      eosStatus: string;

      /** estimateFirstBillDate */
      estimateFirstBillDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      estimatedHeadcount: string;

      /** 执行成本 */
      executionCost: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** expectedIncrease */
      expectedIncrease: string;

      /** expectedIncreaseOld */
      expectedIncreaseOld: string;

      /** 联系人传真 */
      fax: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 首次出账单的客户账单年月 */
      firstAccountMonth: string;

      /** 首次出账单时间(锁定时间) */
      firstBillDate: string;

      /** 首次大合同ID */
      firstContractId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      firstLegalApproveId: string;

      /** 合同审批的首个法务name */
      firstLegalApproveName: string;

      /** 首次出账单的财务应收年月 */
      firstOughtMonth: string;

      /** firstWgApproveId */
      firstWgApproveId: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** 原销售所属大区Name */
      formerGoverningAreaName: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** 原销售所属分公司Name */
      formerGoverningBranchName: string;

      /** 原销售 */
      formerSales: string;

      /** 原销售名字 */
      formerSalesName: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      furtureOpportunity: string;

      /** 所属大区 */
      governingArea: string;

      /** private String contractSvcStateName; */
      governingAreaName: string;

      /** 所属分公司 */
      governingBranch: string;

      /** governingBranchName */
      governingBranchName: string;

      /** 毛利 */
      grossProfit: string;

      /** 集团公司编号 */
      groupId: string;

      /** 集团公司名称 */
      groupName: string;

      /** 是否有交接单 */
      hasTransferInfo: string;

      /** 人力资源联系人 */
      hrContract: string;

      /** fileId */
      importFileId: string;

      /** fileName */
      importFileName: string;

      /** inId */
      inId: string;

      /** 收入 */
      income: string;

      /** 内支金额 */
      internalMoney: string;

      /** invoiceMoney */
      invoiceMoney: string;

      /** NP-8564 */
      invoiceNum: string;

      /** 滞纳金比例是否为万分之五 1：是；0：否 */
      is5Per10000FineRate: string;

      /** 滞纳金比例是否为万分之五name */
      is5Per10000FineRateName: string;

      /** 是否有补充附件 */
      isAddedAttachment: string;

      /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
      isAdjustRenewContract: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      isAssign: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否要提交审核 */
      isCommitApprove: string;

      /** 是否为已有客户所推荐 1：是；0：否 */
      isCustRecommend: string;

      /** 是否为已有客户所推荐name */
      isCustRecommendName: string;

      /** isDefer */
      isDefer: string;

      /** isDeferName */
      isDeferName: string;

      /** 删除标记 */
      isDeleted: string;

      /** 是否开通EOS账号 */
      isEosAccount: string;

      /** 是否内支 */
      isInternalPayment: string;

      /** isIssuingSalary */
      isIssuingSalary: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
      isJoinCompensation: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款name */
      isJoinCompensationName: string;

      /** 是否正常审批 */
      isNormalApprove: string;

      /** 质控计算结果是否为垫付 1：是；0：否 */
      isPaymentQAResult: string;

      /** 质控计算结果是否为垫付name */
      isPaymentQAResultName: string;

      /** 服务人数小于20人，是否季度付款 1：是；0：否 */
      isQuarterlyPaymentLess20: string;

      /** 服务人数小于20人，是否季度付款name */
      isQuarterlyPaymentLess20Name: string;

      /** 是否有赠送退休额度 */
      isRetQuotaGranted: string;

      /** 是否包含退休业务 */
      isRetirementBusiness: string;

      /** 是否包含退休业务name */
      isRetirementBusinessName: string;

      /** 是否抢单 */
      isRob: string;

      /** isSameInsur */
      isSameInsur: string;

      /** 是否集中一地投保name */
      isSameInsurName: string;

      /** isSecondaryDev */
      isSecondaryDev: string;

      /** isSecondaryDevName */
      isSecondaryDevName: string;

      /** 含差旅服务 */
      isTravelServices: string;

      /** 含差旅服务(展示) */
      isTravelServicesName: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      legalApproval: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      legalApprovalStatus: string;

      /** legalRemark */
      legalRemark: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      liabilityCs: string;

      /** 责任客服所属分公司id */
      liabilityCsDepartmentId: string;

      /** 责任客服名字 */
      liabilityCsName: string;

      /** 责任客服职员代码 */
      libilityCsCode: string;

      /** 会议记录id */
      meetingRecordId: string;

      /** 会议记录上传附件id */
      meetingRecordImportFileId: string;

      /** 会议记录上传附件名称 */
      meetingRecordImportFileName: string;

      /** memo */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** 范本修改版合同备注 */
      modelModifyVersionRemark: string;

      /** 新销售 */
      newSales: string;

      /** nextContractId */
      nextContractId: string;

      /** nextContractName */
      nextContractName: string;

      /** 下个法务 */
      nextLegalApproveId: string;

      /** noChange */
      noChange: boolean;

      /** 非标合同审批单:NON_STA_COCT_APPR_ID */
      nonStaCoctApprId: string;

      /** 服务订单数 */
      orderNumber: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      parentContractId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      payCollectPoint: string;

      /** payMonth */
      payMonth: string;

      /** 缴费类型 */
      payType: string;

      /** 客户付款方id集合 */
      payerIds: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      payerName: string;

      /** 付款方式 */
      paymentMode: string;

      /** 体检预估成本 */
      peExecutionCost: string;

      /** 体检毛利 */
      peGrossProfit: string;

      /** 体检收入 */
      peIncome: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      prepayAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      prepayApproval: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      prepayApprovalStatus: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processDefId */
      processDefId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      processInstanceId: string;

      /** productLineIdLogs */
      productLineIdLogs: string;

      /** 产品线id集合 */
      productLineIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      projectPlanRequest: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      projectRemark: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerType */
      providerType: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** QA审核意见 */
      qaApprove: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      queryType: string;

      /** 报价单集合id */
      quoIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      renewedContractNum: string;

      /** reportElEvacuatedDate */
      reportElEvacuatedDate: string;

      /** 客服反馈撤单时间 */
      reportEvacuatedDate: string;

      /** 客服撤单详细原因说明 */
      reportEvacuatedExplantion: string;

      /** 客服撤单原因分类 */
      reportEvacuatedReason: string;

      /** reportGlEvacuatedDate */
      reportGlEvacuatedDate: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 赠送退休数量 */
      retirementGiftCount: string;

      /** 回访历史内容 */
      returnVisitMemo: string;

      /** 最后回访人Id */
      returnVisitorId: string;

      /** 风险金比例% */
      riskPremiumRatio: string;

      /** 风险分担比例% */
      riskSharingRatio: string;

      /** roleCode */
      roleCode: string;

      /** 统计标志位 */
      salFlag: string;

      /** 新增存量标识 （手工） */
      salFlagManual: string;

      /** salFlagManualName */
      salFlagManualName: string;

      /** salFlagName */
      salFlagName: string;

      /** 客户对应销售及分公司 */
      saleAndBranchName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      salesApprove: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      salesApproveStatus: string;

      /** 现销售职员代码 */
      salesCode: string;

      /** 销售所在主部门 */
      salesDeptName: string;

      /** 销售名字 */
      salesName: string;

      /** 所属销售团队类型 */
      salesTeamType: string;

      /** 客服竞争对手优势 */
      sctScComAdvancetage: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      sealApproveStatus: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      sealDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      sealOpinion: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** signArea */
      signArea: string;

      /** 签约方公司抬头 */
      signBranchTitle: string;

      /** 签约方公司抬头id */
      signBranchTitleId: string;

      /** signBranchTitleIdAreaId */
      signBranchTitleIdAreaId: string;

      /** signBranchTitleIdBranchId */
      signBranchTitleIdBranchId: string;

      /** 新签标识（手工） */
      signFlagManual: string;

      /** 新签标识（手工）name */
      signFlagManualName: string;

      /** 签单分公司 */
      signProvider: string;

      /** 驳回原因list */
      slDisaReasonList: Array<defs.report.FilterEntity>;

      /** startIndex */
      startIndex: number;

      /** 撤单原因 */
      stopReason: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      stopSvcDt: string;

      /** 终止服务操作日期 查询条件：终止服务日期到 */
      stopSvcEndDt: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 销售补充标志 0需要销售补充信息1审批 */
      supplyMark: string;

      /** 销售补充附件说明(历史) */
      supplyShow: string;

      /** 销售补充附件名称 */
      supplyShowFileName: string;

      /** 销售补充附件路径 */
      supplyShowFilePath: string;

      /** 销售补充附件说明(新增) */
      supplyShowNew: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      svcRegion: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;

      /** 税费 */
      tax: string;

      /** 总售价 */
      totalPrice: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      tranferProcessId: string;

      /** 交接单id */
      transferId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      transferRemark: string;

      /** 差旅服务费比例% */
      travelServicesRatio: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      uploadFileName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      uploadUrl: string;

      /** upt */
      upt: boolean;

      /** 修改驳回原因list */
      uptSlDisaReasonList: Array<defs.report.FilterEntity>;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 职场健康 预付款金额 */
      whAdvancePaymentAmt: string;

      /** 职场健康 预付款时间 */
      whAdvancePaymentDt: string;

      /** 提成销售 */
      whCommissionSale: string;

      /** 提成销售name */
      whCommissionSaleName: string;

      /** 职场健康 合同寄送地址 */
      whContractSendAddress: string;

      /** 职场健康 预计垫付时长（天） */
      whExpectedPrepayDay: string;

      /** 职场健康 尾款金额 */
      whFinalPaymentAmt: string;

      /** 职场健康 尾款时间 */
      whFinalPaymentDt: string;

      /** 职场健康 开票顺序 */
      whInvoiceOrder: string;

      /** 开票顺序name */
      whInvoiceOrderName: string;

      /** 职场健康 项目编号 */
      whItemCode: string;

      /** 职场健康 毛利率% */
      whMargin: string;

      /** 体检税率% */
      whPeRate: string;

      /** 职场健康 垫付备注 */
      whPrepayRemark: string;

      /** 职场健康 采购发票内容 */
      whPurchaseInvoiceContent: string;

      /** 职场健康 采购发票类型 */
      whPurchaseInvoiceType: string;

      /** 职场健康 采购发票类型name */
      whPurchaseInvoiceTypeName: string;

      /** 职场健康 返佣收入 */
      whRebateIncome: string;

      /** 职场健康 返佣税费 */
      whRebateTax: string;

      /** 职场健康 销售发票内容 */
      whSaleInvoiceContent: string;

      /** 职场健康 销售发票类型 */
      whSaleInvoiceType: string;

      /** 职场健康 销售发票类型name */
      whSaleInvoiceTypeName: string;

      /** 职场健康 支付供货商货款时间 */
      whSupplierPaymentDt: string;

      /** workitemId */
      workitemId: string;
    }

    export class ContractFile {
      /** 审批节点 */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同附件ID */
      contractFileId: string;

      /** 大合同ID号 */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 附件ID */
      fileId: string;

      /** 附件名称 */
      fileName: string;

      /** 附件路径 */
      filePath: string;

      /** 附件类型 */
      fileType: string;

      /** 附件类型name */
      fileTypeName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程defId */
      processDefId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 上传步骤 */
      uploadStep: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ContractRetiree {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 人员姓名 */
      bz: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 大合同ID号 */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 人员姓名 */
      empName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 身份证号 */
      idCardNum: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 大合同退休人员主键 */
      retireeId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class DDCustQuery {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 客户账单年月 */
      billYm: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 客服 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户 */
      custId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 销售 */
      salesId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class DropdownList {
      /** 业务大类类型 */
      btType: string;

      /** chargeRate */
      chargeRate: string;

      /** cityId */
      cityId: string;

      /** cityIdForParty */
      cityIdForParty: string;

      /** cityName */
      cityName: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** contractName */
      contractName: string;

      /** contractSubType */
      contractSubType: string;

      /** contractSubTypeName */
      contractSubTypeName: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** currentSalesName */
      currentSalesName: string;

      /** departmentName */
      departmentName: string;

      /** englishTermName */
      englishTermName: string;

      /** exFeeMonth */
      exFeeMonth: string;

      /** 供应商收费模板 */
      exFeeTempltId: string;

      /** governingArea */
      governingArea: string;

      /** 所属大区 */
      governingAreaId: string;

      /** governingBranch */
      governingBranch: string;

      /** 所属分公司 */
      governingBranchId: string;

      /** groupType */
      groupType: string;

      /** 主键 */
      key: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 全称 */
      name: string;

      /** 拼音码 */
      pinYinCode: string;

      /** productLineId */
      productLineId: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 保留名字1 */
      reserveName1: string;

      /** 保留名字2 */
      reserveName2: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj: string;

      /** 缩写名 */
      shortName: string;

      /** signBrachTitleId */
      signBrachTitleId: string;

      /** signBranchTitleName */
      signBranchTitleName: string;

      /** 社保组ID */
      ssGroupId: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;
    }

    export class EvacuateWarning {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 客服竞争对手名称 */
      competitorName: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractId */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客服联系人手机 */
      cstScCall: string;

      /** 客服联系人 */
      cstScContact: string;

      /** 客服联系人邮件 */
      cstScEmail: string;

      /** 客服联系人电话 */
      cstScTel: string;

      /** 客服联系人传真 */
      cstScfax: string;

      /** 客服联系人职位 */
      cstScposition: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 客服反馈撤单时间 */
      reportEvacuatedDate: string;

      /** 客服撤单详细原因说明 */
      reportEvacuatedExplantion: string;

      /** 客服撤单原因分类 */
      reportEvacuatedReason: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 客服竞争对手优势 */
      sctScComAdvancetage: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** waringId */
      waringId: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class FilterEntity {
      /** activityNameCn */
      activityNameCn: string;

      /** activityNameEn */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractId */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByStr */
      createByStr: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** disaBatchId */
      disaBatchId: string;

      /** disaReasonId */
      disaReasonId: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** reasonBz */
      reasonBz: string;

      /** reasonId */
      reasonId: string;

      /** reasonStr */
      reasonStr: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** workitemId */
      workitemId: string;
    }

    export class HashMap<T0 = any, T1 = any> {}

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class PerfectSearchQuery {
      /** AlterstatusFW */
      AlterstatusFW: string;

      /** addConfirmStat */
      addConfirmStat: string;

      /** addconfirmstatusFW */
      addconfirmstatusFW: string;

      /** alterStat */
      alterStat: string;

      /** alterstatusFW */
      alterstatusFW: string;

      /** 接单方客服 */
      assigneeCsId: string;

      /** assigneeProviderId */
      assigneeProviderId: string;

      /** 派单方客服 */
      assignerCsId: string;

      /** assignerProviderId */
      assignerProviderId: string;

      /** category */
      category: string;

      /** chargeEndDate */
      chargeEndDate: string;

      /** 雇员费用段横表查询用时作为  "查询参考日" */
      chargeStartDate: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** employeeCode */
      employeeCode: string;

      /** employeeName */
      employeeName: string;

      /** endIndex */
      endIndex: number;

      /** 订单完善所要用的参数 */
      governingBranch: string;

      /** 订单完善所要用的参数 */
      governingBranch1: string;

      /** idCardNum */
      idCardNum: string;

      /** idCardType */
      idCardType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 产品id 多值 */
      productId: string;

      /** productName */
      productName: string;

      /** 产品名称，多值 */
      productNames: string;

      /** providerType */
      providerType: string;

      /** rptHireEndDt */
      rptHireEndDt: string;

      /** rptHireStartDt */
      rptHireStartDt: string;

      /** sepConfirmStat */
      sepConfirmStat: string;

      /** sepconfirmstatusFW */
      sepconfirmstatusFW: string;

      /** 社保公积金组id，多值，雇员费用段横表查询用  */
      ssGroupId: string;

      /** 社保公积金组名称，多值 */
      ssGroupNames: string;

      /** startIndex */
      startIndex: number;

      /** subcontractCode */
      subcontractCode: string;

      /** subcontractId */
      subcontractId: string;

      /** subcontractName */
      subcontractName: string;

      /** 产品类型 多值 */
      typeClass: string;

      /** 产品类型名称，多值 */
      typeClassNames: string;

      /** 当前登录人Id */
      userId: string;

      /** 当前登录人Id,报表要传两个userId，参数名不同值相同  */
      userId1: string;
    }

    export class QaPayRollQuery {
      /** 对应大区 */
      areaId: string;

      /** 大合同签单分公司 */
      branchId: string;

      /** custId */
      custId: string;

      /** 发放名称 */
      disburseName: string;

      /** endIndex */
      endIndex: number;

      /** 导入人 */
      importerName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** wageMonthEnd */
      wageMonthEnd: string;

      /** wageMonthStart */
      wageMonthStart: string;
    }

    export class RptComplaint {
      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CASE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      caseBy: string;

      /** caseByName */
      caseByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CASE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      caseDt: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CLOSE_CASE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      closeCase: string;

      /** closeCaseName */
      closeCaseName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COM_HAND_SAT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      comHandSat: string;

      /** comHandSatName */
      comHandSatName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAIN_DATE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      complainDate: string;

      /** complainDateGE */
      complainDateGE: string;

      /** complainDateLE */
      complainDateLE: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAIN_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      complainId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAINT_PERSON           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      complaintPerson: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.COMPLAINT_REASON           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      complaintReason: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CONTACT_TEL           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractCode */
      contractCode: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CONTRACT_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      contractId: string;

      /** contractName */
      contractName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CREATE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** createByName */
      createByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CREATE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      createDt: string;

      /** currentSales */
      currentSales: string;

      /** currentSalesName */
      currentSalesName: string;

      /** custCode */
      custCode: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.CUST_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** departmentId */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.DPE_CUST_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      dpeCustDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.EFFECT_TRACK           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      effectTrack: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.EMERGENCY_DEVICE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      emergencyDevice: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.EMERGENCY_GROUP           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      emergencyGroup: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.HANDLE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      handleBy: string;

      /** handleByName */
      handleByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.HANDLE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      handleDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.ID_CARD_NUM           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      idCardNum: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.IF_RECEIVE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      ifReceive: string;

      /** ifReceiveName */
      ifReceiveName: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.IS_DELETED           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** liabilityCs */
      liabilityCs: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.MIMIC_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.NOT_CASE_REASON           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      notCaseReason: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.NOTE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      noteBy: string;

      /** noteByList */
      noteByList: Array<defs.report.RptComplaintNoteBy>;

      /** noteByName */
      noteByName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** participant */
      participant: string;

      /** piDt */
      piDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PREVENT_MEASURES           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      preventMeasures: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PRO_DES           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      proDes: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processInsId */
      processInsId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PROCESSING_RESULT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      processingResult: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.PROXY_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.QA_DIS           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      qaDis: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receiveBy: string;

      /** receiveByName */
      receiveByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_COMPLAINT_AREA_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receiveComplaintAreaId: string;

      /** receiveComplaintAreaName */
      receiveComplaintAreaName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_COMPLAINT_DEPART_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receiveComplaintDepartId: string;

      /** receiveComplaintDepartName */
      receiveComplaintDepartName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_DEPART_ID           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receiveDepartId: string;

      /** receiveDepartName */
      receiveDepartName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_PE_TYPE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receivePeType: string;

      /** receivePeTypeName */
      receivePeTypeName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_TYPE           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receiveType: string;

      /** receiveTypeName */
      receiveTypeName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RECEIVE_WAY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      receiveWay: string;

      /** receiveWayName */
      receiveWayName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.REMARK           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.RETURN_VISIT_RESULT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      returnVisitResult: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.ROOT_ANALYSIS           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      rootAnalysis: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.STA_REQ           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      staReq: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.STANDARDIZATION           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      standardization: string;

      /** startIndex */
      startIndex: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.STATUS           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;

      /** toHandleDt */
      toHandleDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.TRAIN           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      train: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.UPDATE_BY           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      updateBy: string;

      /** updateByName */
      updateByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column RPT_COMPLAINT.UPDATE_DT           ibatorgenerated Thu Apr 27 15:35:04 CST 2017 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** workitemId */
      workitemId: string;
    }

    export class RptComplaintNoteBy {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** complainId */
      complainId: string;

      /** complainNoteById */
      complainNoteById: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** noteBy */
      noteBy: string;

      /** noteByName */
      noteByName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class financeRptQuery {
      /** areaId */
      areaId: string;

      /** billType */
      billType: string;

      /** billYM */
      billYM: string;

      /** bill_ym */
      bill_ym: string;

      /** createDtE */
      createDtE: string;

      /** createDtS */
      createDtS: string;

      /** custId */
      custId: string;

      /** cust_id */
      cust_id: string;

      /** departmentId */
      departmentId: string;

      /** deptId */
      deptId: string;

      /** endIndex */
      endIndex: number;

      /** feeBackGe */
      feeBackGe: string;

      /** feeBackLe */
      feeBackLe: string;

      /** finRecYm */
      finRecYm: string;

      /** governingArea */
      governingArea: string;

      /** isGroup */
      isGroup: string;

      /** isMerge */
      isMerge: string;

      /** isSelTemplts */
      isSelTemplts: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** payeeId */
      payeeId: string;

      /** print_code */
      print_code: string;

      /** print_prcedure */
      print_prcedure: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** receivable_templt_id */
      receivable_templt_id: string;

      /** signBranchTitleId */
      signBranchTitleId: string;

      /** spCode */
      spCode: string;

      /** startIndex */
      startIndex: number;

      /** userId */
      userId: string;
    }

    export class payrollDisabilityBenefitsQuery {
      /** 客户Id */
      custId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 薪资专员 */
      payrollSpecialistName: string;

      /** 查询类型：默认不传查残障金，工会经费传2 */
      queryType: string;

      /** startIndex */
      startIndex: number;

      /** 计税月 */
      taxMonth: string;

      /** 扣缴义务人id */
      withholdAgentId: string;
    }

    export class taxDeclarationEmpInfoQuery {
      /** 受理ID */
      acceptId: string;

      /** 申报反馈受理ID */
      backAcceptId: string;

      /** 唯一号 */
      batchId: string;

      /** 确认邀请人员任务ID */
      createBy: string;

      /** decLarationHisId */
      decLarationHisId: string;

      /** 个税申报任务主表ID */
      decLarationId: string;

      /** 作废受理ID */
      delAcceptId: string;

      /** 唯一号 */
      empCode: string;

      /** 唯一号 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 证件号码 */
      idCardNum: string;

      /** 所得类型 */
      incomeType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 受理ID */
      rdecAcceptId: string;

      /** 所得月份 */
      sdyf: string;

      /** 唯一号 */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** 任务状态 0.已生成任务，1已获取明细数据，2已生成报税数据，3已预报税提交，4已获取预报税结果，5已提交个税申报，6已获取个税申报结果 */
      taskStatus: string;

      /** 所得月份 */
      taskstatus: string;

      /** 排除表ID */
      taxDecOutId: string;

      /** 税差核算任务主表ID */
      taxDiffId: string;

      /** 确认邀请人员任务ID */
      taxInviteId: string;

      /** 计税月 */
      taxMonth: string;

      /** 主键 */
      taxTaskDetailId: string;

      /** 主任务id */
      taxTaskId: string;

      /** 更正受理ID */
      uptAcceptId: string;

      /** 发放ID */
      wageSendId: string;

      /** 扣缴义务人id */
      withholdAgentId: string;

      /** withholdAgentName */
      withholdAgentName: string;

      /** withholdAgentType */
      withholdAgentType: string;

      /** 姓名 */
      xm: string;

      /** 证照号码 */
      zzhm: string;

      /** 姓名 */
      zzlx: string;
    }
  }
}

declare namespace API {
  export namespace report {
    /**
     * 钉钉客户报表
     */
    export namespace ddCust {
      /**
        * 导出
导出
        * /reportDDCust/export
        */
      export namespace exportCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询钉钉客户
查询钉钉客户
        * /reportDDCust/queryDDCust
        */
      export namespace queryDDCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.FilterEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.DDCustQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.DDCustQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 撤单预警
     */
    export namespace evacuateWaring {
      /**
        * 导出
导出
        * /evacuateWaring/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新建撤单预警记录
新建撤单预警记录
        * /evacuateWaring/insertEvacuateWaring
        */
      export namespace insertEvacuateWaring {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.EvacuateWarning,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.EvacuateWarning,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询撤单预警
分页查询撤单预警
        * /evacuateWaring/queryEvacuateWaring
        */
      export namespace queryEvacuateWaring {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新客服联系人
更新客服联系人
        * /evacuateWaring/updateCstContract
        */
      export namespace updateCstContract {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.Contract,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.Contract,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 获得部分报表的下拉框数据
     */
    export namespace inDecMember {
      /**
        * 部门下拉列表
部门下拉列表
        * /inDecMember/getDepartmentDropdownList
        */
      export namespace getDepartmentDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Contract;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取服务产品下拉框
获取服务产品下拉框
        * /inDecMember/getServiceProductDropdownList
        */
      export namespace getServiceProductDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Contract;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 同时获得大区，以及分公司的列表信息
同时获得大区，以及分公司的列表信息
        * /inDecMember/initAreaBranchData
        */
      export namespace initAreaBranchData {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Contract;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 残障金数据报表
     */
    export namespace payrollDisabilityBenefitsReport {
      /**
        * 导出，经费queryType传2
导出，经费queryType传2
        * /disabilityBenefitsReport/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取是否存在大于0数据
获取是否存在大于0数据
        * /disabilityBenefitsReport/getListCount
        */
      export namespace getListCount {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.taxDeclarationEmpInfoQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.taxDeclarationEmpInfoQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取残障金数据报表数据，查工会经费queryType传2
获取残障金数据报表数据，查工会经费queryType传2
        * /disabilityBenefitsReport/getPayrollDisabilityBenefitsList
        */
      export namespace getPayrollDisabilityBenefitsList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.payrollDisabilityBenefitsQuery;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.payrollDisabilityBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.payrollDisabilityBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 报表相关
     */
    export namespace reportComplain {
      /**
        * 没有备注
没有备注
        * /reportComplain/approve
        */
      export namespace approve {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 没有备注
没有备注
        * /reportComplain/back
        */
      export namespace back {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 没有备注
没有备注
        * /reportComplain/backPrevious
        */
      export namespace backPrevious {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 没有备注
没有备注
        * /reportComplain/createRptComplaint
        */
      export namespace createRptComplaint {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /reportComplain/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 没有备注
没有备注
        * /reportComplain/queryRptComplaintNoteBy
        */
      export namespace queryRptComplaintNoteBy {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaintNoteBy,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaintNoteBy,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询分页
查询分页
        * /reportComplain/queryRptComplaintPage
        */
      export namespace queryRptComplaintPage {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询分页
查询分页
        * /reportComplain/queryRptComplaintPageApp
        */
      export namespace queryRptComplaintPageApp {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存
保存
        * /reportComplain/saveRptComplaint
        */
      export namespace saveRptComplaint {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新
更新
        * /reportComplain/updateRptComplaint
        */
      export namespace updateRptComplaint {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.RptComplaint,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 应收报表
     */
    export namespace reportFinance {
      /**
        * 每日首版账单客户查询
每日首版账单客户查询
        * /reportFinance/customerDailyVersion
        */
      export namespace customerDailyVersion {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /reportFinance/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 执行特殊账单明细的存储过程
执行特殊账单明细的存储过程
        * /reportFinance/generateSpecialBillPrc
        */
      export namespace generateSpecialBillPrc {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取可选的账套
获取可选的账套
        * /reportFinance/getAllBillTemplateFromDetail
        */
      export namespace getAllBillTemplateFromDetail {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 判断所需分的sheet数
判断所需分的sheet数
        * /reportFinance/getAuditOrNotifyCountNumberFromDetail
        */
      export namespace getAuditOrNotifyCountNumberFromDetail {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据登录用户id找到相关的特殊账单报表
根据登录用户id找到相关的特殊账单报表
        * /reportFinance/getBPXDropdownListByUserId
        */
      export namespace getBPXDropdownListByUserId {
        export class Params {
          /** userId */
          userId: string;
        }

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 返回当前选择的客户及客户账单年月下生成过账单的记录数
返回当前选择的客户及客户账单年月下生成过账单的记录数
        * /reportFinance/getBPXReceivevableNUm
        */
      export namespace getBPXReceivevableNUm {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据登录用户id找到相关的特殊账单报表
根据登录用户id找到相关的特殊账单报表
        * /reportFinance/getBPXTempltListByCustIdAndCode
        */
      export namespace getBPXTempltListByCustIdAndCode {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据客户id和账单年月返回账单模板个数
入参 cudtId billYM 必填，返回 {count:1}
        * /reportFinance/getReceivableTempltCount
        */
      export namespace getReceivableTempltCount {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据分公司获取客户下拉列表
根据分公司获取客户下拉列表
        * /reportFinance/getUserDropdownListByBranch
        */
      export namespace getUserDropdownListByBranch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.CommonResponse<Array<defs.report.DropdownList>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询对应的有账单的供应商数
查询对应的有账单的供应商数
        * /reportFinance/queryBillProviderNums
        */
      export namespace queryBillProviderNums {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询退费服务费报表数据
分页查询退费服务费报表数据
        * /reportFinance/queryRptServfeeReturn
        */
      export namespace queryRptServfeeReturn {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.financeRptQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 报表 - 薪资质控部分相关服务实现类
     */
    export namespace reportQaPayroll {
      /**
        * 判断所需分的sheet数
判断所需分的sheet数
        * /reportQaPayroll/queryMaxRoleByUser
        */
      export namespace queryMaxRoleByUser {
        export class Params {
          /** userId */
          userId: string;
        }

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询员工最大权限
查询员工最大权限
        * /reportQaPayroll/queryMaxRoleByUserEx
        */
      export namespace queryMaxRoleByUserEx {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.BizTypeInfo,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.BizTypeInfo,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 报表 - 质量管理相关服务
     */
    export namespace reportQc {
      /**
        * 获取 客户使用率和员工使用率的比率
获取 客户使用率和员工使用率的比率
        * /reportQC/fetchClientVisitedRatio
        */
      export namespace fetchClientVisitedRatio {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据登录的用户信息获得该用户的权限相关的值
根据登录的用户信息获得该用户的权限相关的值
        * /reportQC/fetchUserMaxRoleByUserId
        */
      export namespace fetchUserMaxRoleByUserId {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据大区获取城市下拉列表
根据大区获取城市下拉列表
        * /reportQC/getCityDropdownListByArea
        */
      export namespace getCityDropdownListByArea {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.CommonResponse<Array<defs.report.DropdownList>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 雇员费用段横表查询 获取表单数据
雇员费用段横表查询 获取表单数据
        * /reportQC/getEmpFeeProductForShow
        */
      export namespace getEmpFeeProductForShow {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.CommonResponse<
            Array<defs.report.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 雇员费用段横表查询 获取动态表头
雇员费用段横表查询 获取动态表头
        * /reportQC/getEmpFeeProductTitleForShow
        */
      export namespace getEmpFeeProductTitleForShow {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.CommonResponse<
            Array<defs.report.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 读配置文件memcached.properties满意度服务器地址
读配置文件memcached.properties满意度服务器地址
        * /reportQC/getMydServerPath
        */
      export namespace getMydServerPath {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 读配置文件memcached.properties满意度服务器地址
读配置文件memcached.properties满意度服务器地址
        * /reportQC/getMydServerPath
        */
      export namespace postGetMydServerPath {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新建BO日志信息
新建BO日志信息
        * /reportQC/insertBoLog
        */
      export namespace insertBoLog {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户端 客户使用率和员工使用率获取分页数据
客户端 客户使用率和员工使用率获取分页数据
        * /reportQC/queryClientVisitedPage
        */
      export namespace queryClientVisitedPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 雇员费用段横表查询 获取分页数据
雇员费用段横表查询 获取分页数据
        * /reportQC/queryEmpFeeProductPage
        */
      export namespace queryEmpFeeProductPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 雇员费用段横表查询 查询产品
雇员费用段横表查询 查询产品
        * /reportQC/queryProductReport
        */
      export namespace queryProductReport {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.report.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.PerfectSearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询BO数据抽取库中有多少条符合报表年月和分公司的数据
查询BO数据抽取库中有多少条符合报表年月和分公司的数据
        * /reportQC/queryQAMonthDataNumber
        */
      export namespace queryQAMonthDataNumber {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 薪资质控报表查询
     */
    export namespace wageSureData {
      /**
        * 薪资指控查询
薪资指控查询
        * /wageSureData/getWageSureData
        */
      export namespace getWageSureData {
        export class Params {}

        export type Response<T> = defs.report.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.report.QaPayRollQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.report.QaPayRollQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
