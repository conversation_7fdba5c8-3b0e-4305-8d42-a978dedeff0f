class AddEmpTransact {
  /** acct */
  acct = '';

  /** acctEmpName */
  acctEmpName = '';

  /** add */
  add = false;

  /** bankAcct */
  bankAcct = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 手机端预约id */
  bookingId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** btType */
  btType = '';

  /** busSubtypeId */
  busSubtypeId = '';

  /** busSubtypeName */
  busSubtypeName = '';

  /** busTypeId */
  busTypeId = '';

  /** busTypeName */
  busTypeName = '';

  /** businessId */
  businessId = '';

  /** businessStatus */
  businessStatus = '';

  /** businessStatusName */
  businessStatusName = '';

  /** categoryId */
  categoryId = '';

  /** categoryInfoId */
  categoryInfoId = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** contactTel */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** ebmBusinessSubTypeList */
  ebmBusinessSubTypeList = [];

  /** ebmHpTransactList */
  ebmHpTransactList = [];

  /** ebmMateralsList */
  ebmMateralsList = [];

  /** empBankCardId */
  empBankCardId = '';

  /** empCode */
  empCode = '';

  /** empHireSepId */
  empHireSepId = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** 福利办理方 */
  employerName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectProcessDt */
  expectProcessDt = '';

  /** fastMailCode */
  fastMailCode = '';

  /** 文件id */
  fileId = '';

  /** 文件名 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** finishDate */
  finishDate = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 办理进度查询 */
  handleProgressInquiry = '';

  /** 办理进度查询 */
  handleProgressInquiryText = '';

  /** idCardNum */
  idCardNum = '';

  /** ifInsert */
  ifInsert = '';

  /** ifReturn */
  ifReturn = '';

  /** ifReturnName */
  ifReturnName = '';

  /** important */
  important = '';

  /** importantName */
  importantName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isCutList */
  isCutList = '';

  /** isCutName */
  isCutName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 大户、单立户 */
  isIndependent = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 联名卡银行 */
  jointBankCard = '';

  /** 联名卡号 */
  jointCardNo = '';

  /** leadTel */
  leadTel = '';

  /** 医保手册号 */
  medicalHandbookNo = '';

  /** medicalStatus */
  medicalStatus = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 开户地 */
  openAddress = '';

  /** openBankName */
  openBankName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processor */
  processor = '';

  /** processorId */
  processorId = '';

  /** processorTel */
  processorTel = '';

  /** processorType */
  processorType = '';

  /** processorTypeName */
  processorTypeName = '';

  /** productId */
  productId = '';

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** returnType */
  returnType = '';

  /** returnTypeName */
  returnTypeName = '';

  /** 客户社保公积金账号 */
  scaAcct = '';

  /** 员工社保公积金账号 */
  seaAcct = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 选择办理人 */
  selTransactor = '';

  /** selTransactorName */
  selTransactorName = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 新增时间入离职状态 */
  sepStatus = '';

  /** sepStatusName */
  sepStatusName = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssInfoId */
  ssInfoId = '';

  /** ssStatus */
  ssStatus = '';

  /** ssStatusName */
  ssStatusName = '';

  /** status */
  status = '';

  /** statusName */
  statusName = '';

  /** subContractId */
  subContractId = '';

  /** subContractName */
  subContractName = '';

  /** 户口性质 */
  subTypeId = '';

  /** subTypeName */
  subTypeName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** transactCityId */
  transactCityId = '';

  /** 业务办理对象 */
  transactObject = '';

  /** 业务办理对象 */
  transactObjectText = '';

  /** transactResult */
  transactResult = '';

  /** transactResultName */
  transactResultName = '';

  /** transacter */
  transacter = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 福利办理月 */
  welfareProcessMon = '';

  /** 福利开始月 */
  welfareStartMon = '';

  /** whetherPay */
  whetherPay = '';

  /** 支取周期 */
  withdrawalPeriod = '';

  /** 支取周期显示文本 */
  withdrawalPeriodText = '';
}

class AddTransactDTO {
  /** 预约id */
  bookingId = '';

  /** elog */
  elog = new EbmTransactLog();

  /** 下一状态 */
  nextStatus = '';

  /** sql */
  statementName = '';

  /** 状态 */
  status = '';

  /** 对象 */
  transact = new AddEmpTransact();

  /** 办理id */
  transactId = '';

  /** 办理id串 */
  transactIds = '';
}

class AdjTaskQuery {
  /** 调整范围 1:个人订单、2:社保实做、3:个人订单+社保实做 */
  adjRange = '';

  /** adjRangeName */
  adjRangeName = '';

  /** 调基任务id */
  adjTaskId = '';

  /** 调基名称 */
  adjTaskName = '';

  /** adjTaskProduct */
  adjTaskProduct = [];

  /** adjTaskRange */
  adjTaskRange = [];

  /** adjTaskType */
  adjTaskType = '';

  /** adjTaskTypeName */
  adjTaskTypeName = '';

  /** adjType */
  adjType = '';

  /** 申请文件模板ID */
  applyFileId = '';

  /** applyFileName */
  applyFileName = '';

  /** 申请文件说明 */
  applyMemo = '';

  /** approvalBy */
  approvalBy = '';

  /** approvalDate */
  approvalDate = '';

  /** approvalOpinion */
  approvalOpinion = '';

  /** approvalOpinionCur */
  approvalOpinionCur = '';

  /** 接单客服id */
  assigneeCsId = '';

  /** 接单方分公司id */
  assigneeProviderId = '';

  /** assigneeProviderName */
  assigneeProviderName = '';

  /** 派单客服id */
  assignerCsId = '';

  /** 基数提交截止时间 */
  baseCommitEndDate = '';

  /** baseCommitEndDateGT */
  baseCommitEndDateGT = '';

  /** baseCommitEndDateLT */
  baseCommitEndDateLT = '';

  /** 城市 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** collectData */
  collectData = '';

  /** collectEmailResult */
  collectEmailResult = '';

  /** createByName */
  createByName = '';

  /** 客户 */
  custId = '';

  /** custName */
  custName = '';

  /** 调基人数 */
  empCount = '';

  /** endIndex */
  endIndex = undefined;

  /** fileId */
  fileId = '';

  /** finishEmailResult */
  finishEmailResult = '';

  /** 提交状态 */
  isCommit = '';

  /** 调基名单生成状态 ：0未生成1已生成 */
  isGenEmp = '';

  /** 0未锁定,1:已锁定 */
  isLocked = '';

  /** isLockedName */
  isLockedName = '';

  /** 注意事项 */
  memo = '';

  /** 订单调整状态： 0未调整，1调整中，2调整完成 */
  orderAdjStatus = '';

  /** orderAdjStatusName */
  orderAdjStatusName = '';

  /** orderAdjSubmitDate */
  orderAdjSubmitDate = '';

  /** pageName */
  pageName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 政策文件ID */
  policyFileId = '';

  /** policyFileName */
  policyFileName = '';

  /** 政策链接 */
  policyUrl = '';

  /** 供应商客服id */
  providerCsId = '';

  /** remindEmailResult */
  remindEmailResult = '';

  /** 实做调整状态 0未调整，1调整种，2调整完成 */
  ssAdjStatus = '';

  /** ssAdjStatusName */
  ssAdjStatusName = '';

  /** ssAdjSubmitDate */
  ssAdjSubmitDate = '';

  /** 组类别：1社保组2 公积金组 3社保公积基金组 */
  ssGroupType = '';

  /** 组类别名称 */
  ssGroupTypeName = '';

  /** startIndex */
  startIndex = undefined;

  /** 调基状态: 1 待核对 2 待审批 3 驳回 4 基数收集中5 收集完成 6调基开始月确认 7终止 */
  status = '';

  /** statusName */
  statusName = '';

  /** 拆分方分公司id */
  svcProviderId = '';

  /** svcProviderName */
  svcProviderName = '';

  /** updateByName */
  updateByName = '';

  /** userBranchId */
  userBranchId = '';

  /** userId */
  userId = '';

  /** 福利办理方 */
  welfareProcessor = '';

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class BaseEntity {
  /** add */
  add = false;

  /** 适用场景 */
  appScene = '';

  /** 适用场景名称 */
  appSceneName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司Id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建人名称 */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** 客户Id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 短信内容预览 */
  smsContent = '';

  /** 短信内容带key 例子${empName} */
  smsContent1 = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 供应商Id */
  supplierId = '';

  /** 供应商名称 */
  supplierName = '';

  /** templtId */
  templtId = '';

  /** 模板名称 */
  templtName = '';

  /** 模块类型9701员工劳动合同管理、9702员工离职材料管理、9703完善个人订单。 */
  templtType = '';

  /** 模块类型名称 */
  templtTypeName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改人名称 */
  updateByName = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本类型：1分公司版本、2客户版本、3集团版本 */
  versionType = '';

  /** 版本类型名称 */
  versionTypeName = '';
}

class CityProcessConfigQuery {
  /** 城市编码 */
  cityCode = '';

  /** 城市 */
  cityId = undefined;

  /** 城市名称 */
  cityName = '';

  /** empType */
  empType = undefined;

  /** isMedicalInsuranceCrossMonth */
  isMedicalInsuranceCrossMonth = undefined;

  /** isSocialSecurityCrossMonth */
  isSocialSecurityCrossMonth = undefined;

  /** pageNum */
  pageNum = undefined;

  /** pageSize */
  pageSize = undefined;

  /** 省份编码 */
  provinceCode = '';

  /** 省份 */
  provinceId = undefined;

  /** 省份名称 */
  provinceName = '';
}

class ClientExportConfig {
  /** className */
  className = '';

  /** expAnnoId */
  expAnnoId = '';

  /** exportFieldList */
  exportFieldList = [];

  /** exportParam */
  exportParam = undefined;

  /** exportType */
  exportType = undefined;

  /** paramMap */
  paramMap = undefined;

  /** remark */
  remark = '';

  /** serviceName */
  serviceName = '';

  /** serviceParam */
  serviceParam = undefined;
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new AddEmpTransact();

  /** message */
  message = '';

  /** t */
  t = new AddEmpTransact();
}

class Contract {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agereedAmtReceiveMon = '';

  /** agreedPayDt */
  agreedPayDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** areaId */
  areaId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** attTypeDraftId */
  attTypeDraftId = '';

  /** attTypeDraftName */
  attTypeDraftName = '';

  /** attTypeLegalId */
  attTypeLegalId = '';

  /** attTypeLegalName */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  billDt = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** isIssuingSalary */
  contractCategeryName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartA = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartB = '';

  /** contractProductLineIds */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractRetrieveDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStatus = '';

  /** contractStatusName */
  contractStatusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSubType = '';

  /** 合同类别（子类）name */
  contractSubTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** contractSvcStateName */
  contractSvcStateName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractTemplateId = '';

  /** contractTerminationDate */
  contractTerminationDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 合同类别 */
  contractVersionType = '';

  /** isIssuingSalary */
  contractVersionTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByName */
  createByName = '';

  /** createByParty */
  createByParty = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  createType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  creditPeriod = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  custPayerId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custSealDt = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerSize */
  customerSize = '';

  /** defStatus */
  defStatus = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** draftRemark */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** estimateFirstBillDate */
  estimateFirstBillDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectedIncrease */
  expectedIncrease = '';

  /** expectedIncreaseOld */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务name */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /** firstWgApproveId */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区Name */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司Name */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** private String contractSvcStateName; */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** governingBranchName */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** fileId */
  importFileId = '';

  /** fileName */
  importFileName = '';

  /** inId */
  inId = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** invoiceMoney */
  invoiceMoney = '';

  /** NP-8564 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
  isAdjustRenewContract = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  isAssign = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** isDefer */
  isDefer = '';

  /** isDeferName */
  isDeferName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** isIssuingSalary */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** isSameInsur */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** isSecondaryDev */
  isSecondaryDev = '';

  /** isSecondaryDevName */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApprovalStatus = '';

  /** legalRemark */
  legalRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** nextContractId */
  nextContractId = '';

  /** nextContractName */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** noChange */
  noChange = false;

  /** 非标合同审批单:NON_STA_COCT_APPR_ID */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  parentContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  payCollectPoint = '';

  /** payMonth */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  payerName = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApprovalStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  processInstanceId = '';

  /** productLineIdLogs */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectPlanRequest = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectRemark = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerType */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 报价单集合id */
  quoIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  renewedContractNum = '';

  /** reportElEvacuatedDate */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** reportGlEvacuatedDate */
  reportGlEvacuatedDate = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** roleCode */
  roleCode = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增存量标识 （手工） */
  salFlagManual = '';

  /** salFlagManualName */
  salFlagManualName = '';

  /** salFlagName */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApprove = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealApproveStatus = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealOpinion = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signArea */
  signArea = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** signBranchTitleIdAreaId */
  signBranchTitleIdAreaId = '';

  /** signBranchTitleIdBranchId */
  signBranchTitleIdBranchId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 撤单原因 */
  stopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcRegion = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadFileName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadUrl = '';

  /** upt */
  upt = false;

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** workitemId */
  workitemId = '';
}

class ContractFile {
  /** 审批节点 */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同附件ID */
  contractFileId = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 附件ID */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 附件路径 */
  filePath = '';

  /** 附件类型 */
  fileType = '';

  /** 附件类型name */
  fileTypeName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程defId */
  processDefId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传步骤 */
  uploadStep = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractRetiree {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 人员姓名 */
  bz = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 人员姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 身份证号 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 大合同退休人员主键 */
  retireeId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustAcct {
  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.ACCT	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acct = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.ACCT_BANK_NAME           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctBankName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.ACCT_NAME           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.ACCT_NUM           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctNum = '';

  /** add */
  add = false;

  /** appointpayLastDay */
  appointpayLastDay = '';

  /** appointpayLastMon */
  appointpayLastMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.BANK_TYPE           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  bankType = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CITY_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custAcctId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** forId */
  forId = undefined;

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** ifInSalaryPlatform */
  ifInSalaryPlatform = '';

  /** ifSSPay */
  ifSSPay = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** openAddress */
  openAddress = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** payableBranchId */
  payableBranchId = '';

  /** payableFirstMon */
  payableFirstMon = '';

  /** payableFrequency */
  payableFrequency = '';

  /** payeeName */
  payeeName = '';

  /** pk */
  pk = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.PROVINCE_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  provinceId = undefined;

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.REMARK           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.SS_GROUP_ID	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssPayableMon */
  ssPayableMon = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class EbmBusinessIfs {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** businessIfsId */
  businessIfsId = '';

  /** businessSubtypeId */
  businessSubtypeId = '';

  /** businessSubtypeName */
  businessSubtypeName = '';

  /** businessTypeId */
  businessTypeId = '';

  /** businessTypeName */
  businessTypeName = '';

  /** 业务项目 */
  busnameClassId = '';

  /** 业务项目名称 */
  busnameClassName = '';

  /** 所属类型 */
  categoryId = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** uptUserName */
  uptUserName = '';

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EbmBusinessImpTask {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** businessIfsId */
  businessIfsId = '';

  /** businessStatus */
  businessStatus = '';

  /** businessStatusText */
  businessStatusText = '';

  /** businessSubtypeId */
  businessSubtypeId = '';

  /** businessSubtypeName */
  businessSubtypeName = '';

  /** businessTypeId */
  businessTypeId = '';

  /** businessTypeName */
  businessTypeName = '';

  /** 业务项目名称 */
  busnameClassName = '';

  /** 所属类型 */
  categoryId = '';

  /** 所属类型 */
  categoryName = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** createName */
  createName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** handleEndDt */
  handleEndDt = '';

  /** handleStartDt */
  handleStartDt = '';

  /** impTaskId */
  impTaskId = '';

  /** impTaskName */
  impTaskName = '';

  /** impTaskType */
  impTaskType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** transactResult */
  transactResult = '';

  /** transactResultText */
  transactResultText = '';

  /** transactText */
  transactText = '';

  /** transacterId */
  transacterId = '';

  /** transacterName */
  transacterName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EbmBusinessSubType {
  /** add */
  add = false;

  /** bank */
  bank = '';

  /** bankAcct */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busSubTypeId */
  busSubTypeId = '';

  /** busSubTypeName */
  busSubTypeName = '';

  /** businessId */
  businessId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 上传附件id */
  fileId = '';

  /** 上传附件名称 */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** nRemark */
  nRemark = '';

  /** noChange */
  noChange = false;

  /** 支付标识：0未支付，1 已支付 */
  paymentStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realAmount */
  realAmount = '';

  /** remark */
  remark = '';

  /** reportAmount */
  reportAmount = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** result */
  result = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** transactTypeId */
  transactTypeId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** whetherPay */
  whetherPay = '';
}

class EbmHpTransact {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** businessHosId */
  businessHosId = '';

  /** businessId */
  businessId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** countyName */
  countyName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hosCode */
  hosCode = '';

  /** hosId */
  hosId = '';

  /** hosLevel */
  hosLevel = '';

  /** hosName */
  hosName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EbmIfsItem {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** businessIfsId */
  businessIfsId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** ifsItemId */
  ifsItemId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isText */
  isText = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** itemCode */
  itemCode = '';

  /** itemName */
  itemName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EbmMaterals {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busSubTypeId */
  busSubTypeId = '';

  /** businessId */
  businessId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isOriginal */
  isOriginal = '';

  /** isReturn */
  isReturn = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** materialsAccount */
  materialsAccount = '';

  /** materialsId */
  materialsId = '';

  /** materialsName */
  materialsName = '';

  /** 模拟人 */
  mimicBy = '';

  /** nRemark */
  nRemark = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** traMaterialsId */
  traMaterialsId = '';

  /** transactTypeId */
  transactTypeId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EbmTransactLog {
  /** add */
  add = false;

  /** afterStatus */
  afterStatus = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** beforeStatus */
  beforeStatus = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** businessId */
  businessId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** logId */
  logId = '';

  /** logType */
  logType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EbmTransactQuery {
  /** 业务小类 */
  busSubtypeId = '';

  /** 业务大类 */
  busTypeId = '';

  /** 业务状态 */
  businessStatus = '';

  /** 所属类型id */
  categoryId = '';

  /** 所属城市 */
  cityId = '';

  /** 业务开始时间止 */
  createDtEnd = '';

  /** 业务开始时间起 */
  createDtStart = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 数据来源 */
  dataSource = '';

  /** 唯一号 */
  empCode = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 预计办理时间 */
  expectProcessDt = '';

  /** 业务结束时间止 */
  finishDtEnd = '';

  /** 业务结束时间起 */
  finishDtStart = '';

  /** 证件号码 */
  idCardNum = '';

  /** 查询权限 */
  myType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 待办人 */
  processorId = '';

  /** 查询类型，定点医疗机构 */
  selType = '';

  /** 入离职状态 */
  sepStatus = '';

  /** startIndex */
  startIndex = undefined;

  /** 办理对象 */
  transactObject = '';

  /** 办理结果 */
  transactResult = '';

  /** 办理进度 */
  transactStatus = '';

  /** 经办人 */
  transacterId = '';

  /** 用户id */
  userId = '';
}

class EleContractInfoDTO {
  /** 电子合同文档id */
  eleContractDocId = '';

  /** 电子合同编号 */
  eleContractId = '';

  /** 主键 */
  eleContractInfoId = '';

  /** 电子合同名称 */
  eleContractName = '';

  /** 电子合同状态 */
  eleContractStatus = '';

  /** 电子合同状态（显示） */
  eleContractStatusName = '';

  /** 劳动合同id */
  laborContractId = '';
}

class EleContractInfoQuery {
  /** 电子合同文档id */
  eleContractDocId = '';

  /** 电子合同编号 */
  eleContractId = '';

  /** 主键 */
  eleContractInfoId = '';

  /** 电子合同名称 */
  eleContractName = '';

  /** 电子合同状态 */
  eleContractStatus = '';

  /** 电子合同状态（显示） */
  eleContractStatusName = '';

  /** endIndex */
  endIndex = undefined;

  /** 劳动合同id */
  laborContractId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class EmpAcct {
  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.ACCT	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acct = '';

  /** add */
  add = false;

  /** appointpayLastDay */
  appointpayLastDay = '';

  /** appointpayLastMon */
  appointpayLastMon = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** companyCode */
  companyCode = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_EMP_ACCT.EMP_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  empAcctId = undefined;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_EMP_ACCT.EMP_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  empId = undefined;

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** entrustBankAccount */
  entrustBankAccount = '';

  /** entrustBankName */
  entrustBankName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** forId */
  forId = undefined;

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hrCode */
  hrCode = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** ifInSalaryPlatform */
  ifInSalaryPlatform = '';

  /** ifSSPay */
  ifSSPay = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** payableBranchId */
  payableBranchId = '';

  /** payableFirstMon */
  payableFirstMon = '';

  /** payableFrequency */
  payableFrequency = '';

  /** payeeName */
  payeeName = '';

  /** pk */
  pk = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_EMP_ACCT.REMARK           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.SS_GROUP_ID	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssPayableMon */
  ssPayableMon = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmpAcctDTO {
  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.ACCT	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acct = '';

  /** appointpayLastDay */
  appointpayLastDay = '';

  /** appointpayLastMon */
  appointpayLastMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custAcctId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_EMP_ACCT.EMP_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  empAcctId = undefined;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_EMP_ACCT.EMP_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  empId = undefined;

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** entrustBankAccount */
  entrustBankAccount = '';

  /** entrustBankName */
  entrustBankName = '';

  /** forId */
  forId = undefined;

  /** hrCode */
  hrCode = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** ifInSalaryPlatform */
  ifInSalaryPlatform = '';

  /** ifSSPay */
  ifSSPay = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** payableBranchId */
  payableBranchId = '';

  /** payableFirstMon */
  payableFirstMon = '';

  /** payableFrequency */
  payableFrequency = '';

  /** payeeName */
  payeeName = '';

  /** pk */
  pk = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_EMP_ACCT.REMARK           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  remark = '';

  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.SS_GROUP_ID	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssPayableMon */
  ssPayableMon = '';

  /** startIndex */
  startIndex = undefined;
}

class EmpPaymentResult {
  /** 征集单号 */
  collectionNumber = '';

  /** 足额到账标志 */
  fullPaymentFlag = '';

  /** 证件号码 */
  idNumber = '';

  /** 证件类型 */
  idType = '';

  /** 险种类型 */
  insuranceType = '';

  /** 姓名 */
  name = '';

  /** 缴费单位 */
  payFirmName = '';

  /** 应缴费额 */
  payableAmount = '';

  /** 缴费基数 */
  paymentBase = '';

  /** 到账日期 */
  paymentDate = '';

  /** 费款所属期止 */
  paymentPeriodEnd = '';

  /** 费款所属期起 */
  paymentPeriodStart = '';

  /** 缴费类型 */
  paymentType = '';

  /** 费率 */
  rate = '';

  /** 社保组 */
  socialSecurityGroup = '';

  /** 更新日期 */
  updateDate = '';

  /** 福利办理方 */
  welfareAgent = '';
}

class EmployeeFee {
  /** add */
  add = false;

  /** 调整情况分类：1费用添加、2费用清空、3比例调整、4基数调整、5基数+比例调整、6其他调整 */
  adjSituType = '';

  /** 金额 */
  amount = undefined;

  /** 金额(不含税) */
  amtNoTax = '';

  /** 附加税费 */
  atr = '';

  /** 基数绑定级次 */
  baseBindingLevel = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单起始月 */
  billStartMonth = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 类型 */
  category = '';

  /** 收费结束时间 */
  chargeEndDate = '';

  /** 缴费频率 */
  chargeRate = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** cityId */
  cityId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 企业附加金额 */
  eAdditionalAmt = undefined;

  /** 企业金额 */
  eAmt = undefined;

  /** 企业基数 */
  eBase = undefined;

  /** eBillTemplt */
  eBillTemplt = '';

  /** 企业账单模板id */
  eBillTempltId = '';

  /** 企业计算方法:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
  eCalculationMethod = '';

  /** 企业收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  eFeeMonth = '';

  /** 收费模板名 */
  eFeeTemplt = '';

  /** 企业收费模板id */
  eFeeTempltId = '';

  /** 企业频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  eFrequency = '';

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业提前几个月收,默认为0，选项0-3 */
  eMonthInAdvance = '';

  /** 企业精确值0：0位小数1：1位小数2：2位小数5： 精确值 */
  ePrecision = '';

  /** 企业比例 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** 费用段历史id */
  empFeeHisId = '';

  /** 费用段id */
  empFeeId = '';

  /** 费用段操作id */
  empFeeOprId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 外部供应商账单起始月 */
  exBillStartMonth = '';

  /** 供应商收费月 */
  exFeeMonth = '';

  /** 供应商收费模板名 */
  exFeeTemplt = '';

  /** 外部供应商收费模板id */
  exFeeTempltId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否一次性付费 */
  isOneTimePay = '';

  /** 是否显示 1:是0:否 */
  isShow = '';

  /** 是否更新月度表1:是0:否 */
  isUptFeeMon = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 原金额 */
  oldAmount = '';

  /** 原账单起始月 */
  oldBillStartMonth = '';

  /** 个人附加金额 */
  pAdditionalAmt = undefined;

  /** 个人金额 */
  pAmt = undefined;

  /** 个人基数 */
  pBase = undefined;

  /** pBillTemplt */
  pBillTemplt = '';

  /** 个人部分账单模板id */
  pBillTempltId = '';

  /** 个人计算方式:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
  pCalculationMethod = '';

  /** 个人收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  pFeeMonth = '';

  /** 收费模板名 */
  pFeeTemplt = '';

  /** 个人收费模板id */
  pFeeTempltId = '';

  /** 个人频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  pFrequency = '';

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人提前几个月收,提前几个月收,默认为0，选项0-3 */
  pMonthInAdvance = '';

  /** 个人精度0：0位小数1：1位小数2：2位小数5： 精确值 */
  pPrecision = '';

  /** 个人比例 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** 支付频率1:月缴,2季度缴,3年缴(不足一年按年缴),4年缴(不足一年按月缴) */
  payFrequency = '';

  /** 支付最后服务年月 */
  payLastServiceMonth = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 产品比例id */
  productRatioId = '';

  /** 产品比例名称 */
  productRatioName = '';

  /** 产品类型id */
  productTypeId = undefined;

  /** 供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单id */
  quotationId = '';

  /** 报价单子项id */
  quotationItemId = '';

  /** 应收金额 */
  receivableAmt = undefined;

  /** 应收几个月 */
  receivableMonth = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保组id */
  ssGroupId = '';

  /** 社保组名称 */
  ssGroupName = '';

  /** 社保福利包id */
  ssWelfarePkgId = '';

  /** 社保福利包名称 */
  ssWelfarePkgName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 标签 */
  tag = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 增值税 */
  vat = '';

  /** 增值税率 */
  vatr = '';

  /** 实收金额 */
  verifyAmt = undefined;

  /** 实收金额(不含税) */
  verifyAmtNoTax = '';

  /** 实收金额增值税 */
  verifyAmtVat = '';
}

class EmployeeFile {
  /** add */
  add = false;

  /** assigneeProvider */
  assigneeProvider = '';

  /** assignerProvider */
  assignerProvider = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractName */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** empFileDetailList */
  empFileDetailList = [];

  /** empHireSepId */
  empHireSepId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.EMP_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE_DETAIL.END_MONTH	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  endMonth = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.FILE_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  fileId = '';

  /** fileNo */
  fileNo = '';

  /** fileProvider */
  fileProvider = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.FILE_PROVIDER_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  fileProviderId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** frequency */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hireDt */
  hireDt = '';

  /** idCardNum */
  idCardNum = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.IN_DT	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  inDt = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** months */
  months = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.OUT_DT	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  outDt = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** price */
  price = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.REMARK	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  remark = '';

  /** reportEndMonth */
  reportEndMonth = '';

  /** reportStartMonth */
  reportStartMonth = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** seq */
  seq = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE_DETAIL.START_MONTH	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  startMonth = '';

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** subcontractName */
  subcontractName = '';

  /** total */
  total = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmployeeFileDetail {
  /** add */
  add = false;

  /** assigneeProvider */
  assigneeProvider = '';

  /** assignerProvider */
  assignerProvider = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractName */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** empFileDetailList */
  empFileDetailList = [];

  /** empHireSepId */
  empHireSepId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.EMP_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE_DETAIL.END_MONTH	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  endMonth = '';

  /** exProvider */
  exProvider = '';

  /** exProviderId */
  exProviderId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE_DETAIL.FILE_DETAIL_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  fileDetailId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.FILE_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  fileId = '';

  /** fileNo */
  fileNo = '';

  /** fileProvider */
  fileProvider = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.FILE_PROVIDER_ID	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  fileProviderId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** frequency */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hireDt */
  hireDt = '';

  /** idCardNum */
  idCardNum = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.IN_DT	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  inDt = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** months */
  months = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.OUT_DT	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  outDt = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** price */
  price = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE.REMARK	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  remark = '';

  /** reportEndMonth */
  reportEndMonth = '';

  /** reportStartMonth */
  reportStartMonth = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** seq */
  seq = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FILE_DETAIL.START_MONTH	  	  ibatorgenerated Tue May 14 11:23:17 CST 2013 */
  startMonth = '';

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** subcontractName */
  subcontractName = '';

  /** total */
  total = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmployeeFileQuery {
  /** 客户名称 */
  custName = '';

  /** 雇员编码 */
  empCode = '';

  /** 雇员姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 截至月 */
  endMonth = '';

  /** 文件供应商id */
  fileProviderId = '';

  /** 证件号码 */
  idCardNum = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 开始月 */
  startMonth = '';
}

class EntPaymentResult {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 完税证明文件名 */
  certificateFileName = '';

  /**  城市编码 */
  cityCode = '';

  /** cityId */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** companyName */
  companyName = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /**  缴费实体id */
  custPayEntityId = '';

  /**  缴费实体名称 */
  custPayEntityName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** detailId */
  detailId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /**  费用年月 */
  feeMonth = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否单立户 */
  isIndependent = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /**  主键 */
  paymentResultId = '';

  /**  缴费凭证minio下载地址 */
  paymentVoucher = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssBatchId */
  ssBatchId = '';

  /**  社保组ID */
  ssGroupId = '';

  /**  社保组名称 */
  ssGroupName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /**  完税证明下载minio地址 */
  taxPaymentCertificate = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 缴费凭证文件名 */
  voucherFileName = '';

  /**  福利办理方 */
  welfareProcessor = '';

  /**  福利办理方名称 */
  welfareProcessorName = '';
}

class ExOneChargesDTO {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** areaId */
  areaId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单年月 */
  billYm = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 财务应收年月 */
  finReceiableYm = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 人数 */
  headCount = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 金额 */
  oldamount = undefined;

  /** onecharesId */
  onecharesId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLine = '';

  /** 产品名称 */
  productName = '';

  /** 产品大类 */
  productTypeId = '';

  /** providerId */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = '';

  /** quotationItemId */
  quotationItemId = '';

  /** receivableId */
  receivableId = '';

  /** 帐套名称 */
  receivableTempltName = '';

  /** 注释 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** templtId */
  templtId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExportField {
  /** dataType */
  dataType = undefined;

  /** fieldName */
  fieldName = '';

  /** fieldText */
  fieldText = '';

  /** tag */
  tag = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class File {
  /** absolute */
  absolute = false;

  /** absoluteFile */
  absoluteFile = {};

  /** absolutePath */
  absolutePath = '';

  /** canonicalFile */
  canonicalFile = {};

  /** canonicalPath */
  canonicalPath = '';

  /** directory */
  directory = false;

  /** executable */
  executable = false;

  /** file */
  file = false;

  /** freeSpace */
  freeSpace = undefined;

  /** hidden */
  hidden = false;

  /** lastModified */
  lastModified = undefined;

  /** name */
  name = '';

  /** parent */
  parent = '';

  /** parentFile */
  parentFile = {};

  /** path */
  path = '';

  /** readable */
  readable = false;

  /** totalSpace */
  totalSpace = undefined;

  /** usableSpace */
  usableSpace = undefined;

  /** writable */
  writable = false;
}

class FileInfo {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** bizType */
  bizType = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileData */
  fileData = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filePath */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hisFileId */
  hisFileId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 级联查询条件,逐级网上找 */
  levelQueryByFileId = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** nonStaCoctApprId */
  nonStaCoctApprId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** parameters */
  parameters = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realPath */
  realPath = '';

  /** recordId */
  recordId = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** uploadBy */
  uploadBy = '';

  /** uploadByName */
  uploadByName = '';

  /** uploadDt */
  uploadDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class FilePayDetail {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.ASSIGNEE_PROVIDER_ID           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  assigneeProviderId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.ASSIGNEE_PROVIDER_NAME           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  assigneeProviderName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.ASSIGNER_PROVIDER_ID           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  assignerProviderId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.ASSIGNER_PROVIDER_NAME           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  assignerProviderName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.CHARGE_RATE           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  chargeRate = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.CUST_ID           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.EMP_ID           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.END_MONTH           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  endMonth = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileNo */
  fileNo = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.FILE_PAY_DETAIL_ID           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  filePayDetailId = '';

  /** fileProviderId */
  fileProviderId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** idCardNum */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.MONTHS           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  months = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.PAY_AUDIT_ID           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  payAuditId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.PRICE           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  price = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.START_MONTH           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  startMonth = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_FILE_PAY_DETAIL.TOTAL           ibatorgenerated Tue Aug 20 13:59:05 CST 2013 */
  total = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class FilterEntity {
  /** add */
  add = false;

  /** 适用场景 */
  appScene = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司Id */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户Id */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 供应商Id */
  supplierId = '';

  /** templtId */
  templtId = '';

  /** 模板名称 */
  templtName = '';

  /** 模块类型9701员工劳动合同管理、9702员工离职材料管理、9703完善个人订单。 */
  templtType = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本类型：1分公司版本、2客户版本、3集团版本 */
  versionType = '';
}

class GenerateBillContionDTO {
  /** 大区ID */
  areaId = '';

  /** 账单年月 */
  billYm = '';

  /** billYmEd */
  billYmEd = '';

  /** billYmSt */
  billYmSt = '';

  /** billtempId */
  billtempId = '';

  /** chargeDTOs */
  chargeDTOs = [];

  /** chargesDTOs */
  chargesDTOs = [];

  /** contractIds */
  contractIds = '';

  /** createDt */
  createDt = '';

  /** 生成人 */
  creater = '';

  /** custCode */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** custName */
  custName = '';

  /** 部门ID */
  departmentId = '';

  /** empCode */
  empCode = '';

  /** 雇员ID */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 应收年月 */
  finReceivableYm = '';

  /** finReceivableYmEd */
  finReceivableYmEd = '';

  /** finReceivableYmSt */
  finReceivableYmSt = '';

  /** genType */
  genType = '';

  /** ids */
  ids = '';

  /** invoiceStatus */
  invoiceStatus = '';

  /** 是否锁定 */
  isLocked = '';

  /** 是否支付申请 */
  isPayed = '';

  /** isoldData */
  isoldData = '';

  /** 0 客户一次性项目 1 外包一次性税费 */
  onechargesType = '';

  /** 帐套ID */
  pBillTempltId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** payeeId */
  payeeId = '';

  /** 账单方ID */
  payerId = '';

  /** 产品ID */
  productId = '';

  /** 大区ID */
  providerId = '';

  /** 报价类型 */
  quotationItemType = '';

  /** receivableAmt */
  receivableAmt = '';

  /** receivableAmtEd */
  receivableAmtEd = '';

  /** receivableAmtSt */
  receivableAmtSt = '';

  /** 应收主记录ID */
  receivableId = '';

  /** 帐套ID */
  receivableTempltId = '';

  /** 应收ID串 */
  receivableTempltIds = '';

  /** receivableTempltName */
  receivableTempltName = '';

  /** 账单版本ID */
  receivableVersionId = '';

  /** genType */
  sdr = '';

  /** 服务年月 */
  serviceMonth = '';

  /** 社保组ID */
  ssGroupId = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 帐套ID */
  tempId = '';

  /** tempIdArray */
  tempIdArray = '';

  /** userId */
  userId = '';

  /** verifyStatus */
  verifyStatus = '';

  /** wageIds */
  wageIds = '';

  /** zdr */
  zdr = '';
}

class HashMap {}

class HsLaborEosApproveDetailQuery {
  /** eos_emp_add增员表主键 */
  empAddId = undefined;

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class HsLaborFileBatch {
  /** add */
  add = false;

  /** 上传批次号 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 上传人id */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 上传日期<= */
  createDtEt = '';

  /** 上传日期>= */
  createDtSt = '';

  /** 上传人姓名 */
  createbyName = '';

  /** 上传时间 */
  createdt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 上传记录数 */
  fileCount = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class HsLaborFileBatchItem {
  /** add */
  add = false;

  /** 上传批次号 */
  batchId = '';

  /** batchItemId */
  batchItemId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 上传人id */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 上传人姓名 */
  createbyName = '';

  /** 上传时间 */
  createdt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 失败原因 */
  errorInfo = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 文件id */
  fileId = '';

  /** 文件名 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 上传结果，0成功  1失败 */
  impTag = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传方式：1单个功能上传 2minio批量上传 3EHR历史数据同步 */
  uploadType = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class HsLaborctEosRenew {
  /** add */
  add = false;

  /** 提交时间 */
  applyDate = '';

  /** 提交日期从 */
  applyDateFrom = '';

  /** 提交日期到 */
  applyDateTo = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 易才处理完成时间 */
  checkDate = '';

  /** 易才处理意见 */
  checkMemo = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 接口提交员工上下岗id */
  empHiresepId = '';

  /** 接口提交员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误信息 */
  errInfo = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** HRO查询出的客户编号 */
  hroCustCode = '';

  /** HRO查询出的客户id */
  hroCustId = '';

  /** HRO查询出的客户名称 */
  hroCustName = '';

  /** HRO查询出的唯一号 */
  hroEmpCode = '';

  /** HRO查询出的上下岗id */
  hroEmpHiresepId = '';

  /** HRO查询出的雇员id */
  hroEmpId = '';

  /** HRO查询出的姓名 */
  hroEmpName = '';

  /** HRO查询出的证件号码 */
  hroIdCardNum = '';

  /** HRO查询出的劳动合同id */
  hroLaborCtId = '';

  /** 接口提交证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同续签任务编号 */
  labRenewId = '';

  /** 接口提交劳动合同ID */
  laborContractId = '';

  /** 模拟人 */
  mimicBy = '';

  /** 接口提交新劳动合同终止日期 */
  newLabDateEnd = '';

  /** 接口提交新劳动合同起始日期 */
  newLabDateStart = '';

  /** 新劳动合同ID */
  newLaborCtId = '';

  /** 接口提交劳动合同续签任务备注 */
  newMemo = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态： 1. 提交成功 2. 数据错误 3. 已处理 4.不需处理 5.不需续签 */
  status = '';

  /** 状态（展示） */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HsLaborctEosRenewDTO {
  /** 提交时间 */
  applyDate = '';

  /** 提交日期从 */
  applyDateFrom = '';

  /** 提交日期到 */
  applyDateTo = '';

  /** 易才处理完成时间 */
  checkDate = '';

  /** 易才处理意见 */
  checkMemo = '';

  /** 接口提交员工上下岗id */
  empHiresepId = '';

  /** 接口提交员工姓名 */
  empName = '';

  /** 错误信息 */
  errInfo = '';

  /** HRO查询出的客户编号 */
  hroCustCode = '';

  /** HRO查询出的客户id */
  hroCustId = '';

  /** HRO查询出的客户名称 */
  hroCustName = '';

  /** HRO查询出的唯一号 */
  hroEmpCode = '';

  /** HRO查询出的上下岗id */
  hroEmpHiresepId = '';

  /** HRO查询出的雇员id */
  hroEmpId = '';

  /** HRO查询出的姓名 */
  hroEmpName = '';

  /** HRO查询出的上下岗id */
  hroIdCardNum = '';

  /** HRO查询出的劳动合同id */
  hroLaborCtId = '';

  /** 接口提交证件号码 */
  idCardNum = '';

  /** 劳动合同续签任务编号 */
  labRenewId = '';

  /** 接口提交劳动合同ID */
  laborContractId = '';

  /** 接口提交新劳动合同终止日期 */
  newLabDateEnd = '';

  /** 接口提交新劳动合同起始日期 */
  newLabDateStart = '';

  /** 新劳动合同ID */
  newLaborCtId = '';

  /** 接口提交劳动合同续签任务备注 */
  newMemo = '';

  /** 状态： 1. 提交成功 2. 数据错误 3. 已处理 4.不需处理 5.不需续签 */
  status = '';

  /** 状态（展示） */
  statusName = '';
}

class HsLaborctEosRenewQuery {
  /** 提交时间 */
  applyDate = '';

  /** 提交日期从 */
  applyDateFrom = '';

  /** 提交日期到 */
  applyDateTo = '';

  /** 易才处理完成时间 */
  checkDate = '';

  /** 易才处理意见 */
  checkMemo = '';

  /** 接口提交员工上下岗id */
  empHiresepId = '';

  /** 接口提交员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误信息 */
  errInfo = '';

  /** HRO查询出的客户编号 */
  hroCustCode = '';

  /** HRO查询出的客户id */
  hroCustId = '';

  /** HRO查询出的客户名称 */
  hroCustName = '';

  /** HRO查询出的唯一号 */
  hroEmpCode = '';

  /** HRO查询出的上下岗id */
  hroEmpHiresepId = '';

  /** HRO查询出的雇员id */
  hroEmpId = '';

  /** HRO查询出的姓名 */
  hroEmpName = '';

  /** HRO查询出的上下岗id */
  hroIdCardNum = '';

  /** HRO查询出的劳动合同id */
  hroLaborCtId = '';

  /** 接口提交证件号码 */
  idCardNum = '';

  /** 劳动合同续签任务编号 */
  labRenewId = '';

  /** 接口提交劳动合同ID */
  laborContractId = '';

  /** 接口提交新劳动合同终止日期 */
  newLabDateEnd = '';

  /** 接口提交新劳动合同起始日期 */
  newLabDateStart = '';

  /** 新劳动合同ID */
  newLaborCtId = '';

  /** 接口提交劳动合同续签任务备注 */
  newMemo = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 状态： 1. 提交成功 2. 数据错误 3. 待续签 4.不需处理 5.不需续签 6 续签执行中 9 续签完成 */
  status = '';

  /** 状态（展示） */
  statusName = '';
}

class HzSupplyCert {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 证件id */
  certId = '';

  /** 证件名称 */
  certName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否必填 */
  isMust = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 主键 */
  supplyCertId = '';

  /** 批次id */
  supplyId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传方 1客服 2个人 */
  uploadType = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HzSupplyDTO {
  /** add */
  add = false;

  /** 审批状态1、初始化，2、待上传，3、待审核，4、已驳回、5、审批通过、6、已终止 */
  auditStatus = '';

  /** 审批状态名称 */
  auditStatusName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同类别 */
  contractTypeName = '';

  /** 合同版本名称 */
  contractVersionName = '';

  /** createBy */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建时间小于等于 */
  createDtEt = '';

  /** 创建时间大于等于 */
  createDtSt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户Id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 电子合同编号 */
  eleContractId = '';

  /** 电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
  eleContractStatus = '';

  /** 电子合同状态名称 */
  eleContractStatusName = '';

  /** 唯一号 */
  empCode = '';

  /** 姓名 */
  empName = '';

  /** 工号 */
  empNo = '';

  /** 合同结束日期 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 同步失败原因 */
  failReason = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** hzSupplyCerts */
  hzSupplyCerts = [];

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否发送短信 1是 0否 */
  isSendMsg = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 驳回原因 */
  rejectReason = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 签署状态 */
  signStatusName = '';

  /** 合同起始日期 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 批次id */
  supplyId = '';

  /** 同步状态 0 未同步 1 电子签同步成功 2 电子签同步失败 3 华住同步成功 4 华住同步失败 */
  synchroStatus = '';

  /** 同步状态名称 */
  synchroStatusName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传方 1客服 2个人 */
  uploadType = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HzSupplyEmpAddDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同类别 */
  contractTypeName = '';

  /** 合同版本名称 */
  contractVersionName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户Id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 电子合同编号 */
  eleContractId = '';

  /** 电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
  eleContractStatus = '';

  /** 电子合同状态名称 */
  eleContractStatusName = '';

  /** 唯一号 */
  empCode = '';

  /** 姓名 */
  empName = '';

  /** 合同结束日期 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 签署状态 */
  signStatusName = '';

  /** 合同起始日期 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpRuleQuery {
  /** 调基任务号 */
  adjTaskId = '';

  /** 基础数据编码 */
  baseDataCode = '';

  /** 批次id */
  batchId = '';

  /** 列名 */
  columns = '';

  /** 薪资批次id */
  currentRecordId = '';

  /** 客户id */
  custId = '';

  /** 结束时间 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误类型 */
  errorType = '';

  /** 导入结果 0：成功 1：失败 */
  impTag = '';

  /** 导入人姓名 */
  impUserName = '';

  /** 是否有效 */
  isEffective = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 导入批次中做详细筛选的id值 */
  recordId = '';

  /** 规则id */
  ruleId = '';

  /** 规则名称 */
  ruleName = '';

  /** 前端传入的参数，判断不同的情况查询语句 */
  selByAuth = '';

  /** 开始时间 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 表名 */
  tableName = '';

  /** 类型id */
  typeId = '';

  /** 薪资类别ID */
  wageClassId = '';
}

class ImpSIBackpayBase {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.BACKPAY_INFO_BASE_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoBaseId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.BACKPAY_INFO_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.CUST_CODE           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.CUST_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.EMP_CODE           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.EMP_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  empId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.EMP_NAME           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.ERR_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  errId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.ERR_INFO           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  errInfo = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.IDC_NUM           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  idcNum = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.IDC_TYPE           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  idcType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.IMP_TAG           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impTag = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.SS_INFO_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  ssInfoId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpSIBackpayItem {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_ITEM.BACKPAY_INFO_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_ITEM.BACKPAY_INFO_ITEM_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoItemId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** ifAdditional */
  ifAdditional = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_ITEM.PRODUCT_ID           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  productId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_ITEM.RPODUCT_NAME           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_ITEM.SEQ_NUM           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  seqNum = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_ITEM.WELFARE_PROCESSOR           ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  welfareProcessor = '';
}

class LaborBatchFileQuery {
  /** 批次id */
  batchId = '';

  /** 创建人 */
  createByName = '';

  /** 客户id */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 文件Minio地址 */
  fileMinioUrl = '';

  /** 文件名 */
  fileName = '';

  /** 主键id */
  hsLaborBatchFileId = '';

  /** 逻辑删除标识，0:正常 1：已删除，,默认值为0 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 状态:1生成中 2已完成3已失效 */
  status = '';

  /** 状态:1生成中 2已完成3已失效 */
  statusName = '';
}

class LaborContract {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 增员状态 */
  addConfirmStatus = '';

  /** 增员状态name */
  addConfirmStatusName = '';

  /** 增员状态 decode值 NP-8383 */
  addConfirmStatusText = '';

  /** addOrModify */
  addOrModify = undefined;

  /** 接单客服id */
  assigneeCsId = '';

  /** assigneeCsInstanceId */
  assigneeCsInstanceId = '';

  /** 接单客服name */
  assigneeCsName = '';

  /** 接单方客服对应联系方式 */
  assigneeCsTel = '';

  /** 接单方name */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服id */
  assignerCsId = '';

  /** assignerCsInstanceId */
  assignerCsInstanceId = '';

  /** 派单客服name */
  assignerCsName = '';

  /** 派单方客服对应联系方式 */
  assignerCsTel = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 取消聘用原因 */
  cancelHireReason = '';

  /** cityId */
  cityId = '';

  /** 城市 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 契约锁完成时间 */
  completeTime = '';

  /** 手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 劳动合同编号 */
  contractId = '';

  /** 劳动合同期限 */
  contractPeriod = '';

  /** 合同原则 */
  contractPrinciple = '';

  /** 合同性质：1新签、2续签、3变更、4补充 */
  contractProperty = undefined;

  /** 合同性质名称 */
  contractPropertyName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 合同类型 */
  contractType = '';

  /** 员工类别 */
  contractTypeEx = '';

  /** 合同类型显示名称 */
  contractTypeName = '';

  /** 员工类别(显示) */
  contractTypeNameEx = '';

  /** 合同版本 */
  contractVersion = '';

  /** 合同版本显示名称 */
  contractVersionName = '';

  /** 法人公司个数 */
  corporationCount = undefined;

  /** 法人单位ID */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 当前备注修改时间 NP-5286 */
  currTimeStamp = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 日期字段 */
  dateType = '';

  /** 期限 */
  deadLine = '';

  /** del */
  del = false;

  /** 电子合同id */
  eleContractId = '';

  /** 电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
  eleContractStatus = undefined;

  /** 电子合同状态（显示） */
  eleContractStatusName = '';

  /** eos中add表id */
  empAddId = '';

  /** 员工唯一号 */
  empCode = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入离职状态 */
  empHireStatus = '';

  /** 入离职状态 decode值 NP-8383 */
  empHireStatusText = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** 工号 */
  employeeNo = '';

  /** 员工签署日期 */
  employeeSignTime = '';

  /** 员工签署日期小于 */
  employeeSignTimeFrom = '';

  /** 员工签署日期大于 */
  employeeSignTimeTo = '';

  /** 结束时间 */
  endDt = '';

  /** 结束时间2 */
  endDt2 = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS劳动合同签署提醒流程实例ID */
  eosAssigneeCsInstanceId = '';

  /** 大客户续签劳动合同表id */
  eosReLaborContractId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 失败原因 */
  failureReason = '';

  /** 上传附件文件id */
  fileId = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 证件类型name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否删除 */
  isDeleted = '';

  /** 是否需要外呼 */
  isNeedCall = '';

  /** 是否需要外呼Name */
  isNeedCallName = '';

  /** 是否试用期 */
  isTrial = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 劳动合同id集合 */
  laborContractIds = '';

  /** 劳动合同list */
  laborContractList = [];

  /** 劳动合同动态字段 */
  laborParams = [];

  /** 模拟人 */
  mimicBy = '';

  /** 大于该日期的输入日期内容 */
  moreThanDt = '';

  /** 新增备注 NP-5286 */
  newRemark = '';

  /** noChange */
  noChange = false;

  /** 无固定期限劳动合同起始时间 */
  noFixedStartDt = '';

  /** 无固定期限试用期结束时间 */
  noFixedTrialEndDt = '';

  /** 无固定期限试用期月数 */
  noFixedTrialPeriod = '';

  /** 无固定期限试用期起始时间 */
  noFixedTrialStartDt = '';

  /** 无固定期限试用期工资百分比 */
  noFixedTrialWagePer = '';

  /** notOrder */
  notOrder = '';

  /** 原劳动合同id */
  oldLaborContractId = '';

  /** 劳动合同来源：(0手动创建,1自动创建) */
  origin = '';

  /** 劳动合同来源名称：(0手动创建,1自动创建) */
  originName = '';

  /** 其他劳动合同相关说明事项 */
  otherLabor = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程定义Id */
  processDefId = '';

  /** 供应商id(主签) */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 转正工资 */
  regularWage = '';

  /** 备注 */
  remark = '';

  /** 提醒标志 */
  remindMark = '';

  /** 大客户续签eos续签状态值(1:已提交、2:待提交、3:已取消) */
  renewStatus = '';

  /** 大客户续签eos续签状态文本 */
  renewStatusText = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 派遣期限结束 */
  sendPeriodEndDt = '';

  /** 派遣期限起始 */
  sendPeriodStartDt = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** 签订操作人 */
  signBy = '';

  /** 签订操作人显示名称 */
  signByName = '';

  /** 合同签订日期 */
  signDate = '';

  /** 合同生效日期 */
  signDt = '';

  /** 签署日期2 */
  signDt2 = '';

  /** 签单地 */
  signLoc = '';

  /** 签单地 */
  signLocName = '';

  /** 合同签订地 */
  signPlace = undefined;

  /** 印章流程个数 */
  signProcessCount = undefined;

  /** 印章流程ID */
  signProcessId = '';

  /** 合同版本名称 */
  signProcessName = '';

  /** 签署状态 */
  signStatus = '';

  /** 签署状态显示名称 */
  signStatusName = '';

  /** 合同签订形式 */
  signType = undefined;

  /** 合同签订形式(显示) */
  signTypeName = '';

  /** 开始时间 */
  startDt = '';

  /** 开始时间2 */
  startDt2 = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 提交时间 */
  submitDt = '';

  /** 大客户续签eos提交结束时间 */
  submitEndDt = '';

  /** 大客户续签eos提交开始时间 */
  submitStartDt = '';

  /** 同步状态 */
  syncStatus = '';

  /** 同步状态(显示) */
  syncStatusName = '';

  /** 试用结束时间 */
  trialEndDt = '';

  /** 试用期限 */
  trialPeriod = '';

  /** 试用开始时间 */
  trialStartDt = '';

  /** 试用工资 */
  trialWage = '';

  /** 试用期工资百分比 */
  trialWagePer = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 当前操作人Id NP-5286 */
  userId = '';

  /** userIdType */
  userIdType = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本地 */
  versionLoc = '';

  /** 版本地 */
  versionLocName = '';

  /** 工作城市 */
  workCity = '';

  /** 工作岗位 */
  workPost = '';

  /** 工作制 */
  workSystem = '';

  /** 工作制中文1, '标准工时',2,'不定时工时', 3, '综合工时', 4, '特殊工时（不定时工时/综合工时） */
  workSystemStr = '';

  /** 工作单位 */
  workUnit = '';

  /** workitemId */
  workitemId = '';
}

class LaborContractDTO {
  /** 流程节点表的活动英文名称 */
  activityNameEn = '';

  /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
  activityStatus = '';

  /** 增员状态 */
  addConfirmStatus = '';

  /** 增员状态name */
  addConfirmStatusName = '';

  /** 增员状态 decode值 */
  addConfirmStatusText = '';

  /** 接单客服id */
  assigneeCsId = '';

  /** 接单客服流程实例ID */
  assigneeCsInstanceId = '';

  /** 接单客服name */
  assigneeCsName = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服id */
  assignerCsId = '';

  /** 责任客服流程实例ID */
  assignerCsInstanceId = '';

  /** 派单客服name */
  assignerCsName = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 取消聘用原因 */
  cancelHireReason = '';

  /** 城市 */
  cityName = '';

  /** 手机 */
  contactTel2 = '';

  /** 劳动合同编号 */
  contractId = '';

  /** 劳动合同期限 */
  contractPeriod = '';

  /** 合同原则 */
  contractPrinciple = '';

  /** 合同性质：1新签、2续签、3变更、4补充 */
  contractProperty = undefined;

  /** 合同类型 */
  contractType = '';

  /** 员工类别 1代理 2派遣 3BPO 4外包项目 */
  contractTypeEx = '';

  /** 合同类型显示名称 */
  contractTypeName = '';

  /** 员工类别(显示) */
  contractTypeNameEx = '';

  /** 合同版本 */
  contractVersion = '';

  /** 合同版本显示名称 */
  contractVersionName = '';

  /** 法人单位ID */
  corporationId = '';

  /** 法人公司名称 */
  corporationName = '';

  /** 创建日期 */
  createDt = '';

  /** 当前备注修改时间 */
  currTimeStamp = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 日期字段 */
  dateType = '';

  /** 期限 */
  deadLine = '';

  /** 电子合同id */
  eleContractId = '';

  /** 电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
  eleContractStatus = undefined;

  /** 电子合同状态（显示） */
  eleContractStatusName = '';

  /** eos中add表id */
  empAddId = '';

  /** 员工唯一号 */
  empCode = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入离职状态 */
  empHireStatus = '';

  /** 入离职状态 decode值 */
  empHireStatusText = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** 工号 */
  employeeNo = '';

  /** 员工签署日期 */
  employeeSignTime = '';

  /** 员工签署日期小于 */
  employeeSignTimeFrom = '';

  /** 员工签署日期大于 */
  employeeSignTimeTo = '';

  /** 结束时间 */
  endDt = '';

  /** 结束时间2 */
  endDt2 = '';

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS劳动合同签署提醒流程实例ID */
  eosAssigneeCsInstanceId = '';

  /** 大客户续签劳动合同表id */
  eosReLaborContractId = '';

  /** 失败原因 */
  failureReason = '';

  /** 上传附件文件id */
  fileId = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 证件类型name */
  idCardTypeName = '';

  /** 是否删除 */
  isDeleted = '';

  /** 是否需要外呼 */
  isNeedCall = '';

  /** 是否需要外呼Name */
  isNeedCallName = '';

  /** 是否试用期 */
  isTrial = '';

  /** 劳动合同续签任务编号 */
  labRenewId = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 劳动合同id集合 */
  laborContractIds = '';

  /** 劳动合同list */
  laborContractList = [];

  /** 大于该日期的输入日期内容 */
  moreThanDt = '';

  /** 新增备注 */
  newRemark = '';

  /** 选择员工弹出页面时传的条件 */
  notOrder = '';

  /** 原劳动合同id */
  oldLaborContractId = '';

  /** 其他劳动合同相关说明事项 */
  otherLabor = '';

  /** 审批人 */
  participant = '';

  /** 流程定义Id */
  processDefId = '';

  /** 供应商id(主签) */
  providerId = '';

  /** 转正工资 */
  regularWage = '';

  /** 备注 */
  remark = '';

  /** 提醒标志 */
  remindMark = '';

  /** 导入规则id */
  ruleId = '';

  /** 派遣期限结束 */
  sendPeriodEndDt = '';

  /** 派遣期限起始 */
  sendPeriodStartDt = '';

  /** 签订操作人 */
  signBy = '';

  /** 签订操作人显示名称 */
  signByName = '';

  /** 合同签订日期 */
  signDate = '';

  /** 合同生效日期 */
  signDt = '';

  /** 签署日期2 */
  signDt2 = '';

  /** 签单地 */
  signLoc = '';

  /** 签单地 */
  signLocName = '';

  /** 合同签订地  1:派单地 2:接单地 3:派单地+接单地 4:拆分方  */
  signPlace = undefined;

  /** 印章流程ID */
  signProcessId = '';

  /** 印章流程名称 */
  signProcessName = '';

  /** 签署状态 */
  signStatus = '';

  /** 签署状态显示名称 */
  signStatusName = '';

  /** 合同签订形式 :1电子版、2纸质版 */
  signType = '';

  /** 合同签订形式(显示) */
  signTypeName = '';

  /** 开始时间 */
  startDt = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 同步状态 */
  syncStatus = '';

  /** 同步状态(显示) */
  syncStatusName = '';

  /** 试用结束时间 */
  trialEndDt = '';

  /** 试用期限 */
  trialPeriod = '';

  /** 试用开始时间 */
  trialStartDt = '';

  /** 试用工资 */
  trialWage = '';

  /** 当前操作人Id */
  userId = '';

  /** 传入后端的条件，用于区分修改和续签时终止流程的操作 */
  userIdType = '';

  /** 版本地 */
  versionLoc = '';

  /** 版本地 */
  versionLocName = '';

  /** 工作城市 */
  workCity = '';

  /** 工作岗位 */
  workPost = '';

  /** 工作制 */
  workSystem = '';

  /** 工作制中文1, '标准工时',2,'不定时工时', 3, '综合工时', 4, '特殊工时（不定时工时/综合工时） */
  workSystemStr = '';

  /** 工作单位 */
  workUnit = '';

  /** 工作流id */
  workitemId = '';
}

class LaborContractHistoryDTO {
  /** 创建时间 */
  createDt = '';

  /** 结束时间 */
  endDt = '';

  /** 是否删除 */
  isDeleted = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 劳动合同记录id */
  laborContractRecordId = '';

  /** 新状态 */
  newStatus = '';

  /** 新状态显示名称 */
  newStatusName = '';

  /** 原状态 */
  oldStatus = '';

  /** 原状态显示名称 */
  oldStatusName = '';

  /** 转正工资 */
  regularWage = '';

  /** 派遣期限结束 */
  sendPeriodEnd = '';

  /** 派遣期限起始 */
  sendPeriodStartDt = '';

  /** 签署日期 */
  signDt = '';

  /** 开始时间 */
  startDt = '';
}

class LaborContractQuery {
  /** 增员状态 */
  addConfirmStatus = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 合同性质：1新签、2续签、3变更、4补充 */
  contractProperty = undefined;

  /** 员工类别 1代理 2派遣 3BPO 4外包项目 */
  contractTypeEx = '';

  /** 客户编号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 日期字段类型 */
  dateType = '';

  /** 电子合同id */
  eleContractId = '';

  /** 电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
  eleContractStatus = undefined;

  /** 唯一号 */
  empCode = '';

  /** 入离职状态 */
  empHireStatus = '';

  /** 员工姓名 */
  empName = '';

  /** 工号 */
  employeeNo = '';

  /** 员工签署日期 */
  employeeSignTime = '';

  /** 员工签署日期小于 */
  employeeSignTimeFrom = '';

  /** 员工签署日期大于 */
  employeeSignTimeTo = '';

  /** 终止日期> */
  endDt = '';

  /** 终止日期< */
  endDt2 = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** 失败原因 */
  failureReason = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 证件号码 */
  idCardNum = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 大于该日期 */
  moreThanDt = '';

  /** 劳动合同查询雇员信息的条件 */
  notOrder = '';

  /** 劳动合同来源：(0手动创建,1自动创建) */
  origin = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 审批人 */
  participant = '';

  /** 流程定义Id */
  processDefId = '';

  /** 大客户续签eos续签状态值(1:已提交、2:待提交、3:已取消) */
  renewStatus = '';

  /** 卡权限(1：客户权限，3：订单权限也就是小合同权限) */
  restrictType = '';

  /** 签署日期> */
  signDt = '';

  /** 签署日期< */
  signDt2 = '';

  /** 签订地 */
  signLoc = '';

  /** 合同签订地  1:派单地 2:接单地 3:派单地+接单地 4:拆分方 */
  signPlace = undefined;

  /** 印章流程ID */
  signProcessId = '';

  /** 签署状态 */
  signStatus = '';

  /** 合同签订形式 :1电子版、2纸质版 */
  signType = '';

  /** 开始时间 */
  startDt = '';

  /** 开始时间2 */
  startDt2 = '';

  /** startIndex */
  startIndex = undefined;

  /** 大客户续签eos提交结束时间 */
  submitEndDt = '';

  /** 大客户续签eos提交开始时间 */
  submitStartDt = '';

  /** 同步状态 */
  syncStatus = '';

  /** 同步状态(显示) */
  syncStatusName = '';

  /** 版本地 */
  versionLoc = '';
}

class LateFeeBudget {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.APPLY_DT_FROM           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  applyDtFrom = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.APPLY_DT_TO           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  applyDtTo = '';

  /** base */
  base = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** budgetList */
  budgetList = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.BUDGET_NAME           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  budgetName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.CUST_ID           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** eAmt */
  eAmt = '';

  /** eLateFee */
  eLateFee = '';

  /** endIndex */
  endIndex = undefined;

  /** endMon */
  endMon = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileName */
  fileName = '';

  /** filePath */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.MAKEUP_DT           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  makeupDt = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** operationType */
  operationType = '';

  /** pAmt */
  pAmt = '';

  /** pLateFee */
  pLateFee = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.PROCESS_DT           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  processDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.PROCESS_DT_FROM           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  processDtFrom = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.PROCESS_DT_TO           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  processDtTo = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.PROCESSOR_ID           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  processorId = '';

  /** productId */
  productId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.SS_GROUP_ID           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  ssGroupId = '';

  /** ssInfoId */
  ssInfoId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.SS_LATE_FEE_BUDGET_ID           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  ssLateFeeBudgetId = '';

  /** startIndex */
  startIndex = undefined;

  /** startMon */
  startMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.STATUS           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** userId */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LATE_FEE_BUDGET.WELFARE_PROCESSOR           ibatorgenerated Wed Oct 31 14:21:57 CST 2012 */
  welfareProcessor = '';
}

class LockMon {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** costType */
  costType = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.CUST_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custList */
  custList = [];

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.EMPLOYER_MAINTAIN_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  employerMaintainId = undefined;

  /** employerName */
  employerName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** insuranceName */
  insuranceName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isIndependent */
  isIndependent = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.IS_VALID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  isValid = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.MON           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  mon = undefined;

  /** monLess */
  monLess = '';

  /** monThan */
  monThan = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.SS_LOCK_MON_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssLockMonId = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** unlockBy */
  unlockBy = '';

  /** unlockDt */
  unlockDt = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class LockMonQuery {
  /** costType */
  costType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.CUST_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.EMPLOYER_MAINTAIN_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  employerMaintainId = undefined;

  /** employerName */
  employerName = '';

  /** endIndex */
  endIndex = undefined;

  /** insuranceName */
  insuranceName = '';

  /** isIndependent */
  isIndependent = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.IS_VALID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  isValid = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.MON           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  mon = undefined;

  /** monLess */
  monLess = '';

  /** monThan */
  monThan = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.SS_LOCK_MON_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssLockMonId = undefined;

  /** startIndex */
  startIndex = undefined;

  /** unlockBy */
  unlockBy = '';

  /** unlockDt */
  unlockDt = '';
}

class MakeupPay {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.CREATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.CREATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.IS_DELETED           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MAKEUP_PAY_END_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MAKEUP_PAY_PROCESS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MAKEUP_PAY_START_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayStartMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MIMIC_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.PRODUCT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.PROXY_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.SS_MAKEUP_PAY_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMakeupPayId = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.UPDATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.UPDATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class MakeupPayMon {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  amt = undefined;

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMon */
  annualPaymentMon = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAdditionalAmt = undefined;

  /** eAdditionalAmtOld */
  eAdditionalAmtOld = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAmt = undefined;

  /** eAmtOld */
  eAmtOld = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eBase = undefined;

  /** 企业计算方法 */
  eCalculationMethod = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_LATE_FEE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eLateFee = undefined;

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业精确值 */
  ePrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** eRecordSum */
  eRecordSum = '';

  /** empCode */
  empCode = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** isSsExclued */
  isSsExclued = '';

  /** isSsExcluedFlag */
  isSsExcluedFlag = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.MAKEUP_PAY_PRO_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayProMon = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pBase = undefined;

  /** 个人计算方法 */
  pCalculationMethod = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_LATE_FEE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pLateFee = undefined;

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人精确值 */
  pPrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** pRecordSum */
  pRecordSum = '';

  /** payFrequency */
  payFrequency = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.PRODUCT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.PRODUCT_RATIO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** rMon */
  rMon = '';

  /** recordSum */
  recordSum = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** returnsEamt */
  returnsEamt = '';

  /** returnsPamt */
  returnsPamt = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** ssInfoIds */
  ssInfoIds = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_MAKEUP_PAY_MON_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMakeupPayMonId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMon = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class Map {}

class Material {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_NAME           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class OneChargesDTO {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** amtNoTax */
  amtNoTax = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单年月 */
  billYm = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** dataFrom */
  dataFrom = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 财务应收年月 */
  finReceiableYm = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 人数 */
  headCount = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否包括存档费 0否 1是 */
  isIncludedArchive = '';

  /** 是否包括商保0否1是 */
  isIncludedBusiness = '';

  /** 是否包含客户一次性费用  0 否 1是 */
  isIncludedOnechares = '';

  /** 是否包含服务费(0 否，1 是) */
  isIncludedService = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** oldVat */
  oldVat = undefined;

  /** 金额 */
  oldamount = undefined;

  /** onecharesId */
  onecharesId = '';

  /** 0 客户一次性项目 1 外包一次性税费 */
  onechargesType = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLine = '';

  /** 产品名称 */
  productName = '';

  /** 产品大类 */
  productTypeId = '';

  /** productTypeName */
  productTypeName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = '';

  /** quotationItemId */
  quotationItemId = '';

  /** receivableId */
  receivableId = '';

  /** receivableTempltId */
  receivableTempltId = '';

  /** 帐套名称 */
  receivableTempltName = '';

  /** 注释 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceType */
  serviceType = '';

  /** serviceTypeText */
  serviceTypeText = '';

  /** signBranchTitle */
  signBranchTitle = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 总税率 */
  totalTax = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;
}

class OrderSsGroupSvc {
  /** add */
  add = false;

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** custPayerId */
  custPayerId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** detailId */
  detailId = '';

  /** empHireSepId */
  empHireSepId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** orderSsGroupSvcId */
  orderSsGroupSvcId = undefined;

  /** pAcct */
  pAcct = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processRemark */
  processRemark = '';

  /** processType */
  processType = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** reduceDetailId */
  reduceDetailId = '';

  /** reduceSendChannel */
  reduceSendChannel = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sendChannel */
  sendChannel = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssGroupType */
  ssGroupType = '';

  /** status */
  status = undefined;

  /** statusEx */
  statusEx = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** welfareEndMon */
  welfareEndMon = '';

  /** welfareProcessor */
  welfareProcessor = '';
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class PayAllowwanceDetail {
  /** 开户行 */
  accountBank = '';

  /** 开户名称 */
  accountName = '';

  /** add */
  add = false;

  /** 金额 */
  amount = '';

  /** 银行帐号 */
  bankAcck = '';

  /** 银行名称 */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cashId */
  cashId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** custId */
  custId = '';

  /** 客户姓名 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 员工编号 */
  empCode = '';

  /** empId */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 身份证 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** payAllowanceDetailid */
  payAllowanceDetailid = undefined;

  /** payAuditId */
  payAuditId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processInsId */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PayAudit {
  /** acctBankName */
  acctBankName = '';

  /** acctId */
  acctId = '';

  /** acctIdBankName */
  acctIdBankName = '';

  /** acctIdName */
  acctIdName = '';

  /** acctIdNum */
  acctIdNum = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** allowanceDetailIds */
  allowanceDetailIds = [];

  /** allowanceFileId */
  allowanceFileId = '';

  /** allowanceFileName */
  allowanceFileName = '';

  /** 本次使用金额 */
  amount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  amt = undefined;

  /** appendRemark */
  appendRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLICANT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applicant = undefined;

  /** applicantDepartment */
  applicantDepartment = undefined;

  /** applicantDepartmentName */
  applicantDepartmentName = '';

  /** applicantName */
  applicantName = '';

  /** 申请人数 */
  applyCount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applyDt = '';

  /** applyEndDt */
  applyEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLY_PAY_AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applyPayAmt = undefined;

  /** applyPayAmtNoOne */
  applyPayAmtNoOne = '';

  /** applyPayAmtOne */
  applyPayAmtOne = '';

  /** applyStartDt */
  applyStartDt = '';

  /** 申请单抬头 */
  applyTitle = '';

  /** 审批通过时间小于等于 */
  approveEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPROVE_OPINION	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  approveOpinion = '';

  /** 审批通过时间大于等于 */
  approveStartDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPROVE_STATUS	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** 派单地申请单抬头 */
  assignerApplyTitle = '';

  /** 派单地抬头对应分公司 */
  assignerApplyTitleBranchId = '';

  /** 派单地抬头名称 */
  assignerApplyTitleName = '';

  /** 派单地 */
  assignerDepartmentId = '';

  /** 派单地name */
  assignerDepartmentName = '';

  /** auditId */
  auditId = '';

  /** 出款账号 */
  backBankAcct = '';

  /** 出款附言 */
  backPostscript = '';

  /** 交易日期 */
  backTradeDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.BANK_ACCT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  bankAcct = '';

  /** bank_acct_id */
  bankAcctId = '';

  /** bank_name */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** batchPayResult */
  batchPayResult = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.BILL_TYPE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  billType = '';

  /** 财务大类 */
  bizCategory = '';

  /** bizmanType */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型1 写入 RECEIPTS_ID 类型2 写入 PAY_AUDIT_ID */
  bussId = '';

  /** cashAmount */
  cashAmount = '';

  /** cashDt */
  cashDt = '';

  /** cashId */
  cashId = '';

  /** cashUser */
  cashUser = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 确认发放完成时间 */
  confirmFinishDt = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同名称 */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建人的查询条件 */
  createByInput = '';

  /** createByName */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** createDtTo */
  createDtTo = '';

  /** custCode */
  custCode = '';

  /** 客户确认状态 */
  custConfirmStatus = '';

  /** 客户填写的到款金额 */
  custEstimatedAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.CUST_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  custId = undefined;

  /** custList */
  custList = [];

  /** custName */
  custName = '';

  /** 客户填写的预计到款时间 */
  custSendMonth = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 支付地抬头对应分公司 */
  depTitleBranchId = '';

  /** 支付地抬头id */
  depTitleId = '';

  /** 支付地抬头名称 */
  depTitleName = '';

  /** 明细表的初始化状态 */
  detailStatus = '';

  /** empCode */
  empCode = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** expType */
  expType = '';

  /** exportType */
  exportType = '';

  /** 离职补偿金综合 */
  f10008Sum = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** fileProviderName */
  fileProviderName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** governingArea */
  governingArea = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** idCardNum */
  idCardNum = '';

  /** inId */
  inId = '';

  /** invoiceAmount */
  invoiceAmount = '';

  /** invoiceDate */
  invoiceDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.INVOICE_QTY	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  invoiceQty = undefined;

  /** isActualPay */
  isActualPay = '';

  /** 系统数据调整，不实际支付 */
  isAdjustNoPay = '';

  /** 系统核查通过自动提交 1:是 0:否 */
  isAutoCommit = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDelayAmtFlag */
  isDelayAmtFlag = '';

  /** 删除标记 */
  isDeleted = '';

  /** 已获取标记 */
  isGet = '';

  /** 是否加入银企直连黑名单  0：否 1：是 */
  isJoinBlacklist = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** 传递到金蝶系统：是、否 */
  isPassKingdee = '';

  /** isPaySet */
  isPaySet = '';

  /** 供应商工资或其他 1:是 0:否 */
  isSupplier = '';

  /** 是否使用核查到款审批 */
  isUseCheckPayApprove = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.LAST_PAY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  lastPayDt = '';

  /** 最晚支付日期到 */
  lastPayDtTo = '';

  /** lastPayEndDt */
  lastPayEndDt = '';

  /** lastPayStartDt */
  lastPayStartDt = '';

  /** 项目客服姓名 */
  liabilityCsName = '';

  /** mapLockMonUpdateDt */
  mapLockMonUpdateDt = undefined;

  /** mapLockMonUpdateDtM */
  mapLockMonUpdateDtM = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** newApproveOpinion */
  newApproveOpinion = '';

  /** noChange */
  noChange = false;

  /** 未自动提交原因 */
  notAutoCommitReason = '';

  /** 被冲抵的发放批次id */
  offsetPayBatchId = '';

  /** 外包风险金表id */
  orRiskFundId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** payAddress */
  payAddress = '';

  /** payAddressName */
  payAddressName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAmt = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_AUDIT_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAuditId = undefined;

  /** 发放批次名称(不展示，用于后台比较重复名称数据) */
  payAuditName = '';

  /** 发放批次编号 */
  payBatchCode = '';

  /** 发放批次id */
  payBatchId = '';

  /** 发放批次名称 */
  payBatchName = '';

  /** 批次类型：正常发放、虚拟发放 */
  payBatchType = '';

  /** class id集合 */
  payClassIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_DETAIL_METHOD	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payDetailMethod = '';

  /** payDetailType */
  payDetailType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payDt = '';

  /** payEndDt */
  payEndDt = '';

  /** 同步到中间库的pay_id */
  payId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_METHOD	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payMethod = undefined;

  /** payMethodName */
  payMethodName = '';

  /** payObject */
  payObject = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_PURPOSE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payPurpose = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_REASON	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payReason = '';

  /** send id集合 */
  paySends = '';

  /** payStartDt */
  payStartDt = '';

  /** payStatus */
  payStatus = '';

  /** payStatusName */
  payStatusName = '';

  /** payTreatmentDetail */
  payTreatmentDetail = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_TYPE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payType = undefined;

  /** payTypeName */
  payTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAYEE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payee = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAYEE_BANK	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payeeBank = '';

  /** payeeBankName */
  payeeBankName = '';

  /** 到账日期 */
  payeeDt = '';

  /** 附言 */
  postscript = '';

  /** postscriptRmk */
  postscriptRmk = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PROCESS_INS_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  processInsId = undefined;

  /** providerId */
  providerId = '';

  /** providerIdAlias */
  providerIdAlias = '';

  /** providerName */
  providerName = '';

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupId */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** prvdGroupName */
  prvdGroupName = '';

  /** prvdPayType */
  prvdPayType = '';

  /** rcvIds */
  rcvIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.REMARK	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  remark = '';

  /** remindUserIds */
  remindUserIds = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 本次总金额 */
  riskAmount = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.RPT_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  rptDt = '';

  /** sBatchId */
  sBatchId = '';

  /** secondBank */
  secondBank = '';

  /** secondBankAcct */
  secondBankAcct = '';

  /** secondBankBranch */
  secondBankBranch = '';

  /** secondCity */
  secondCity = '';

  /** secondPayee */
  secondPayee = '';

  /** secondProvince */
  secondProvince = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 发放日期 */
  sendDt = '';

  /** 发放通道 */
  sendWay = '';

  /** 服务费合计金额 */
  serviceFeeAmount = '';

  /** 签约方分公司抬头name */
  signBranchTitleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.SS_GROUP_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** 社保公积金合计金额 */
  ssPfAmount = '';

  /** startIndex */
  startIndex = undefined;

  /** subcontractAlias */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** userId */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.WELFARE_PROCESSOR	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** 扣缴义务人ID集合 */
  withholdAgentIds = '';

  /** 扣缴义务人名称集合 */
  withholdAgentNames = '';

  /** 扣缴义务人类型 */
  withholdAgentType = '';

  /** workitemId */
  workitemId = '';
}

class PaySet {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.EP_MAKEUP           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  epMakeup = '';

  /** epMakeupReadOnly */
  epMakeupReadOnly = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.EP_REMIT           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  epRemit = '';

  /** epRemitReadOnly */
  epRemitReadOnly = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.PAY_AUDIT_ID           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  payAuditId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.PAY_SET_ID           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  paySetId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.PN_MAKEUP           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  pnMakeup = '';

  /** pnMakeupReadOnly */
  pnMakeupReadOnly = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.PN_REMIT           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  pnRemit = '';

  /** pnRemitReadOnly */
  pnRemitReadOnly = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.PRODUCT_ID           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  productId = '';

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** rptDtFrom */
  rptDtFrom = '';

  /** rptDtTo */
  rptDtTo = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.BATCH_ID           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  sBatchId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PAY_SET.SS_GROUP_ID           ibatorgenerated Fri Aug 09 10:31:19 CST 2013 */
  ssGroupId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PayTreatmentDetail {
  /** 账户名称 */
  accountEmployeeName = '';

  /** add */
  add = false;

  /** 银行卡号 */
  bankAcct = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 业务小类 */
  busSubtypeId = '';

  /** 业务大类 */
  busTypeId = '';

  /** businessId */
  businessId = '';

  /** cashId */
  cashId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custId */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户付款方名称 */
  custPayName = '';

  /** 客户付款方名称id */
  custPayerId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 唯一号 */
  empCode = '';

  /** 姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 办理凭证id */
  fileId = '';

  /** 办理凭证 */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 开户行名称 */
  openBankName = '';

  /** payAuditId */
  payAuditId = '';

  /** 主键 */
  payTreatmentDetailId = undefined;

  /** 支付对象类型 */
  payType = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processInsId */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 实报金额 */
  realAmount = '';

  /** 提报金额 */
  reportAmount = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 入离职状态 */
  sepStatus = '';

  /** 社保状态 */
  ssStatus = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同名称 */
  subcontractName = '';

  /** transactTypeId */
  transactTypeId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ProcessInfo {
  /** acct */
  acct = '';

  /** add */
  add = false;

  /** addConfirmStatus */
  addConfirmStatus = '';

  /** addConfirmStatusName */
  addConfirmStatusName = '';

  /** allRemark */
  allRemark = '';

  /** alterStatus */
  alterStatus = '';

  /** alterStatusName */
  alterStatusName = '';

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** areaType */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** 接单客服id */
  assigneeCs = '';

  /** assigneeCsName */
  assigneeCsName = '';

  /** 派单客服id */
  assignerCs = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.CUST_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** 缴费实体名称 */
  custPayEntityName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 申报工资 */
  decSalary = undefined;

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_HIRE_SEP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empHireSepId = undefined;

  /** empHireSepStatus */
  empHireSepStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** filterEndMon */
  filterEndMon = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hireDt */
  hireDt = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** inId */
  inId = '';

  /** insuranceName */
  insuranceName = '';

  /** 增员是否需要实做 */
  isAddProcess = '';

  /** 增员是否需要实做 */
  isAddProcessName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isIndependent */
  isIndependent = '';

  /** 减员是否需要实做 */
  isReduceProcess = '';

  /** 减员是否需要实做 */
  isReduceProcessName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.NEXT_POINT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  nextPoint = undefined;

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.P_ACCT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAcct = undefined;

  /** pAcctEx */
  pAcctEx = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 派遣单位名称 */
  payerName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PRE_POINT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  prePoint = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processDt = '';

  /** processEndDt */
  processEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_REMARK           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_TYPE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processType = undefined;

  /** processTypeName */
  processTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESSOR_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processorId = undefined;

  /** processorName */
  processorName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** queryMode */
  queryMode = '';

  /** reduceSendChannel */
  reduceSendChannel = '';

  /** reduceSendChannelName */
  reduceSendChannelName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** rptHireDt */
  rptHireDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.S_NO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  sNo = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sendChannel */
  sendChannel = '';

  /** sendChannelName */
  sendChannelName = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** 签单客服id */
  signCs = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STATUS           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopBy = undefined;

  /** stopByName */
  stopByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopDt = '';

  /** stopEndDt */
  stopEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_REMARK           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_TYPE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopType = undefined;

  /** stopTypeName */
  stopTypeName = '';

  /** strStatus */
  strStatus = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** subcontractId */
  subcontractId = '';

  /** subcontractName */
  subcontractName = '';

  /** 统一社会信用码 */
  taxpayerIdentifier = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** verificationFile */
  verificationFile = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_END_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESSOR           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_START_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class ProcessInfoOpr {
  /** 开户人姓名 */
  accountEmployeeName = '';

  /** 实际工作地 */
  actualWorkLoc = '';

  /** add */
  add = false;

  /** 增员确认人 */
  addConfirmBy = '';

  /** 增员确认时间 */
  addConfirmDate = '';

  /** 增员过程 */
  addConfirmPro = '';

  /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
  addConfirmStatus = '';

  /** 增员状态名称 */
  addConfirmStatusName = '';

  /** 增员接单确认时间 */
  addPerfectBy = '';

  /** 增员接单确认人 */
  addPerfectDate = '';

  /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
  addReason = '';

  /** 增员备注 */
  addRemark = '';

  /** 年龄 */
  age = '';

  /** 变更确认人 */
  alterConfirmBy = '';

  /** 变更确认时间 */
  alterConfirmDate = '';

  /** 变更确认过程 */
  alterConfirmPro = '';

  /** 变更接单确认人 */
  alterPerfectBy = '';

  /** 变更接单确时间 */
  alterPerfectDate = '';

  /** 变更备注 */
  alterRemark = '';

  /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
  alterStatus = '';

  /** 变更状态名称 */
  alterStatusName = '';

  /** 大区类型 */
  areaType = '';

  /** 大区类型名称 */
  areaTypeName = '';

  /** 接单城市id */
  assigneeCityId = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 派单类型1 执行单2 协调单3 收集单 */
  assignmentType = '';

  /** 关联状态 */
  associationStatus = '';

  /** 银行卡号 */
  bankAcct = '';

  /** 银行卡更新人 */
  bankCardUpdateBy = '';

  /** 银行卡更新时间 */
  bankCardUpdateDt = '';

  /** baseInfo主键 */
  baseInfoId = '';

  /** 批次号,用于生成社保服务信息 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单模板id */
  billTempltId = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型 */
  category = '';

  /** 离职证明电子版本 */
  certificateSpId = '';

  /** certificateStatusName */
  certificateStatusName = '';

  /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
  changeMethod = '';

  /** 收费截至日期 */
  chargeEndDate = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 商保订单状态 */
  commInsurStatus = '';

  /** 商保订单状态name */
  commInsurStatusName = '';

  /** 确认备注 */
  confirmRemark = '';

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 法人单位id */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户方内部编号 */
  custInternalNum = '';

  /** 客户姓名 */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** 缴费实体 */
  custPayEntityName = '';

  /** 客户类型 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** 类型: 1,正常 2,大客户  */
  dataType = undefined;

  /** 申报工资 */
  decSalary = '';

  /** decSalaryRemark */
  decSalaryRemark = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** 客户端增员ID */
  empAddId = '';

  /** 客户端变更ID */
  empAlterId = '';

  /** feeId数组 */
  empFeeIdArray = '';

  /** 历史表主键 */
  empHireSepHisId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PROCESS_INFO_OPR.EMP_HIRE_SEP_ID	  	  ibatorgenerated Thu Apr 02 09:15:40 CST 2015 */
  empHireSepId = '';

  /** 入职主记录ID */
  empHiresepMainId = '';

  /** 员工id */
  empId = '';

  /** 停缴id */
  empStopId = '';

  /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
  empStopProcessState = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empType = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empTypeId = undefined;

  /** 人员分类 */
  empTypeName = '';

  /** 唯一号 */
  employeeCode = '';

  /** 费用段列表 */
  employeeFeeList = [];

  /** 雇员姓名 */
  employeeName = '';

  /** 雇员状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** enhancedAgent */
  enhancedAgent = '';

  /** enhancedAgentName */
  enhancedAgentName = '';

  /** 外部供应商收费模板 */
  exFeeTemplt = '';

  /** 外部供应商账单id */
  exFeeTempltId = '';

  /** exQuotationFeeList */
  exQuotationFeeList = [];

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  feeMonth = '';

  /** 收费模板名称 */
  feeTemplt = '';

  /** 收费模板id */
  feeTempltId = '';

  /** 档案柜编号 */
  fileCabCode = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 文件夹编号 */
  folderCode = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否关联订单 */
  hasAssociations = undefined;

  /** 入职竞争对手 */
  hireCompetitor = '';

  /** 入职时间 */
  hireDt = '';

  /** 入职时间止 */
  hireEndDt = '';

  /** 入职报价单 */
  hireQuotationId = '';

  /** 入职备注 */
  hireRemark = '';

  /** 入职时间起 */
  hireStartDt = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 接单客服name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 内外部类型(1内部2外部3全部) */
  innerType = '';

  /** 是否需要实做 */
  isAddProcess = '';

  /** 增员是否需要实做 */
  isAddProcessName = '';

  /** 是否需要签订劳动合同 */
  isArchive = '';

  /** 是否归档名称 */
  isArchiveName = '';

  /** 银行卡是否上传 */
  isBankCardUpload = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否入职呼叫 */
  isHireCall = '';

  /** 是否入职呼叫名称 */
  isHireCallName = '';

  /** 身份证是否上传 */
  isIDCardUpload = '';

  /** 是否单立户1 是0 否 */
  isIndependent = '';

  /** 劳动合同是否上传 */
  isLaborContractUpload = '';

  /** 是否需要签订劳动合同 */
  isNeedSign = '';

  /** 是否需要实做 */
  isReduceProcess = '';

  /** 是否退费 0否  1是 */
  isRefund = '';

  /** isRelated */
  isRelated = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保中文 */
  isSameInsurName = '';

  /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
  isSepCall = '';

  /** 是否离职呼叫名 */
  isSepCallName = '';

  /** 是否有统筹医疗 */
  isThereACoordinateHealth = '';

  /** 是否有统筹医疗名称 */
  isThereACoordinateHealthText = '';

  /** 是否有社保卡 */
  isThereSsCard = '';

  /** 是否有社保卡名称 */
  isThereSsCardText = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 离职动态模板json */
  jsonStr = [];

  /** 劳动关系单位 */
  laborRelationUnit = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 材料列表 */
  materialList = [];

  /** materialSignStatus */
  materialSignStatus = undefined;

  /** materialSignStatusName */
  materialSignStatusName = '';

  /** 离职材料电子版本id */
  materialSpId = '';

  /** materialStatusName */
  materialStatusName = '';

  /** 模拟人 */
  mimicBy = '';

  /** 操作方式  单立户1、大户2 */
  modeOfOperation = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** 后指针 */
  nextPointer = '';

  /** noChange */
  noChange = false;

  /** 非社保列表 */
  nonSsGroupList = [];

  /** 银行名称 */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 挂起原因 */
  pendingReason = '';

  /** 挂起原因中文 */
  pendingReasonName = '';

  /** 人员分类id */
  personCategoryId = '';

  /** 职位id */
  positionId = '';

  /** 前指针 */
  prevPointer = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PROCESS_INFO_OPR.PROCESS_INS_ID	  	  ibatorgenerated Thu Apr 02 09:15:40 CST 2015 */
  processInsId = '';

  /** processRemark */
  processRemark = '';

  /** 供应商编码 */
  providerCode = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商客服id */
  providerCsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** 供应商集团id */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 供应商集团 */
  prvdGroupName = '';

  /** 离职材料签订形式 */
  quitSignType = undefined;

  /** quitSignTypeName */
  quitSignTypeName = '';

  /** 电子离职合同任务主键 */
  quitTaskId = undefined;

  /** 报价单编码 */
  quotationCode = '';

  /** 报价单名称 */
  quotationName = '';

  /** 减少详细原因 */
  reduceDetailReason = '';

  /** 减原详细原因名称 */
  reduceDetailReasonName = '';

  /** 客户端减员ID */
  reduceId = '';

  /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
  reduceReason = '';

  /** 减员原因名称 */
  reduceReasonName = '';

  /** 参考日期，页面传入 */
  referDate = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** 报入职人员id */
  rptHireBy = '';

  /** 报入职人 */
  rptHireByName = '';

  /** 报入职时间 */
  rptHireDt = '';

  /** 报入职日期止 */
  rptHireEndDt = '';

  /** 报入职日期起 */
  rptHireStartDt = '';

  /** 报离职人员id */
  rptSepBy = '';

  /** 报离职人 */
  rptSepByName = '';

  /** 报离职日期 */
  rptSepDt = '';

  /** 报离职日期止 */
  rptSepEndDt = '';

  /** 报离职日期起 */
  rptSepStartDt = '';

  /** 用章对象 */
  sealObject = '';

  /** 用章类型 */
  sealType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职确认人 */
  sepConfirmBy = '';

  /** 离职确认日期 */
  sepConfirmDate = '';

  /** 离职确认历史 */
  sepConfirmHis = '';

  /** 离职确认进程 */
  sepConfirmPro = '';

  /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
  sepConfirmStatus = '';

  /** 离职状态名称 */
  sepConfirmStatusName = '';

  /** 离职详细原因 */
  sepDetailReason = '';

  /** 离职详细原因名称 */
  sepDetailReasonName = '';

  /** 离职日期 */
  sepDt = '';

  /** 离职时间止 */
  sepEndDt = '';

  /** 离职接单确认人 */
  sepPerfectBy = '';

  /** 离职接单确认时间 */
  sepPerfectDate = '';

  /** 离职手续办理状态:0  未完成   1  完成 */
  sepProcessStatus = '';

  /** 离职报价单 */
  sepQuotationId = '';

  /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
  sepReason = '';

  /** 离职原因key */
  sepReasonKey = '';

  /** 离职原因名称 */
  sepReasonName = '';

  /** 离职备注 */
  sepRemark = '';

  /** 离职时间止 */
  sepStartDt = '';

  /** 离职导出类型:1离职接单确认,2离职派单确认 */
  sepType = '';

  /** sigle */
  sigle = false;

  /** 签约方分公司抬头 */
  signBranchTitle = '';

  /** 签约方分公司抬头id */
  signBranchTitleId = '';

  /** 签约方分公司抬头name */
  signBranchTitleName = '';

  /** 签单供应商 */
  signProvider = '';

  /** 签单方 */
  signProviderId = '';

  /** signStatus */
  signStatus = undefined;

  /** 短信发送日期 */
  smsSendDt = '';

  /** 短信发送状态: 0未发送, 1成功, 2失败 */
  smsSendStatus = '';

  /** 短信发送状态中文: 未发送, 成功, 失败 */
  smsSendStatusStr = '';

  /** 分拆方分公司:分拆方客服 */
  splitServiceProviderCs = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PROCESS_INFO_OPR.SS_GROUP_ID	  	  ibatorgenerated Thu Apr 02 09:15:40 CST 2015 */
  ssGroupId = '';

  /** 社保列表 */
  ssGroupList = [];

  /** ssGroupName */
  ssGroupName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PROCESS_INFO_OPR.SS_INFO_ID	  	  ibatorgenerated Thu Apr 02 09:15:40 CST 2015 */
  ssInfoId = '';

  /** 员工社保参与地 */
  ssParticipateLocation = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PROCESS_INFO_OPR.SS_PROCESS_INFO_OPR_ID	  	  ibatorgenerated Thu Apr 02 09:15:40 CST 2015 */
  ssProcessInfoOprId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PROCESS_INFO_OPR.SS_TYPE	  	  ibatorgenerated Thu Apr 02 09:15:40 CST 2015 */
  ssType = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 1入职未生效2在职3离职 */
  status = '';

  /** 状态名称 */
  statusName = '';

  /** 小类名称 */
  subTypeName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 大类名称 */
  superTypeName = '';

  /** syncType */
  syncType = undefined;

  /** 总收费日期 */
  totalFeeDt = '';

  /** eos转移id */
  transferId = undefined;

  /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
  type = undefined;

  /** 机动分类项目1 */
  type1 = '';

  /** 机动分类项目2 */
  type2 = '';

  /** 机动分类项目3 */
  type3 = '';

  /** 机动分类项目4 */
  type4 = '';

  /** 机动分类项目5 */
  type5 = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';

  /** workItemId */
  workItemId = '';
}

class ProductRatioDetail {
  /** add */
  add = false;

  /** adjustNotes */
  adjustNotes = '';

  /** adjustType */
  adjustType = '';

  /** adjustTypeName */
  adjustTypeName = '';

  /** 年度平均工资 */
  annualAvgSalary = '';

  /** 年度最低工资 */
  annualMinSalary = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** eMaxBase */
  eMaxBase = undefined;

  /** eMinBase */
  eMinBase = undefined;

  /** endMonth */
  endMonth = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** pMaxBase */
  pMaxBase = undefined;

  /** pMinBase */
  pMinBase = undefined;

  /** policyLink */
  policyLink = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productRatioAppId */
  productRatioAppId = '';

  /** productRatioDetailId */
  productRatioDetailId = '';

  /** productRatioId */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startMonth */
  startMonth = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ProductRatioQuery {
  /** activityNameEn */
  activityNameEn = '';

  /** adjustNotes */
  adjustNotes = '';

  /** adjustType */
  adjustType = '';

  /** adjustTypeName */
  adjustTypeName = '';

  /** 年缴每月企业费用 */
  annualMonthEFee = undefined;

  /** 年缴每月个人费用 */
  annualMonthPFee = undefined;

  /** 年缴月 */
  annualPaymentMonth = '';

  /** appMark */
  appMark = '';

  /** approveOpinion */
  approveOpinion = '';

  /** areaId */
  areaId = '';

  /** areaName */
  areaName = '';

  /** 年缴计算顺序 */
  calculationOrder = '';

  /** 城市Id */
  cityId = '';

  /** 城市 */
  cityName = '';

  /** eadditionalAmt */
  eadditionalAmt = undefined;

  /** ecalculationMethod */
  ecalculationMethod = '';

  /** ecalculationMethodName */
  ecalculationMethodName = '';

  /** emaxBase */
  emaxBase = '';

  /** emaxRatio */
  emaxRatio = undefined;

  /** eminBase */
  eminBase = '';

  /** eminRatio */
  eminRatio = undefined;

  /** endIndex */
  endIndex = undefined;

  /** 企业个人比例关系 */
  epRatioRelationship = '';

  /** epRatioRelationshipName */
  epRatioRelationshipName = '';

  /** epRatioRelative */
  epRatioRelative = '';

  /** eprecision */
  eprecision = '';

  /** eprecisionName */
  eprecisionName = '';

  /** eratio */
  eratio = '';

  /** eratioStep */
  eratioStep = undefined;

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** insuranceMonth */
  insuranceMonth = '';

  /** isDefault */
  isDefault = '';

  /** 最近截止月 */
  lastEndMonth = '';

  /** 最近起始月 */
  lastStartMonth = '';

  /** padditionalAmt */
  padditionalAmt = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 代办人 */
  participant = '';

  /** 缴费频率 */
  payFrequency = '';

  /** 收费频率 */
  payFrequencyName = '';

  /** pcalculationMethod */
  pcalculationMethod = '';

  /** pcalculationMethodName */
  pcalculationMethodName = '';

  /** pmaxBase */
  pmaxBase = '';

  /** pmaxRatio */
  pmaxRatio = undefined;

  /** pminBase */
  pminBase = '';

  /** pminRatio */
  pminRatio = undefined;

  /** policyLink */
  policyLink = '';

  /** pprecision */
  pprecision = '';

  /** pprecisionName */
  pprecisionName = '';

  /** pratio */
  pratio = '';

  /** pratioStep */
  pratioStep = undefined;

  /** 流程定义id */
  processDefId = '';

  /** 流程实例Id */
  processInsId = '';

  /** 产品Id */
  productId = '';

  /** 产品 */
  productName = '';

  /** productRatioAppId */
  productRatioAppId = '';

  /** 比例内部号 */
  productRatioId = '';

  /** 比例名称 */
  productRatioName = '';

  /** reMark */
  reMark = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** statusText */
  statusText = '';

  /** updateByName */
  updateByName = '';

  /** 代办Id */
  workitemId = '';
}

class ProviderAcct {
  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.ACCT	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acct = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.ACCT_BANK_NAME           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctBankName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.ACCT_NAME           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.ACCT_NUM           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctNum = '';

  /** add */
  add = false;

  /** appointpayLastDay */
  appointpayLastDay = '';

  /** appointpayLastMon */
  appointpayLastMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.BANK_TYPE           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  bankType = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.CITY_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.EMPLOYER_MAINTAIN_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  employerMaintainId = undefined;

  /** employerMaintainName */
  employerMaintainName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** forId */
  forId = undefined;

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** ifInSalaryPlatform */
  ifInSalaryPlatform = '';

  /** ifSSPay */
  ifSSPay = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** payableBranchId */
  payableBranchId = '';

  /** payableFirstMon */
  payableFirstMon = '';

  /** payableFrequency */
  payableFrequency = '';

  /** payeeName */
  payeeName = '';

  /** pk */
  pk = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.PROVIDER_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  providerAcctId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.PROVIDER_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  providerId = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.PROVINCE_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  provinceId = undefined;

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.REMARK           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.SS_GROUP_ID	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssPayableMon */
  ssPayableMon = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ProviderAcctDTO {
  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.ACCT	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acct = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.ACCT_BANK_NAME           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctBankName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.ACCT_NAME           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.ACCT_NUM           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acctNum = '';

  /** appointpayLastDay */
  appointpayLastDay = '';

  /** appointpayLastMon */
  appointpayLastMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.BANK_TYPE           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  bankType = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.CITY_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custAcctId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.EMPLOYER_MAINTAIN_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  employerMaintainId = undefined;

  /** employerMaintainName */
  employerMaintainName = '';

  /** endIndex */
  endIndex = undefined;

  /** forId */
  forId = undefined;

  /** ifInSalaryPlatform */
  ifInSalaryPlatform = '';

  /** ifSSPay */
  ifSSPay = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** payableBranchId */
  payableBranchId = '';

  /** payableFirstMon */
  payableFirstMon = '';

  /** payableFrequency */
  payableFrequency = '';

  /** payeeName */
  payeeName = '';

  /** pk */
  pk = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.PROVIDER_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  providerAcctId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.PROVIDER_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  providerId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.PROVINCE_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  provinceId = undefined;

  /** provinceName */
  provinceName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROVIDER_ACCT.REMARK           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  remark = '';

  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.SS_GROUP_ID	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssPayableMon */
  ssPayableMon = '';

  /** startIndex */
  startIndex = undefined;
}

class Remit {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  amt = undefined;

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMon */
  annualPaymentMon = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.CREATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.CREATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eBase = undefined;

  /** 企业计算方法 */
  eCalculationMethod = '';

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业精确值 */
  ePrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.IS_DELETED           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  isDeleted = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.MIMIC_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pBase = undefined;

  /** 个人计算方法 */
  pCalculationMethod = '';

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人精确值 */
  pPrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** payFrequency */
  payFrequency = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PRODUCT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PRODUCT_RATIO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PROXY_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_REMIT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssRemitId = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.UPDATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.UPDATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_END_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_PROCESS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_START_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class RetireRecordQuery {
  /** add */
  add = false;

  /** 接单大区 */
  assigneeProviderAreas = '';

  /** 接单分公司 */
  assigneeProviderBranch = '';

  /** 派单大区 */
  assignerProviderAreas = '';

  /** 派单分公司 */
  assignerProviderBranch = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 唯一号 */
  empCode = '';

  /** 雇员姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 离退休月数 */
  retireMonth = undefined;

  /** 离退休年数 */
  retireYear = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class RetirementQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** 雇员姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 离退休月数 */
  retireMonth = undefined;

  /** 退休确认状态 */
  retirementConfirmStatus = undefined;

  /** 退休确认状态 */
  retirementConfirmStatuses = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 来源 */
  source = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同ID */
  subcontractId = undefined;

  /** subcontractName */
  subcontractName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class RiskFundEmp {
  /** add */
  add = false;

  /** 支付金额 */
  amount = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型1 写入 RECEIPTS_ID 类型2 写入 PAY_AUDIT_ID */
  bussId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 员工入离职id */
  empHireSepId = '';

  /** 员工ID */
  empId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 开户银行 */
  openBank = '';

  /** 开户名 */
  openName = '';

  /** 主键 */
  orRiskFundEmpId = '';

  /** 外包风险金id */
  orRiskFundId = '';

  /** 支付银行 */
  payBank = '';

  /** 支付银行账号 */
  payBankAcct = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 收款对象 1员工、2社保局 */
  reObj = '';

  /** 使用原因 1仲裁结果赔付、2补偿金、3赔偿金、4一次性就业补助 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 类型 1 核销 2 支付 */
  riskType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class RpaBatchInfoDTO {
  /** 【批次状态】固定值1、导出办理中2、已推送社保3、社保已抓取4、已获取反馈5、完成6、数据核对中 */
  batchStatus = undefined;

  /** detailList */
  detailList = [];

  /** empType */
  empType = undefined;

  /** 批次处理反馈时间 */
  endDate = '';

  /** organizationType */
  organizationType = [];

  /** 秘钥 */
  secretKey = '';

  /** 报送失败笔数 */
  sendFailure = '';

  /** 报送成功笔数 */
  sendSuccess = '';

  /** singleOrBatch */
  singleOrBatch = undefined;

  /** 发放批次号 */
  ssBatchId = undefined;

  /** statusType */
  statusType = undefined;

  /** userId */
  userId = '';
}

class RpaDetailDTO {
  /** 实际缴费起始年月（机器反馈） */
  actualStartMon = '';

  /** autoReverseCheck */
  autoReverseCheck = '';

  /** 明细id */
  detailId = undefined;

  /** 2、成功3、失败4、数据核对中5、数据丢失 */
  detailStatus = undefined;

  /** 失败原因 */
  failureReason = '';

  /** flexible */
  flexible = '';

  /** 凭证下载路径-按户 */
  forCorpPath = '';

  /** 凭证下载路径-按人 */
  forPersonalPath = '';

  /** medicalReverseCheck */
  medicalReverseCheck = '';

  /** medicalStatus */
  medicalStatus = '';

  /** 减员频道 */
  reduceSendChannel = '';

  /** 反馈结果 */
  result = '';

  /** 反馈备注 */
  resultRemark = '';

  /** 频道 */
  sendChannel = '';

  /** 序号（机器反馈） */
  serialNumber = '';

  /** socialReverseCheck */
  socialReverseCheck = '';

  /** ssBatchId */
  ssBatchId = '';

  /** 社保反馈结果 */
  ssResult = '';

  /** ssRuleId */
  ssRuleId = '';

  /** taxResultJson */
  taxResultJson = '';

  /** terminationVerificationFile */
  terminationVerificationFile = '';

  /** verificationFile */
  verificationFile = '';

  /** welfareEndMon */
  welfareEndMon = '';

  /** welfareProcessMon */
  welfareProcessMon = '';

  /** welfareStartMon */
  welfareStartMon = '';
}

class RpaSsCustCertificate {
  /** approveDt */
  approveDt = '';

  /** approveReason */
  approveReason = '';

  /** approveUserName */
  approveUserName = '';

  /** batchId */
  batchId = '';

  /** certificateId */
  certificateId = undefined;

  /** certificateStatus */
  certificateStatus = undefined;

  /** certificateStatusName */
  certificateStatusName = '';

  /** certificateType */
  certificateType = undefined;

  /** cityCode */
  cityCode = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** companyCode */
  companyCode = '';

  /** companyId */
  companyId = '';

  /** companyName */
  companyName = '';

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custName */
  custName = '';

  /** downloadPath */
  downloadPath = '';

  /** downloadStatus */
  downloadStatus = undefined;

  /** downloadStatusName */
  downloadStatusName = '';

  /** endDt */
  endDt = '';

  /** isDeleted */
  isDeleted = undefined;

  /** isDownload */
  isDownload = undefined;

  /** isDownloadName */
  isDownloadName = '';

  /** isIndependent */
  isIndependent = undefined;

  /** orgCode */
  orgCode = '';

  /** processDate */
  processDate = '';

  /** rejectReason */
  rejectReason = '';

  /** ssGroupId */
  ssGroupId = '';

  /** startDt */
  startDt = '';

  /** transactor */
  transactor = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';

  /** welfareProcessor */
  welfareProcessor = '';

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** workItemId */
  workItemId = '';
}

class RpaStampInfo {
  /** batchId */
  batchId = '';

  /** batchNumber */
  batchNumber = '';

  /** companyCode */
  companyCode = '';

  /** companyId */
  companyId = '';

  /** companyName */
  companyName = '';

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** flexible */
  flexible = '';

  /** hasStamp */
  hasStamp = '';

  /** isDeleted */
  isDeleted = undefined;

  /** isIndependent */
  isIndependent = undefined;

  /** medicalFileDownload */
  medicalFileDownload = '';

  /** medicalFileUpload */
  medicalFileUpload = '';

  /** medicalStampStatus */
  medicalStampStatus = undefined;

  /** orgCode */
  orgCode = '';

  /** rpaStampInfoId */
  rpaStampInfoId = '';

  /** 企业编号 */
  socialSecurity = '';

  /** ssFileDownload */
  ssFileDownload = '';

  /** ssFileUpload */
  ssFileUpload = '';

  /** ssStampStatus */
  ssStampStatus = undefined;

  /** updateBy */
  updateBy = '';

  /** updateByName */
  updateByName = '';

  /** updateDt */
  updateDt = '';
}

class RpaTaxation {
  /** batchEndDt */
  batchEndDt = '';

  /** batchId */
  batchId = undefined;

  /** batchStartDt */
  batchStartDt = '';

  /** companyCode */
  companyCode = '';

  /** companyName */
  companyName = '';

  /** compareBatch */
  compareBatch = '';

  /** compareDt */
  compareDt = '';

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custName */
  custName = '';

  /** decSalary */
  decSalary = undefined;

  /** empHireSepId */
  empHireSepId = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** idcardNum */
  idcardNum = '';

  /** idcardType */
  idcardType = '';

  /** isDeleted */
  isDeleted = undefined;

  /** medicalResult */
  medicalResult = '';

  /** operateYear */
  operateYear = '';

  /** orderServiceId */
  orderServiceId = undefined;

  /** orgCode */
  orgCode = '';

  /** orgName */
  orgName = '';

  /** sendStatus */
  sendStatus = undefined;

  /** ssDetailId */
  ssDetailId = undefined;

  /** ssResult */
  ssResult = '';

  /** taxDetail */
  taxDetail = '';

  /** taxId */
  taxId = undefined;

  /** taxResult */
  taxResult = '';

  /** transactor */
  transactor = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';
}

class SSAcctDTO {
  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.ACCT	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  acct = '';

  /** appointpayLastDay */
  appointpayLastDay = '';

  /** appointpayLastMon */
  appointpayLastMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ACCT_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custAcctId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_CUST_ACCT.CUST_ID           ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** forId */
  forId = undefined;

  /** ifInSalaryPlatform */
  ifInSalaryPlatform = '';

  /** ifSSPay */
  ifSSPay = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** payableBranchId */
  payableBranchId = '';

  /** payableFirstMon */
  payableFirstMon = '';

  /** payableFrequency */
  payableFrequency = '';

  /** payeeName */
  payeeName = '';

  /** pk */
  pk = '';

  /** This field was generated by Apache iBATIS ibator.	  This field corresponds to the database column SS_CUST_ACCT.SS_GROUP_ID	 	  ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssPayableMon */
  ssPayableMon = '';

  /** startIndex */
  startIndex = undefined;
}

class SSRemitSyncToOrderQuery {
  /** 创建人名字 */
  createBy = '';

  /** 创建时间止 */
  createEndDt = '';

  /** 创建时间始 */
  createStartDt = '';

  /** 客户id */
  custId = '';

  /** 雇员编码 */
  employeeCode = '';

  /** 雇员姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 身份证号码 */
  idCardNum = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 社保组id */
  ssGroupId = '';

  /** startIndex */
  startIndex = undefined;
}

class SSRemitSyncToOrderVO {
  /** 费用段 */
  list = [];

  /** 办理信息 */
  processInfoOpr = new ProcessInfoOpr();
}

class SecondCustPayer {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 二级户名称 */
  payerName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 主键 */
  secondCustPayerId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 二级户注册城市 */
  signupCity = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 二级户统一信用代码 */
  taxpayerIdentifier = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SimpleBusinessDTO {
  /** 接口 */
  ebmBusinessIfs = new EbmBusinessIfs();

  /** 导入记录id串 */
  idArray = [];

  /** 导入记录id串 */
  ids = '';

  /** 任务id */
  impTaskId = '';

  /** 是否生效 */
  isEffective = '';

  /** 接口子项 */
  itemList = [];

  /** 办理结果 */
  transactResult = '';
}

class SimpleBusinessQuery {
  /** 业务小类 */
  busSubtypeId = '';

  /** businessStatus */
  businessStatus = '';

  /** 业务小类id */
  businessSubtypeId = '';

  /** 业务大类id */
  businessTypeId = '';

  /** 业务项目 */
  busnameClassId = '';

  /** 所属类型 */
  categoryId = '';

  /** 城市id */
  cityId = '';

  /** 创建人 */
  createBy = '';

  /** 客户名称 */
  custName = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 唯一号 */
  empCode = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** handleEndDtE */
  handleEndDtE = '';

  /** handleEndDtS */
  handleEndDtS = '';

  /** handleStartDtE */
  handleStartDtE = '';

  /** handleStartDtS */
  handleStartDtS = '';

  /** 证件号码 */
  idCardNum = '';

  /** 任务id */
  impTaskId = '';

  /** 导入任务名称 */
  impTaskName = '';

  /** 导入类型 */
  impType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 小合同编号 */
  subContractId = '';

  /** 小合同名称 */
  subContractName = '';

  /** 办理结果 */
  transactResult = '';

  /** 办理人 */
  transacterId = '';
}

class SocialBatchQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** boUrl */
  boUrl = '';

  /** branchId */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empHireSepId */
  empHireSepId = undefined;

  /** 1增员，2减员 */
  empType = undefined;

  /** empTypeStr */
  empTypeStr = '';

  /** 福利办理方 */
  employerMaintainId = '';

  /** 批次结束时间 */
  endDate = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sendUserId */
  sendUserId = '';

  /** singleOrBatch */
  singleOrBatch = undefined;

  /** 发放批次号 */
  ssBatchId = '';

  /** 社保组 */
  ssGroupId = '';

  /** ssGroupType */
  ssGroupType = '';

  /** 合并组ID */
  ssPackageId = '';

  /** 批次生成时间 */
  startDate = '';

  /** startIndex */
  startIndex = undefined;

  /** 【批次状态】固定值1、导出办理中2、已推送社保3、社保已抓取4、已获取反馈5、完成 */
  status = undefined;

  /** subType */
  subType = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialDetailQuery {
  /** 实际缴费起始月 */
  actualStartMon = '';

  /** add */
  add = false;

  /** 申请人 */
  applicant = undefined;

  /** 申请日期>= */
  applyDtFrom = '';

  /** 申请日期<= */
  applyDtTo = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 1, '大型客户', 2, '中型客户', 3, '小型客户' */
  customerSize = undefined;

  /** del */
  del = false;

  /** 发放明细号 */
  detailId = undefined;

  /** 批次明细状态 */
  detailStatus = undefined;

  /** 唯一号 */
  empCode = '';

  /** 雇员姓名 */
  empName = '';

  /** 1增员，2减员 */
  empType = undefined;

  /** 批次反馈时间>= */
  endDateFrom = '';

  /** 批次反馈时间<= */
  endDateTo = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 办理状态 */
  processStatus = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 发放通道 */
  sendChannel = undefined;

  /** sendUserId */
  sendUserId = '';

  /** 发放批次号 */
  ssBatchId = undefined;

  /** 社保公积金组 */
  ssGroupId = undefined;

  /** 合并组 */
  ssPackageId = undefined;

  /** 批次发送时间>= */
  startDateFrom = '';

  /** 批次发送时间<= */
  startDateTo = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 福利截止月 */
  welfareEndMon = '';

  /** 福利办理方 */
  welfareProcessor = '';
}

class SocialPayCustQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客戶id */
  custId = undefined;

  /** 客戶名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 支付审核ID */
  payAuditId = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保支付客戶Id */
  socialPayCustId = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityGroupNew {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** code */
  code = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isOneTimeMakeupPay */
  isOneTimeMakeupPay = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** name */
  name = '';

  /** newTypeId */
  newTypeId = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityGroupProduct {
  /** add */
  add = false;

  /** baseBindingLevel */
  baseBindingLevel = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** category */
  category = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** eRatio */
  eRatio = undefined;

  /** epMakeup */
  epMakeup = '';

  /** epRemit */
  epRemit = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** isAnnualAvgSalary */
  isAnnualAvgSalary = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isCrossYearSupp */
  isCrossYearSupp = '';

  /** 删除标记 */
  isDeleted = '';

  /** isInterest */
  isInterest = '';

  /** isOffPayment */
  isOffPayment = '';

  /** isRequired */
  isRequired = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** monthlyPayment */
  monthlyPayment = '';

  /** noChange */
  noChange = false;

  /** offPaymentExplain */
  offPaymentExplain = '';

  /** pRatio */
  pRatio = undefined;

  /** pnMakeup */
  pnMakeup = '';

  /** pnRemit */
  pnRemit = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productId */
  productId = '';

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupProductId */
  ssGroupProductId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** suppMonth */
  suppMonth = '';

  /** suppOtherExplain */
  suppOtherExplain = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityGroupQuery {
  /** 年度调整月 */
  annualAdjustMonth = '';

  /** 城市Id */
  cityId = '';

  /** 所属城市 */
  cityName = '';

  /** 托收方式 */
  collectionMethod = '';

  /** 默认显示 */
  defaultDisplay = '';

  /** eamtRemitMethod */
  eamtRemitMethod = '';

  /** endIndex */
  endIndex = undefined;

  /** 首次补缴方式 */
  firstRemitMethod = '';

  /** 组名称 */
  insuranceName = '';

  /** 办理是否需要申 */
  isPrcNeedApply = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** pamtRemitMethod */
  pamtRemitMethod = '';

  /** 办理日 */
  processDeadline = '';

  /** processDeadlineEnd */
  processDeadlineEnd = '';

  /** processDeadlineStart */
  processDeadlineStart = '';

  /** 说明 */
  remark = '';

  /** serviceMonthType */
  serviceMonthType = '';

  /** 社保组i */
  ssGroupId = '';

  /** 组类别 */
  ssGroupType = '';

  /** startIndex */
  startIndex = undefined;

  /** 办停日 */
  stopDeadline = '';

  /** stopDeadlineEnd */
  stopDeadlineEnd = '';

  /** stopDeadlineStart */
  stopDeadlineStart = '';

  /** 办停方式 */
  stopMethod = '';
}

class SocialSecurityGroupRatio {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDefault */
  isDefault = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productId */
  productId = '';

  /** productName */
  productName = '';

  /** productRatioId */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssRatioId */
  ssRatioId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityGroupStop {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** code */
  code = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** name */
  name = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** stopTypeId */
  stopTypeId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityReportConf {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市Id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报表详细信息 */
  reportDetail = '';

  /** 报表名称 */
  reportName = '';

  /** 备注 */
  reportRemark = '';

  /** 调用的报表模板 */
  reportTemplate = '';

  /** 报表类型key */
  reportType = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保组Id */
  ssGroupId = '';

  /** 社保组名称 */
  ssGroupName = '';

  /** 主键 */
  ssReportConfId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityWelfareDetail {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** eMaxRatio */
  eMaxRatio = undefined;

  /** eMinRatio */
  eMinRatio = undefined;

  /** eRatio */
  eRatio = undefined;

  /** eRatioStep */
  eRatioStep = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** insuranceName */
  insuranceName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** pMaxRatio */
  pMaxRatio = undefined;

  /** pMinRatio */
  pMinRatio = undefined;

  /** pRatio */
  pRatio = undefined;

  /** pRatioStep */
  pRatioStep = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productId */
  productId = '';

  /** productName */
  productName = '';

  /** productRatioId */
  productRatioId = '';

  /** productRatioName */
  productRatioName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssWelfarePkgDetailId */
  ssWelfarePkgDetailId = '';

  /** ssWelfarePkgId */
  ssWelfarePkgId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SocialSecurityWelfareQuery {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** disabilityPaymentsName */
  disabilityPaymentsName = '';

  /** endIndex */
  endIndex = undefined;

  /** housingFundName */
  housingFundName = '';

  /** illnessInsuranceName */
  illnessInsuranceName = '';

  /** industrialInjuryInsuranceName */
  industrialInjuryInsuranceName = '';

  /** isDefault */
  isDefault = '';

  /** isDefaultName */
  isDefaultName = '';

  /** maternityInsuranceName */
  maternityInsuranceName = '';

  /** medicalInsuranceName */
  medicalInsuranceName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** pensionName */
  pensionName = '';

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** ssWelfarePkgId */
  ssWelfarePkgId = '';

  /** ssWelfarePkgName */
  ssWelfarePkgName = '';

  /** startIndex */
  startIndex = undefined;

  /** unemploymentInsuranceName */
  unemploymentInsuranceName = '';

  /** validDate */
  validDate = '';

  /** welfareRealName */
  welfareRealName = '';
}

class SsEntryRelate {
  /** add */
  add = false;

  /** 接单方 */
  assigneeProviderId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** entryRelateId */
  entryRelateId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /**  是否强制关联入职（0否，1是） */
  isRelated = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SsEntryRelateResp {
  /** 接单方名称 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** entryRelateId */
  entryRelateId = '';

  /**  是否强制关联入职（0否，1是） */
  isRelated = '';

  /** 省份id */
  provinceId = '';

  /** 省份名称 */
  provinceName = '';
}

class SsImpbatchDTO {
  /** 批次ID */
  batchId = '';

  /** 导入结束日期 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型 */
  impType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** ruleId */
  ruleId = '';

  /** 社保公积金组 */
  ssGroupId = '';

  /** 导入开始日期 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;
}

class SsPaymentResultQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** 缴费实体名称 */
  custPayEntityName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 雇员 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 费用年月 */
  feeMonth = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 雇员证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保组 */
  ssGroupId = '';

  /** 社保组名称 */
  ssGroupName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 福利办理方 */
  welfareProcessor = '';

  /** 福利办理方名称 */
  welfareProcessorName = '';
}

class SsReportConfQuery {
  /** 城市Id */
  cityId = '';

  /** endIndex */
  endIndex = undefined;

  /** 对页面的删除的Idlist */
  idList = [];

  /** 对页面的变更记录进行增改的list */
  list = [];

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 社保组查询条件 */
  ssGroupId = '';

  /** startIndex */
  startIndex = undefined;
}

class SysSmsField {
  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** 例子 */
  example = '';

  /** fieldId */
  fieldId = '';

  /** 对应键 */
  fieldKey = '';

  /** 对应值 */
  fieldValue = '';

  /** isDeleted */
  isDeleted = undefined;

  /** 备注 */
  remark = '';

  /** 排序 */
  sort = '';

  /** 模板类型 */
  templtType = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';
}

class SysSmsLog {
  /** add */
  add = false;

  /** 适用场景 */
  appScene = '';

  /** 适用场景name */
  appSceneName = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服/供应商客服 */
  assigneeCsId = '';

  /** 接单方name */
  assigneeProvider = '';

  /** 接单方/供应商 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方name */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 手机号 */
  contactTel1 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 操作人 */
  createByEx = '';

  /** 操作人Name */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** 发送时间起 */
  createDtFrom = '';

  /** 发送时间止 */
  createDtTo = '';

  /** 客户编号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户姓名 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 雇员ID */
  empId = '';

  /** 唯一号 */
  employeeCode = '';

  /** 雇员姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 失败原因 */
  errInfo = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 备注 */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 短信发送状态 */
  status = '';

  /** 短信发送状态Name */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 通知编号 */
  sysSmsLogId = '';

  /** 模板名称 */
  templtName = '';

  /** 模块类型 */
  templtType = '';

  /** 模块类型name */
  templtTypeName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本类型 */
  versionType = '';

  /** 版本类型name */
  versionTypeName = '';
}

class SysSmsLogQuery {
  /** 适用场景 */
  appScene = '';

  /** 适用场景name */
  appSceneName = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服/供应商客服 */
  assigneeCsId = '';

  /** 接单方name */
  assigneeProvider = '';

  /** 接单方/供应商 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方name */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 手机号 */
  contactTel1 = '';

  /** 操作人 */
  createByEx = '';

  /** 操作人Name */
  createByName = '';

  /** 发送时间起 */
  createDtFrom = '';

  /** 发送时间止 */
  createDtTo = '';

  /** 客户编号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户姓名 */
  custName = '';

  /** 雇员ID */
  empId = '';

  /** 唯一号 */
  employeeCode = '';

  /** 雇员姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 失败原因 */
  errInfo = '';

  /** 证件号码 */
  idCardNum = '';

  /** 备注 */
  memo = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 短信发送状态 */
  status = '';

  /** 短信发送状态Name */
  statusName = '';

  /** 通知编号 */
  sysSmsLogId = '';

  /** 模板名称 */
  templtName = '';

  /** 模块类型 */
  templtType = '';

  /** 模块类型name */
  templtTypeName = '';

  /** 版本类型 */
  versionType = '';

  /** 版本类型name */
  versionTypeName = '';
}

class dropdownListDTO {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class employeeFeeDTO {
  /** 金额(不含税) */
  amtNoTax = '';

  /** 附加税费 */
  atr = '';

  /** baseBindingLevel */
  baseBindingLevel = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.BILL_START_MONTH           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  billStartMonth = '';

  /** category */
  category = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.CHARGE_END_DATE           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  chargeEndDate = '';

  /** chargeRate */
  chargeRate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.CHARGE_START_DATE           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  chargeStartDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.E_BILL_TEMPLT_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eBillTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.E_FEE_MONTH           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eFeeMonth = '';

  /** 收费模板名 */
  eFeeTemplt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.E_FEE_TEMPLT_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eFeeTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.E_FREQUENCY           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eFrequency = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.E_MONTH_IN_ADVANCE           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eMonthInAdvance = '';

  /** empFeeHisId */
  empFeeHisId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.EMP_FEE_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empFeeId = '';

  /** empFeeOprId */
  empFeeOprId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.EMP_HIRE_SEP_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empHireSepId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.EMP_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empId = '';

  /** 外部供应商账单起始月 */
  exBillStartMonth = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板名 */
  exFeeTemplt = '';

  /** 外部供应商收费模板id */
  exFeeTempltId = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** isShow */
  isShow = '';

  /** isUptFeeMon */
  isUptFeeMon = '';

  /** oldAmount */
  oldAmount = '';

  /** oldBillStartMonth */
  oldBillStartMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.P_BILL_TEMPLT_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pBillTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.P_FEE_MONTH           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pFeeMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.P_FEE_TEMPLT_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pFeeTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.P_FREQUENCY           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pFrequency = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.P_MONTH_IN_ADVANCE           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pMonthInAdvance = '';

  /** 支付最后服务年月 */
  payLastServiceMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.PRODUCT_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  productId = '';

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.PRODUCT_RATIO_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  productRatioId = '';

  /** productRatioName */
  productRatioName = '';

  /** 供应商id */
  providerId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.QUOTATION_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  quotationId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.QUOTATION_DETAIL_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  quotationItemId = '';

  /** receivableAmt */
  receivableAmt = undefined;

  /** receivableMonth */
  receivableMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.REMARK           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  remark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.SS_GROUP_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column HS_EMP_FEE.SS_WELFARE_PKG_ID           @ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  ssWelfarePkgId = '';

  /** ssWelfarePkgName */
  ssWelfarePkgName = '';

  /** tag */
  tag = '';

  /** 增值税 */
  vat = '';

  /** 增值税率 */
  vatr = '';

  /** verifyAmt */
  verifyAmt = undefined;

  /** 实收金额(不含税) */
  verifyAmtNoTax = '';

  /** 实收金额增值税 */
  verifyAmtVat = '';
}

class impSIBackpayBaseQuery {
  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.BACKPAY_INFO_BASE_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoBaseId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.BACKPAY_INFO_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.CUST_CODE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.CUST_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  custId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.EMP_CODE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.EMP_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  empId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.EMP_NAME           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.ERR_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  errId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.ERR_INFO           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  errInfo = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.IDC_NUM           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  idcNum = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.IDC_TYPE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  idcType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.IMP_TAG           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impTag = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_BASE.SS_INFO_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  ssInfoId = '';

  /** startIndex */
  startIndex = undefined;
}

class impSIBackpayInfoDTO {
  /** backpayBases */
  backpayBases = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.BACKPAY_INFO_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoId = '';

  /** createByName */
  createByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.CUST_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  custId = '';

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.DEPARTMENT_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_NAME           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_STATUS           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_TAG           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impTag = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_TYPE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impType = '';

  /** importMemo */
  importMemo = '';

  /** importType */
  importType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.MAKEUP_TYPE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  makeupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.PAYMENT_MON           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  paymentMon = '';

  /** proItems */
  proItems = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.SS_GROUP_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.TMP_FILE_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  tmpFileId = '';

  /** tmpFileName */
  tmpFileName = '';

  /** uploadBy */
  uploadBy = '';

  /** uploadDt */
  uploadDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.UPT_FILE_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  uptFileId = '';

  /** uptFileName */
  uptFileName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.WELFARE_PROCESSOR           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  welfareProcessor = '';

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class impSIBackpayInfoQuery {
  /** backpayBases */
  backpayBases = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.BACKPAY_INFO_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  backpayInfoId = '';

  /** createByName */
  createByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.CUST_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  custId = '';

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.DEPARTMENT_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_NAME           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_STATUS           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_TAG           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impTag = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.IMP_TYPE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  impType = '';

  /** importMemo */
  importMemo = '';

  /** importType */
  importType = '';

  /** 删除标记 */
  isDeleted = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.MAKEUP_TYPE           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  makeupType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.PAYMENT_MON           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  paymentMon = '';

  /** proItems */
  proItems = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.SS_GROUP_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.TMP_FILE_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  tmpFileId = '';

  /** tmpFileName */
  tmpFileName = '';

  /** uploadBy */
  uploadBy = '';

  /** uploadDt */
  uploadDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.UPT_FILE_ID           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  uptFileId = '';

  /** uptFileName */
  uptFileName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column IMP_SI_BACKPAY_INFO.WELFARE_PROCESSOR           @ibatorgenerated Thu Nov 01 10:08:49 CST 2012 */
  welfareProcessor = '';

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class lockMonDTO {
  /** costType */
  costType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.CUST_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.EMPLOYER_MAINTAIN_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  employerMaintainId = undefined;

  /** employerName */
  employerName = '';

  /** insuranceName */
  insuranceName = '';

  /** isIndependent */
  isIndependent = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.IS_VALID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  isValid = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.MON           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  mon = undefined;

  /** monLess */
  monLess = '';

  /** monThan */
  monThan = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_LOCK_MON.SS_LOCK_MON_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssLockMonId = undefined;

  /** unlockBy */
  unlockBy = '';

  /** unlockDt */
  unlockDt = '';
}

class makeupPayMonDTO {
  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  amt = undefined;

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMon */
  annualPaymentMon = undefined;

  /** assigneeCsId */
  assigneeCsId = '';

  /** branchId */
  branchId = '';

  /** custCode */
  custCode = '';

  /** custName */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_ADDITIONAL_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAdditionalAmt = undefined;

  /** eAdditionalAmtOld */
  eAdditionalAmtOld = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAmt = undefined;

  /** eAmtOld */
  eAmtOld = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_BASE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eBase = undefined;

  /** 企业计算方法 */
  eCalculationMethod = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_LATE_FEE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eLateFee = undefined;

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业精确值 */
  ePrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_RATIO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** eRecordSum */
  eRecordSum = '';

  /** empCode */
  empCode = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** idCardNum */
  idCardNum = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** isSsExclued */
  isSsExclued = '';

  /** isSsExcluedFlag */
  isSsExcluedFlag = '';

  /** makeUpPayProMon */
  makeUpPayProMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.MAKEUP_PAY_PRO_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayProMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_ADDITIONAL_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_BASE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pBase = undefined;

  /** 个人计算方法 */
  pCalculationMethod = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_LATE_FEE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pLateFee = undefined;

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人精确值 */
  pPrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_RATIO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** pRecordSum */
  pRecordSum = '';

  /** payFrequency */
  payFrequency = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.PRODUCT_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.PRODUCT_RATIO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productRatioId = '';

  /** rMon */
  rMon = '';

  /** recordSum */
  recordSum = '';

  /** returnsEamt */
  returnsEamt = '';

  /** returnsPamt */
  returnsPamt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_INFO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** ssInfoIds */
  ssInfoIds = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_MAKEUP_PAY_MON_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMakeupPayMonId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMon = '';

  /** subcontractName */
  subcontractName = '';

  /** welfareProcessor */
  welfareProcessor = '';

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class payAllowwanceDetailDTO {
  /** 开户行 */
  accountBank = '';

  /** 开户名称 */
  accountName = '';

  /** 金额 */
  amount = '';

  /** 银行帐号 */
  bankAcck = '';

  /** 银行名称 */
  bankName = '';

  /** cashId */
  cashId = '';

  /** 客户编号 */
  custCode = '';

  /** custId */
  custId = '';

  /** 客户姓名 */
  custName = '';

  /** 员工编号 */
  empCode = '';

  /** empId */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** 身份证 */
  idCardNum = '';

  /** payAllowanceDetailid */
  payAllowanceDetailid = undefined;

  /** payAuditId */
  payAuditId = '';

  /** processInsId */
  processInsId = '';
}

class payAuditDTO {
  /** acctBankName */
  acctBankName = '';

  /** acctId */
  acctId = '';

  /** acctIdBankName */
  acctIdBankName = '';

  /** acctIdName */
  acctIdName = '';

  /** acctIdNum */
  acctIdNum = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** allowanceDetailIds */
  allowanceDetailIds = [];

  /** allowanceFileId */
  allowanceFileId = '';

  /** allowanceFileName */
  allowanceFileName = '';

  /** 本次使用金额 */
  amount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  amt = undefined;

  /** appendRemark */
  appendRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPLICANT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applicant = undefined;

  /** applicantDepartment */
  applicantDepartment = undefined;

  /** applicantDepartmentName */
  applicantDepartmentName = '';

  /** applicantName */
  applicantName = '';

  /** 申请人数 */
  applyCount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPLY_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applyDt = '';

  /** applyEndDt */
  applyEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPLY_PAY_AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applyPayAmt = undefined;

  /** applyPayAmtNoOne */
  applyPayAmtNoOne = '';

  /** applyPayAmtOne */
  applyPayAmtOne = '';

  /** applyProcessList */
  applyProcessList = [];

  /** applyStartDt */
  applyStartDt = '';

  /** 申请单抬头 */
  applyTitle = '';

  /** 审批通过时间小于等于 */
  approveEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPROVE_OPINION           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  approveOpinion = '';

  /** 审批通过时间大于等于 */
  approveStartDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPROVE_STATUS           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** 派单地申请单抬头 */
  assignerApplyTitle = '';

  /** 派单地 */
  assignerDepartmentId = '';

  /** auditId */
  auditId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.BANK_ACCT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  bankAcct = '';

  /** bank_acct_id */
  bankAcctId = '';

  /** bank_name */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** batchPayResult */
  batchPayResult = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.BILL_TYPE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  billType = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cashAmount */
  cashAmount = '';

  /** cashDt */
  cashDt = '';

  /** cashId */
  cashId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建人的查询条件 */
  createByInput = '';

  /** createByName */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** createDtTo */
  createDtTo = '';

  /** custCode */
  custCode = '';

  /** 客户确认状态 */
  custConfirmStatus = '';

  /** 客户填写的到款金额 */
  custEstimatedAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.CUST_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  custId = undefined;

  /** custList */
  custList = [];

  /** custName */
  custName = '';

  /** 客户填写的预计到款时间 */
  custSendMonth = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 支付地抬头id */
  depTitleId = '';

  /** 支付地抬头名称 */
  depTitleName = '';

  /** endIndex */
  endIndex = undefined;

  /** expType */
  expType = '';

  /** exportType */
  exportType = '';

  /** 离职补偿金综合 */
  f10008Sum = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** fileProviderName */
  fileProviderName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** governingArea */
  governingArea = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** invoiceAmount */
  invoiceAmount = '';

  /** invoiceDate */
  invoiceDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.INVOICE_QTY           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  invoiceQty = undefined;

  /** isActualPay */
  isActualPay = '';

  /** 系统数据调整，不实际支付 */
  isAdjustNoPay = '';

  /** 系统核查通过自动提交 1:是 0:否 */
  isAutoCommit = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDefault */
  isDefault = '';

  /** isDelayAmtFlag */
  isDelayAmtFlag = '';

  /** 删除标记 */
  isDeleted = '';

  /** 已获取标记 */
  isGet = '';

  /** 是否加入银企直连黑名单  0：否 1：是 */
  isJoinBlacklist = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** 传递到金蝶系统：是、否 */
  isPassKingdee = '';

  /** isPaySet */
  isPaySet = '';

  /** 供应商工资或其他 1:是 0:否 */
  isSupplier = '';

  /** 是否使用核查到款审批 */
  isUseCheckPayApprove = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.LAST_PAY_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  lastPayDt = '';

  /** 最晚支付日期到 */
  lastPayDtTo = '';

  /** lastPayEndDt */
  lastPayEndDt = '';

  /** lastPayStartDt */
  lastPayStartDt = '';

  /** 项目客服姓名 */
  liabilityCsName = '';

  /** lockMonUpdateDt */
  lockMonUpdateDt = '';

  /** lockMonUpdateDtM */
  lockMonUpdateDtM = '';

  /** maintainId */
  maintainId = '';

  /** mapLockMonUpdateDt */
  mapLockMonUpdateDt = undefined;

  /** mapLockMonUpdateDtM */
  mapLockMonUpdateDtM = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** newApproveOpinion */
  newApproveOpinion = '';

  /** noChange */
  noChange = false;

  /** 未自动提交原因 */
  notAutoCommitReason = '';

  /** 外包风险金id */
  orRiskFundId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** payAddress */
  payAddress = '';

  /** payAddressName */
  payAddressName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAmt = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_AUDIT_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAuditId = undefined;

  /** 发放批次名称(不展示，用于后台比较重复名称数据) */
  payAuditName = '';

  /** 发放批次编号 */
  payBatchCode = '';

  /** 发放批次id */
  payBatchId = '';

  /** 发放批次名称 */
  payBatchName = '';

  /** 批次类型：正常发放、虚拟发放 */
  payBatchType = '';

  /** class id集合 */
  payClassIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_DETAIL_METHOD           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payDetailMethod = '';

  /** payDetailType */
  payDetailType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payDt = '';

  /** payEndDt */
  payEndDt = '';

  /** 同步到中间库的pay_id */
  payId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_METHOD           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payMethod = undefined;

  /** payMethodName */
  payMethodName = '';

  /** payObject */
  payObject = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_PURPOSE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payPurpose = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_REASON           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payReason = '';

  /** send id集合 */
  paySends = '';

  /** payStartDt */
  payStartDt = '';

  /** payStatus */
  payStatus = '';

  /** payStatusName */
  payStatusName = '';

  /** payTreatmentDetail */
  payTreatmentDetail = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_TYPE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payType = undefined;

  /** payTypeName */
  payTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAYEE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payee = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAYEE_BANK           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payeeBank = '';

  /** payeeBankName */
  payeeBankName = '';

  /** 到账日期 */
  payeeDt = '';

  /** 附言 */
  postscript = '';

  /** postscriptRmk */
  postscriptRmk = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PROCESS_INS_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  processInsId = undefined;

  /** providerId */
  providerId = '';

  /** providerIdAlias */
  providerIdAlias = '';

  /** providerName */
  providerName = '';

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupId */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** prvdGroupName */
  prvdGroupName = '';

  /** prvdPayType */
  prvdPayType = '';

  /** rcvIds */
  rcvIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.REMARK           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  remark = '';

  /** 审批意见 */
  remarkText = '';

  /** remindUserIds */
  remindUserIds = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 外包风险金雇员列表 */
  rfelist = [];

  /** 本次总金额 */
  riskAmount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.RPT_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  rptDt = '';

  /** rptDtTo */
  rptDtTo = '';

  /** sBatchId */
  sBatchId = '';

  /** secondBank */
  secondBank = '';

  /** secondBankAcct */
  secondBankAcct = '';

  /** secondBankBranch */
  secondBankBranch = '';

  /** secondCity */
  secondCity = '';

  /** secondPayee */
  secondPayee = '';

  /** secondProvince */
  secondProvince = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 发放日期 */
  sendDt = '';

  /** 发放通道 */
  sendWay = '';

  /** sqlmapName */
  sqlmapName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.SS_GROUP_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.WELFARE_PROCESSOR           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** 扣缴义务人ID集合 */
  withholdAgentIds = '';

  /** 扣缴义务人名称集合 */
  withholdAgentNames = '';

  /** 扣缴义务人类型 */
  withholdAgentType = '';

  /** workitemId */
  workitemId = '';
}

class payAuditQuery {
  /** acctBankName */
  acctBankName = '';

  /** acctId */
  acctId = '';

  /** acctIdBankName */
  acctIdBankName = '';

  /** acctIdName */
  acctIdName = '';

  /** acctIdNum */
  acctIdNum = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** allowanceDetailIds */
  allowanceDetailIds = [];

  /** allowanceFileId */
  allowanceFileId = '';

  /** allowanceFileName */
  allowanceFileName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  amt = undefined;

  /** appendRemark */
  appendRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPLICANT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applicant = undefined;

  /** applicantDepartment */
  applicantDepartment = undefined;

  /** applicantDepartmentName */
  applicantDepartmentName = '';

  /** applicantName */
  applicantName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPLY_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applyDt = '';

  /** applyEndDt */
  applyEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPLY_PAY_AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  applyPayAmt = undefined;

  /** applyPayAmtNoOne */
  applyPayAmtNoOne = '';

  /** applyPayAmtOne */
  applyPayAmtOne = '';

  /** applyStartDt */
  applyStartDt = '';

  /** 申请单抬头 */
  applyTitle = '';

  /** 审批通过时间小于等于 */
  approveEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPROVE_OPINION           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  approveOpinion = '';

  /** 审批通过时间大于等于 */
  approveStartDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.APPROVE_STATUS           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** assigneeBranchId */
  assigneeBranchId = '';

  /** 派单地申请单抬头 */
  assignerApplyTitle = '';

  /** assignerAreaId */
  assignerAreaId = '';

  /** assignerBranchId */
  assignerBranchId = '';

  /** 派单地 */
  assignerDepartmentId = '';

  /** auditId */
  auditId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.BANK_ACCT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  bankAcct = '';

  /** bank_acct_id */
  bankAcctId = '';

  /** bank_name */
  bankName = '';

  /** batchPayResult */
  batchPayResult = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.BILL_TYPE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  billType = '';

  /** bizmanType */
  bizmanType = '';

  /** cashAmount */
  cashAmount = '';

  /** cashDt */
  cashDt = '';

  /** cashId */
  cashId = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** createByName */
  createByName = '';

  /** createDtTo */
  createDtTo = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.CUST_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 支付地抬头id */
  depTitleId = '';

  /** 支付地抬头名称 */
  depTitleName = '';

  /** empCode */
  empCode = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** expType */
  expType = '';

  /** exportType */
  exportType = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** fileProviderName */
  fileProviderName = '';

  /** governingArea */
  governingArea = '';

  /** idCardNum */
  idCardNum = '';

  /** invoiceAmount */
  invoiceAmount = '';

  /** invoiceDate */
  invoiceDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.INVOICE_QTY           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  invoiceQty = undefined;

  /** isActualPay */
  isActualPay = '';

  /** isDelayAmtFlag */
  isDelayAmtFlag = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** 传递到金蝶系统：是、否 */
  isPassKingdee = '';

  /** isPaySet */
  isPaySet = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.LAST_PAY_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  lastPayDt = '';

  /** lastPayEndDt */
  lastPayEndDt = '';

  /** lastPayStartDt */
  lastPayStartDt = '';

  /** newApproveOpinion */
  newApproveOpinion = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** payAddress */
  payAddress = '';

  /** payAddressName */
  payAddressName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAmt = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_AUDIT_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAuditId = undefined;

  /** 发放批次编号 */
  payBatchCode = '';

  /** 发放批次id */
  payBatchId = '';

  /** 发放批次名称 */
  payBatchName = '';

  /** 批次类型：正常发放、虚拟发放 */
  payBatchType = '';

  /** class id集合 */
  payClassIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_DETAIL_METHOD           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payDetailMethod = '';

  /** payDetailType */
  payDetailType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payDt = '';

  /** payEndDt */
  payEndDt = '';

  /** 同步到中间库的pay_id */
  payId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_METHOD           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payMethod = undefined;

  /** payMethodName */
  payMethodName = '';

  /** payObject */
  payObject = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_PURPOSE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payPurpose = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_REASON           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payReason = '';

  /** send id集合 */
  paySends = '';

  /** payStartDt */
  payStartDt = '';

  /** payStatus */
  payStatus = '';

  /** payStatusName */
  payStatusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAY_TYPE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payType = undefined;

  /** payTypeName */
  payTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAYEE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payee = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PAYEE_BANK           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payeeBank = '';

  /** payeeBankName */
  payeeBankName = '';

  /** 到账日期 */
  payeeDt = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.PROCESS_INS_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  processInsId = undefined;

  /** providerId */
  providerId = '';

  /** providerIdAlias */
  providerIdAlias = '';

  /** providerName */
  providerName = '';

  /** provinceName */
  provinceName = '';

  /** prvdGroupId */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** prvdGroupName */
  prvdGroupName = '';

  /** prvdPayType */
  prvdPayType = '';

  /** rcvIds */
  rcvIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.REMARK           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  remark = '';

  /** remindUserIds */
  remindUserIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.RPT_DT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  rptDt = '';

  /** sBatchId */
  sBatchId = '';

  /** secondBank */
  secondBank = '';

  /** secondBankAcct */
  secondBankAcct = '';

  /** secondBankBranch */
  secondBankBranch = '';

  /** secondCity */
  secondCity = '';

  /** secondPayee */
  secondPayee = '';

  /** secondProvince */
  secondProvince = '';

  /** 发放日期 */
  sendDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.SS_GROUP_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** startIndex */
  startIndex = undefined;

  /** subcontractAlias */
  subcontractAlias = '';

  /** userId */
  userId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SS_PAY_AUDIT.WELFARE_PROCESSOR           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** workitemId */
  workitemId = '';
}

class processInfoDTO {
  /** acct */
  acct = '';

  /** addConfirmStatus */
  addConfirmStatus = '';

  /** addConfirmStatusName */
  addConfirmStatusName = '';

  /** allRemark */
  allRemark = '';

  /** alterStatus */
  alterStatus = '';

  /** alterStatusName */
  alterStatusName = '';

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** applyProcessList */
  applyProcessList = [];

  /** areaType */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** 接单客服id */
  assigneeCs = '';

  /** assigneeCsName */
  assigneeCsName = '';

  /** 派单客服id */
  assignerCs = '';

  /** batchId */
  batchId = '';

  /** changeRemark */
  changeRemark = '';

  /** cityId */
  cityId = '';

  /** contractId */
  contractId = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.CUST_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_HIRE_SEP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empHireSepId = undefined;

  /** empHireSepStatus */
  empHireSepStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empId = '';

  /** empName */
  empName = '';

  /** filterEndMon */
  filterEndMon = '';

  /** hireDt */
  hireDt = '';

  /** idCardNum */
  idCardNum = '';

  /** insuranceName */
  insuranceName = '';

  /** isIndependent */
  isIndependent = '';

  /** lockMon */
  lockMon = '';

  /** lockMonM */
  lockMonM = '';

  /** makeupPayList */
  makeupPayList = [];

  /** makeupPayMonList */
  makeupPayMonList = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.NEXT_POINT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  nextPoint = undefined;

  /** orderSsGroupSvc */
  orderSsGroupSvc = new OrderSsGroupSvc();

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.P_ACCT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAcct = undefined;

  /** pAcctEx */
  pAcctEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PRE_POINT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  prePoint = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processDt = '';

  /** processEndDt */
  processEndDt = '';

  /** processInfo */
  processInfo = new ProcessInfo();

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_REMARK           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processRemark = '';

  /** processRemark2 */
  processRemark2 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_TYPE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processType = undefined;

  /** processTypeName */
  processTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESSOR_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processorId = undefined;

  /** processorName */
  processorName = '';

  /** queryMode */
  queryMode = '';

  /** remitList */
  remitList = [];

  /** rptHireDt */
  rptHireDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.S_NO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  sNo = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** 签单客服id */
  signCs = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_INFO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** ssType */
  ssType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STATUS           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopBy = undefined;

  /** stopByName */
  stopByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopDt = '';

  /** stopEndDt */
  stopEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_REMARK           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_TYPE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopType = undefined;

  /** stopTypeName */
  stopTypeName = '';

  /** strStatus */
  strStatus = '';

  /** subcontractId */
  subcontractId = '';

  /** subcontractName */
  subcontractName = '';

  /** typeId */
  typeId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_END_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESS_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESSOR           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_START_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class processInfoQuery {
  /** acct */
  acct = '';

  /** addConfirmStatus */
  addConfirmStatus = '';

  /** addConfirmStatusName */
  addConfirmStatusName = '';

  /** addPerfectDtFrom */
  addPerfectDtFrom = '';

  /** addPerfectDtTo */
  addPerfectDtTo = '';

  /** allRemark */
  allRemark = '';

  /** alterStatus */
  alterStatus = '';

  /** alterStatusName */
  alterStatusName = '';

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** applyDtFrom */
  applyDtFrom = '';

  /** applyDtTo */
  applyDtTo = '';

  /** areaType */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** 接单客服id */
  assigneeCs = '';

  /** assigneeCsId */
  assigneeCsId = '';

  /** assigneeCsName */
  assigneeCsName = '';

  /** 派单客服id */
  assignerCs = '';

  /** autoCheck */
  autoCheck = undefined;

  /** boUrl */
  boUrl = '';

  /** changeStartDateMark */
  changeStartDateMark = '';

  /** chargeStartDateFrom */
  chargeStartDateFrom = '';

  /** chargeStartDateTo */
  chargeStartDateTo = '';

  /** cityId */
  cityId = '';

  /** cityIds */
  cityIds = '';

  /** confirmStatus */
  confirmStatus = '';

  /** confirmStatusName */
  confirmStatusName = '';

  /** contractId */
  contractId = '';

  /** 合同类别 */
  contractType = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.CUST_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** detailStatus */
  detailStatus = undefined;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_HIRE_SEP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empHireSepId = undefined;

  /** empHireSepStatus */
  empHireSepStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empId = '';

  /** empName */
  empName = '';

  /** empStatus */
  empStatus = '';

  /** empType */
  empType = undefined;

  /** endIndex */
  endIndex = undefined;

  /** filterEndMon */
  filterEndMon = '';

  /** hasSepCertificate */
  hasSepCertificate = undefined;

  /** hireDt */
  hireDt = '';

  /** hireDtFrom */
  hireDtFrom = '';

  /** hireDtTo */
  hireDtTo = '';

  /** idCardNum */
  idCardNum = '';

  /** insuranceName */
  insuranceName = '';

  /** 增员是否需要实做 */
  isAddProcess = '';

  /** isIndependent */
  isIndependent = '';

  /** isProduct */
  isProduct = undefined;

  /** 是否查询申报结果 */
  isQueryDeclaration = '';

  /** 减员是否需要实做 */
  isReduceProcess = '';

  /** medicalLevel */
  medicalLevel = '';

  /** medicalLevelId */
  medicalLevelId = undefined;

  /** modeOfOperation */
  modeOfOperation = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.NEXT_POINT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  nextPoint = undefined;

  /** orderItem */
  orderItem = '';

  /** orderServiceIds */
  orderServiceIds = [];

  /** orderType */
  orderType = '';

  /** 对应组织code，多个用英文逗号分开 */
  orgCode = '';

  /** outPutType */
  outPutType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.P_ACCT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAcct = undefined;

  /** pAcctEx */
  pAcctEx = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** peggingEndDate */
  peggingEndDate = '';

  /** peggingStartDate */
  peggingStartDate = '';

  /** peggingStatus */
  peggingStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PRE_POINT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  prePoint = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processDt = '';

  /** processDtFrom */
  processDtFrom = '';

  /** processDtTo */
  processDtTo = '';

  /** processEndDt */
  processEndDt = '';

  /** processList */
  processList = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_REMARK           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_TYPE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processType = undefined;

  /** processTypeName */
  processTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESSOR_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processorId = undefined;

  /** processorName */
  processorName = '';

  /** queryMode */
  queryMode = '';

  /** reduceSendChannel */
  reduceSendChannel = '';

  /** rpaPeggingVOList */
  rpaPeggingVOList = [];

  /** rptHireDt */
  rptHireDt = '';

  /** rptHireDtFrom */
  rptHireDtFrom = '';

  /** rptHireDtTo */
  rptHireDtTo = '';

  /** rptSepDtFrom */
  rptSepDtFrom = '';

  /** rptSepDtTo */
  rptSepDtTo = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.S_NO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  sNo = '';

  /** sendChannel */
  sendChannel = '';

  /** sendCount */
  sendCount = undefined;

  /** sendStatus */
  sendStatus = undefined;

  /** sendTo */
  sendTo = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** sepDtFrom */
  sepDtFrom = '';

  /** sepDtTo */
  sepDtTo = '';

  /** 签单客服id */
  signCs = '';

  /** singleOrBatch */
  singleOrBatch = undefined;

  /** socialName */
  socialName = '';

  /** splitPlace */
  splitPlace = '';

  /** ssBatchId */
  ssBatchId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_INFO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** ssPackageId */
  ssPackageId = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STATUS           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  status = '';

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopBy = undefined;

  /** stopByName */
  stopByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopDt = '';

  /** stopEndDt */
  stopEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_REMARK           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_TYPE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopType = undefined;

  /** stopTypeName */
  stopTypeName = '';

  /** strStatus */
  strStatus = '';

  /** subcontractAlias */
  subcontractAlias = '';

  /** subcontractId */
  subcontractId = '';

  /** subcontractName */
  subcontractName = '';

  /** templateId */
  templateId = '';

  /** userId */
  userId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_END_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESS_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESSOR           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_START_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class productRatioDTO {
  /** activityNameEn */
  activityNameEn = '';

  /** adjustNotes */
  adjustNotes = '';

  /** adjustType */
  adjustType = '';

  /** adjustTypeName */
  adjustTypeName = '';

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMonth */
  annualPaymentMonth = '';

  /** appMark */
  appMark = '';

  /** approveOpinion */
  approveOpinion = '';

  /** areaId */
  areaId = '';

  /** areaName */
  areaName = '';

  /** calculationOrder */
  calculationOrder = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** eAdditionalAmt */
  eAdditionalAmt = undefined;

  /** eCalculationMethod */
  eCalculationMethod = '';

  /** eCalculationMethodName */
  eCalculationMethodName = '';

  /** eMaxBase */
  eMaxBase = '';

  /** eMaxRatio */
  eMaxRatio = undefined;

  /** eMinBase */
  eMinBase = '';

  /** eMinRatio */
  eMinRatio = undefined;

  /** ePrecision */
  ePrecision = '';

  /** ePrecisionName */
  ePrecisionName = '';

  /** eRatio */
  eRatio = '';

  /** eRatioStep */
  eRatioStep = undefined;

  /** epRatioRelationship */
  epRatioRelationship = '';

  /** epRatioRelationshipName */
  epRatioRelationshipName = '';

  /** epRatioRelative */
  epRatioRelative = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** insuranceMonth */
  insuranceMonth = '';

  /** isDefault */
  isDefault = '';

  /** lastEndMonth */
  lastEndMonth = '';

  /** lastStartMonth */
  lastStartMonth = '';

  /** pAdditionalAmt */
  pAdditionalAmt = undefined;

  /** pCalculationMethod */
  pCalculationMethod = '';

  /** pCalculationMethodName */
  pCalculationMethodName = '';

  /** pMaxBase */
  pMaxBase = '';

  /** pMaxRatio */
  pMaxRatio = undefined;

  /** pMinBase */
  pMinBase = '';

  /** pMinRatio */
  pMinRatio = undefined;

  /** pPrecision */
  pPrecision = '';

  /** pPrecisionName */
  pPrecisionName = '';

  /** pRatio */
  pRatio = '';

  /** pRatioStep */
  pRatioStep = undefined;

  /** participant */
  participant = '';

  /** payFrequency */
  payFrequency = '';

  /** payFrequencyName */
  payFrequencyName = '';

  /** policyLink */
  policyLink = '';

  /** processDefId */
  processDefId = '';

  /** processInsId */
  processInsId = '';

  /** productId */
  productId = '';

  /** productName */
  productName = '';

  /** productRatioAppId */
  productRatioAppId = '';

  /** productRatioDetailList */
  productRatioDetailList = [];

  /** productRatioDetailListTemp */
  productRatioDetailListTemp = [];

  /** productRatioId */
  productRatioId = '';

  /** productRatioName */
  productRatioName = '';

  /** reMark */
  reMark = '';

  /** status */
  status = '';

  /** statusText */
  statusText = '';

  /** updateByName */
  updateByName = '';

  /** workitemId */
  workitemId = '';
}

class productRatioDetailDTO {
  /** adjustNotes */
  adjustNotes = '';

  /** adjustType */
  adjustType = '';

  /** adjustTypeName */
  adjustTypeName = '';

  /** 年度平均工资 */
  annualAvgSalary = '';

  /** 年度最低工资 */
  annualMinSalary = '';

  /** eMaxBase */
  eMaxBase = undefined;

  /** eMinBase */
  eMinBase = undefined;

  /** endMonth */
  endMonth = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** pMaxBase */
  pMaxBase = undefined;

  /** pMinBase */
  pMinBase = undefined;

  /** policyLink */
  policyLink = '';

  /** productRatioAppId */
  productRatioAppId = '';

  /** productRatioDetailId */
  productRatioDetailId = '';

  /** productRatioId */
  productRatioId = '';

  /** startMonth */
  startMonth = '';
}

class productRatioDetailDTO1 {
  /** 数据列表 */
  productRatioDetail = [];
}

class remitDTO {
  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  amt = undefined;

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMon */
  annualPaymentMon = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.CREATE_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createBy = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.CREATE_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_ADDITIONAL_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_BASE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eBase = undefined;

  /** 企业计算方法 */
  eCalculationMethod = '';

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业精确值 */
  ePrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_RATIO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.IS_DELETED           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  isDeleted = undefined;

  /** isOneTimePay */
  isOneTimePay = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.MIMIC_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  mimicBy = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_ADDITIONAL_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_AMT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_BASE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pBase = undefined;

  /** 个人计算方法 */
  pCalculationMethod = '';

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人精确值 */
  pPrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_RATIO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** payFrequency */
  payFrequency = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PRODUCT_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PRODUCT_RATIO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productRatioId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PROXY_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  proxyBy = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_INFO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_REMIT_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssRemitId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.UPDATE_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateBy = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.UPDATE_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_END_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_PROCESS_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_START_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class remitMonDTO {
  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.AMT           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  amt = undefined;

  /** buttonQueryStatus */
  buttonQueryStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.E_ADDITIONAL_AMT           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  eAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.E_AMT           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  eAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.E_BASE           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  eBase = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.E_RATIO           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  eRatio = undefined;

  /** endMonth */
  endMonth = '';

  /** isSsExclued */
  isSsExclued = '';

  /** isSsExcluedFlag */
  isSsExcluedFlag = '';

  /** makeupPayProEndMon */
  makeupPayProEndMon = '';

  /** makeupPayProMon */
  makeupPayProMon = '';

  /** 月份数 */
  monthNums = '';

  /** num */
  num = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.P_ADDITIONAL_AMT           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.P_AMT           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.P_BASE           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  pBase = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.P_RATIO           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  pRatio = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.PRODUCT_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.PRODUCT_RATIO_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  productRatioId = '';

  /** productRatioName */
  productRatioName = '';

  /** remark */
  remark = '';

  /** showAmt */
  showAmt = '';

  /** ssEndMon */
  ssEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.SS_INFO_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssInfoId = undefined;

  /** ssInfoIds */
  ssInfoIds = '';

  /** This field was generated by Apache iBATIS ibator.       This field corresponds to the database column SS_REMIT_MON.SS_MON      变更删除月度表记录删除条件       @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssMon = '';

  /** 停办删除月度表记录删除条件 */
  ssMon2 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.SS_REMIT_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssRemitId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT_MON.SS_REMIT_MON_ID           @ibatorgenerated Tue Jan 31 16:10:38 CST 2012 */
  ssRemitMonId = undefined;

  /** startMonth */
  startMonth = '';

  /** state */
  state = '';
}

class socialDetailDTO {
  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  amt = undefined;

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** approveStatus */
  approveStatus = '';

  /** assigneeProvider */
  assigneeProvider = '';

  /** assignerCs */
  assignerCs = '';

  /** assignerProvider */
  assignerProvider = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.CONTRACT_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  contractId = undefined;

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractType */
  contractType = '';

  /** 增加查询的字段 */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.E_AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  eAmt = undefined;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.EMP_HIRE_SEP_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  empHireSepId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.EMP_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  empId = undefined;

  /** empName */
  empName = '';

  /** idCardNum */
  idCardNum = '';

  /** idc18 */
  idc18 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.MON           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  mon = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.P_AMT           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.PAY_AUDIT_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  payAuditId = undefined;

  /** payee2 */
  payee2 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.PRODUCT_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  productId = undefined;

  /** productIdAmt200 */
  productIdAmt200 = '';

  /** productIdAmt201 */
  productIdAmt201 = '';

  /** productIdAmt202 */
  productIdAmt202 = '';

  /** productIdAmt203 */
  productIdAmt203 = '';

  /** productIdAmt204 */
  productIdAmt204 = '';

  /** productIdAmt240 */
  productIdAmt240 = '';

  /** productIdEmpname200 */
  productIdEmpname200 = '';

  /** productIdEmpname201 */
  productIdEmpname201 = '';

  /** productIdEmpname202 */
  productIdEmpname202 = '';

  /** productIdEmpname203 */
  productIdEmpname203 = '';

  /** productIdEmpname204 */
  productIdEmpname204 = '';

  /** productIdEmpname240 */
  productIdEmpname240 = '';

  /** productName */
  productName = '';

  /** signProvider */
  signProvider = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.SOCIAL_DETAIL_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  socialDetailId = undefined;

  /** sqlStr */
  sqlStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.SS_GROUP_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssMon */
  ssMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.SUBCONTRACT_ID           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  subcontractId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_SOCIAL_DETAIL.TYPE           @ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
  type = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class socialSecurityGroupDTO {
  /** annualAdjustMonth */
  annualAdjustMonth = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** collectionMethod */
  collectionMethod = '';

  /** defaultDisplay */
  defaultDisplay = '';

  /** eAmtRemitMethod */
  eAmtRemitMethod = '';

  /** firstRemitMethod */
  firstRemitMethod = '';

  /** insuranceName */
  insuranceName = '';

  /** isPrcNeedApply */
  isPrcNeedApply = '';

  /** orgCode */
  orgCode = undefined;

  /** pAmtRemitMethod */
  pAmtRemitMethod = '';

  /** processDeadline */
  processDeadline = '';

  /** processDeadlineEnd */
  processDeadlineEnd = '';

  /** processDeadlineStart */
  processDeadlineStart = '';

  /** remark */
  remark = '';

  /** serviceMonthType */
  serviceMonthType = '';

  /** socialGroupNewList */
  socialGroupNewList = [];

  /** socialGroupStopList */
  socialGroupStopList = [];

  /** socialSecurityGroupProductList */
  socialSecurityGroupProductList = [];

  /** socialSecurityGroupRatioList */
  socialSecurityGroupRatioList = [];

  /** ssGroupId */
  ssGroupId = '';

  /** ssGroupType */
  ssGroupType = '';

  /** stopDeadline */
  stopDeadline = '';

  /** stopDeadlineEnd */
  stopDeadlineEnd = '';

  /** stopDeadlineStart */
  stopDeadlineStart = '';

  /** stopMethod */
  stopMethod = '';
}

class socialSecurityGroupDTO2 {
  /** annualAdjustMonth */
  annualAdjustMonth = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** collectionMethod */
  collectionMethod = '';

  /** defaultDisplay */
  defaultDisplay = '';

  /** eAmtRemitMethod */
  eAmtRemitMethod = '';

  /** firstRemitMethod */
  firstRemitMethod = '';

  /** insuranceName */
  insuranceName = '';

  /** isPrcNeedApply */
  isPrcNeedApply = '';

  /** orgCode */
  orgCode = undefined;

  /** pAmtRemitMethod */
  pAmtRemitMethod = '';

  /** processDeadline */
  processDeadline = '';

  /** processDeadlineEnd */
  processDeadlineEnd = '';

  /** processDeadlineStart */
  processDeadlineStart = '';

  /** remark */
  remark = '';

  /** serviceMonthType */
  serviceMonthType = '';

  /** socialGroupNewList */
  socialGroupNewList = [];

  /** socialGroupStopList */
  socialGroupStopList = [];

  /** socialSecurityGroupProductList */
  socialSecurityGroupProductList = [];

  /** socialSecurityGroupRatioList */
  socialSecurityGroupRatioList = [];

  /** ssGroupId */
  ssGroupId = '';

  /** 数据列表 */
  ssGroupProductDeleteList = [];

  /** ssGroupType */
  ssGroupType = '';

  /** stopDeadline */
  stopDeadline = '';

  /** stopDeadlineEnd */
  stopDeadlineEnd = '';

  /** stopDeadlineStart */
  stopDeadlineStart = '';

  /** stopMethod */
  stopMethod = '';
}

class socialSecurityWelfareDTO {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** disabilityPaymentsName */
  disabilityPaymentsName = '';

  /** housingFundName */
  housingFundName = '';

  /** illnessInsuranceName */
  illnessInsuranceName = '';

  /** industrialInjuryInsuranceName */
  industrialInjuryInsuranceName = '';

  /** isDefault */
  isDefault = '';

  /** isDefaultName */
  isDefaultName = '';

  /** maternityInsuranceName */
  maternityInsuranceName = '';

  /** medicalInsuranceName */
  medicalInsuranceName = '';

  /** pensionName */
  pensionName = '';

  /** socialSecurityWelfareDetailList */
  socialSecurityWelfareDetailList = [];

  /** socialSecurityWelfareDetailListForOrder */
  socialSecurityWelfareDetailListForOrder = [];

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** ssWelfarePkgId */
  ssWelfarePkgId = '';

  /** ssWelfarePkgName */
  ssWelfarePkgName = '';

  /** unemploymentInsuranceName */
  unemploymentInsuranceName = '';

  /** validDate */
  validDate = '';

  /** welfareRealName */
  welfareRealName = '';
}

class socialSecurityWelfareDetailDTO {
  /** eMaxRatio */
  eMaxRatio = undefined;

  /** eMinRatio */
  eMinRatio = undefined;

  /** eRatio */
  eRatio = undefined;

  /** eRatioStep */
  eRatioStep = undefined;

  /** insuranceName */
  insuranceName = '';

  /** pMaxRatio */
  pMaxRatio = undefined;

  /** pMinRatio */
  pMinRatio = undefined;

  /** pRatio */
  pRatio = undefined;

  /** pRatioStep */
  pRatioStep = undefined;

  /** productId */
  productId = '';

  /** productName */
  productName = '';

  /** productRatioId */
  productRatioId = '';

  /** productRatioName */
  productRatioName = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssWelfarePkgDetailId */
  ssWelfarePkgDetailId = '';

  /** ssWelfarePkgId */
  ssWelfarePkgId = '';
}

export const welfaremanage = {
  AddEmpTransact,
  AddTransactDTO,
  AdjTaskQuery,
  BaseEntity,
  CityProcessConfigQuery,
  ClientExportConfig,
  CommonResponse,
  Contract,
  ContractFile,
  ContractRetiree,
  CustAcct,
  DropdownList,
  EbmBusinessIfs,
  EbmBusinessImpTask,
  EbmBusinessSubType,
  EbmHpTransact,
  EbmIfsItem,
  EbmMaterals,
  EbmTransactLog,
  EbmTransactQuery,
  EleContractInfoDTO,
  EleContractInfoQuery,
  EmpAcct,
  EmpAcctDTO,
  EmpPaymentResult,
  EmployeeFee,
  EmployeeFile,
  EmployeeFileDetail,
  EmployeeFileQuery,
  EntPaymentResult,
  ExOneChargesDTO,
  ExportField,
  ExportQuery,
  File,
  FileInfo,
  FilePayDetail,
  FilterEntity,
  GenerateBillContionDTO,
  HashMap,
  HsLaborEosApproveDetailQuery,
  HsLaborFileBatch,
  HsLaborFileBatchItem,
  HsLaborctEosRenew,
  HsLaborctEosRenewDTO,
  HsLaborctEosRenewQuery,
  HzSupplyCert,
  HzSupplyDTO,
  HzSupplyEmpAddDTO,
  ImpRuleQuery,
  ImpSIBackpayBase,
  ImpSIBackpayItem,
  LaborBatchFileQuery,
  LaborContract,
  LaborContractDTO,
  LaborContractHistoryDTO,
  LaborContractQuery,
  LateFeeBudget,
  LockMon,
  LockMonQuery,
  MakeupPay,
  MakeupPayMon,
  Map,
  Material,
  OneChargesDTO,
  OrderSsGroupSvc,
  Page,
  PayAllowwanceDetail,
  PayAudit,
  PaySet,
  PayTreatmentDetail,
  ProcessInfo,
  ProcessInfoOpr,
  ProductRatioDetail,
  ProductRatioQuery,
  ProviderAcct,
  ProviderAcctDTO,
  Remit,
  RetireRecordQuery,
  RetirementQuery,
  RiskFundEmp,
  RpaBatchInfoDTO,
  RpaDetailDTO,
  RpaSsCustCertificate,
  RpaStampInfo,
  RpaTaxation,
  SSAcctDTO,
  SSRemitSyncToOrderQuery,
  SSRemitSyncToOrderVO,
  SecondCustPayer,
  SimpleBusinessDTO,
  SimpleBusinessQuery,
  SocialBatchQuery,
  SocialDetailQuery,
  SocialPayCustQuery,
  SocialSecurityGroupNew,
  SocialSecurityGroupProduct,
  SocialSecurityGroupQuery,
  SocialSecurityGroupRatio,
  SocialSecurityGroupStop,
  SocialSecurityReportConf,
  SocialSecurityWelfareDetail,
  SocialSecurityWelfareQuery,
  SsEntryRelate,
  SsEntryRelateResp,
  SsImpbatchDTO,
  SsPaymentResultQuery,
  SsReportConfQuery,
  SysSmsField,
  SysSmsLog,
  SysSmsLogQuery,
  dropdownListDTO,
  employeeFeeDTO,
  impSIBackpayBaseQuery,
  impSIBackpayInfoDTO,
  impSIBackpayInfoQuery,
  lockMonDTO,
  makeupPayMonDTO,
  payAllowwanceDetailDTO,
  payAuditDTO,
  payAuditQuery,
  processInfoDTO,
  processInfoQuery,
  productRatioDTO,
  productRatioDetailDTO,
  productRatioDetailDTO1,
  remitDTO,
  remitMonDTO,
  socialDetailDTO,
  socialSecurityGroupDTO,
  socialSecurityGroupDTO2,
  socialSecurityWelfareDTO,
  socialSecurityWelfareDetailDTO,
};
