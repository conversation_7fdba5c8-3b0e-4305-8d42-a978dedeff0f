class AdjustmentQuery {
  /** 调整范围 */
  adjRange = '';

  /** 调整开始月 */
  adjStartMonth = '';

  /** 调整任务id */
  adjTaskId = '';

  /** 调整类型 */
  adjType = '';

  /** endIndex */
  endIndex = undefined;

  /** 菜单类型1: 社保公积金调整2: 社保公积金最低基数调整3: 社保调整4: 社保最低基数调整5: 公积金调整6: 公积金最低基数调整 */
  menuType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 社保组id */
  ssGroupId = '';

  /** 社保组类型 */
  ssGroupType = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 任务名称 */
  taskName = '';

  /** 详情类型,1非订单非社保,2订单,3社保 */
  type = '';
}

class AdjustmentTask {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.ADJ_RANGE	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjRange = '';

  /** adjRangeName */
  adjRangeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.ADJ_START_MONTH	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjStartMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.ADJ_TASK_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjTaskId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.ADJ_TYPE	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjType = '';

  /** adjTypeName */
  adjTypeName = '';

  /** adjustmentTaskItemList */
  adjustmentTaskItemList = [];

  /** adjustmentTaskMinItemList */
  adjustmentTaskMinItemList = [];

  /** assigneeProvider */
  assigneeProvider = '';

  /** assigneeProviderId */
  assigneeProviderId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByName */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.FILE_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isIncludeZero */
  isIncludeZero = '';

  /** isIncludeZeroName */
  isIncludeZeroName = '';

  /** isLocked */
  isLocked = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** menuType */
  menuType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.SS_GROUP_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.STATUS	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  status = '';

  /** statusName */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** svcProvider */
  svcProvider = '';

  /** svcProviderId */
  svcProviderId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK.TASK_NAME	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  taskName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class AdjustmentTaskItem {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.ADJ_TASK_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjTaskId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.ADJ_TASK_ITEM_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjTaskItemId = '';

  /** baseBindingLevel */
  baseBindingLevel = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.E_RATIO_COL	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  eBaseCol = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.FORCE_ADD	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  forceAdd = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.FORCE_ADD_E_RATIO	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  forceAddERatio = undefined;

  /** forceAddName */
  forceAddName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.FORCE_ADD_P_RATIO	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  forceAddPRatio = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.FORCE_ADD_RATIO_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  forceAddRatioId = '';

  /** forceAddRatioName */
  forceAddRatioName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.NEW_E_RATIO	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  newERatio = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.NEW_P_RATIO	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  newPRatio = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.NEW_RATIO_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  newRatioId = '';

  /** newRatioName */
  newRatioName = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.OLD_RATIO_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  oldRatioId = '';

  /** oldRatioName */
  oldRatioName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.P_RATIO_COL	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  pBaseCol = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.PRODUCT_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  productId = '';

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_ITEM.SS_GROUP_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  ssGroupId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class AdjustmentTaskMinItem {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.ADJ_TASK_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjTaskId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.ADJ_TASK_ITEM_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  adjTaskMinItemId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.NEW_E_BASE	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  newEBase = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.NEW_P_BASE	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  newPBase = undefined;

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.OLD_E_BASE	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  oldEBase = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.OLD_P_BASE	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  oldPBase = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.PRODUCT_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  productId = '';

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column ADJ_TASK_MIN_ITEM.SS_GROUP_ID	  	  ibatorgenerated Tue Jun 19 16:08:59 CST 2012 */
  ssGroupId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class AgentWageIdCardNumManageQuery {
  /** 银行卡号 */
  bankAcct = '';

  /** 唯一号 */
  employeeCode = '';

  /** 姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 证件号码 */
  idCardNum = '';

  /** 是否标识 */
  isArchive = '';

  /** isWageQuery */
  isWageQuery = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class BaseEntity {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** feeId */
  feeId = undefined;

  /** feeType */
  feeType = undefined;

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationCode */
  quotationCode = '';

  /** quotationId */
  quotationId = undefined;

  /** quotationName */
  quotationName = '';

  /** quotationTotalSalPrice */
  quotationTotalSalPrice = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** transferId */
  transferId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BatchAlterEmpOrder {
  /** add */
  add = false;

  /** 变更编号 */
  batchAlterId = '';

  /** 批量变更名称 */
  batchAlterName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单起始月 */
  billStartMonth = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建时间<= */
  createDtTo = '';

  /** 客户编号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 所选个人订单的ids */
  empHireSepIds = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费起始月 */
  feeStartMonth = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 新账单模板 */
  newBillTempltId = '';

  /** 新账单模板名称 */
  newBillTempltName = '';

  /** 新收费模板 */
  newFeeTempltId = '';

  /** 新收费模板名称 */
  newFeeTempltName = '';

  /** noChange */
  noChange = false;

  /** 原账单模板 */
  oldBillTempltId = '';

  /** 原账单模板名称 */
  oldBillTempltName = '';

  /** 原收费模板 */
  oldFeeTempltId = '';

  /** 原收费模板名称 */
  oldFeeTempltName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 所选产品大类的ids */
  productCategoryIds = '';

  /** 所选产品的ids */
  productIds = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signBranchTitleId */
  signBranchTitleId = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 所选小合同的ids */
  subContractIds = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BatchAlterEmpOrderQuery {
  /** 批量变更名称 */
  batchAlterName = '';

  /** 创建日期起 */
  createDt = '';

  /** 创建日期止 */
  createDtTo = '';

  /** 客户ID */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class BatchDeleteProduct {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.BATCH_DEL_PRODUCT_ID	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  batchDelProductId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.CHANGE_END_MON	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  changeEndMon = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.CHANGE_NAME	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  changeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.CHANGE_START_MON	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  changeStartMon = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.CUST_ID	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** employees */
  employees = [];

  /** endIndex */
  endIndex = undefined;

  /** exRefundBillMon */
  exRefundBillMon = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productTypes */
  productTypes = [];

  /** products */
  products = [];

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.REFUND_BILL_MONTH	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  refundBillMon = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_BATCH_DEL_PRODUCT.REMARK	  	  ibatorgenerated Fri Mar 14 16:05:19 CST 2014 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** subcontracts */
  subcontracts = [];

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BatchDeleteProductQuery {
  /** 变更名称 */
  changeName = '';

  /** 创建日期结束 */
  createDtEnd = '';

  /** 创建日期开始 */
  createDtStart = '';

  /** 客户id */
  custId = '';

  /** employees */
  employees = [];

  /** endIndex */
  endIndex = undefined;

  /** 包括的收费月 */
  includedFeeMonth = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** productTypes */
  productTypes = [];

  /** products */
  products = [];

  /** 报入职时间结束 */
  rptHireDtEnd = '';

  /** 报入职时间开始 */
  rptHireDtStart = '';

  /** 报离职时间结束 */
  rptSepDtEnd = '';

  /** 报离职时间开始 */
  rptSepDtStart = '';

  /** startIndex */
  startIndex = undefined;

  /** subcontracts */
  subcontracts = [];
}

class CallCenterQuery {
  /** 增员确认状态 */
  addConfirmStatus = '';

  /** 变更状态 */
  alterStatus = '';

  /** 区域类型 */
  areaType = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 呼叫结果 */
  callResult = '';

  /** 呼叫状态 */
  callStatus = '';

  /** 呼叫类型 */
  callType = '';

  /** 手机 */
  cellPhone = '';

  /** 创建日期止 */
  createDtEd = '';

  /** 创建日期起 */
  createDtSt = '';

  /** 客户编码 */
  custCode = '';

  /** 客户编码 */
  custName = '';

  /** 雇员编码 */
  empCode = '';

  /** 雇员姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 入职日期止 */
  hireDtEd = '';

  /** 入职日期起 */
  hireDtSt = '';

  /** 创建日期止 */
  hsCreateDtEd = '';

  /** 创建日期起 */
  hsCreateDtSt = '';

  /** 是否发布者 */
  ifDistributer = '';

  /** 是否呼叫过 */
  isCalled = '';

  /** 字段排序 */
  orderByField = '';

  /** 规则排序 */
  orderByRule = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 供应商类型 */
  providerType = '';

  /** 接受者 */
  receiver = '';

  /** 报离职日期止 */
  rptSepDtEd = '';

  /** 报离职日期起 */
  rptSepDtSt = '';

  /** 离职确认状态 */
  sepConfirmStatus = '';

  /** 离职日期止 */
  sepDtEd = '';

  /** 离职日期起 */
  sepDtSt = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 电话 */
  telephone = '';
}

class CallCenterVO {
  /** 基础信息 */
  baseInfo = undefined;

  /** 呼叫状态 */
  hsCcStatus = new HsCcStatus();
}

class CcWorkOrder {
  /** add */
  add = false;

  /** 增员确认状态 */
  addConfirmStatusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.AGE           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  age = '';

  /** 接单方客服 */
  assigneeCs = '';

  /** assigneeCsName */
  assigneeCsName = '';

  /** 接单方供 */
  assigneeProvider = '';

  /** assigneeProviderName */
  assigneeProviderName = '';

  /** 派单方客服 */
  assignerCs = '';

  /** assignerCsName */
  assignerCsName = '';

  /** 派单方 */
  assignerProvider = '';

  /** assignerProviderName */
  assignerProviderName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** callNumber */
  callNumber = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** completionDt */
  completionDt = '';

  /** completionDtGE */
  completionDtGE = '';

  /** completionDtLE */
  completionDtLE = '';

  /** 联系电话 */
  contactNum = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.CREATE_BY           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.CREATE_DT           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  createDt = '';

  /** createDtGE */
  createDtGE = '';

  /** createDtLE */
  createDtLE = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EMP_HIRE_SEP_ID           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  empHireSepId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EMP_ID           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  empId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EMP_NAME           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  empName = '';

  /** 员工类别代理派遣 */
  empType = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_1           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr1 = '';

  /** 处理情况 */
  extAttr10 = '';

  /** 备注 */
  extAttr11 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_12           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr12 = '';

  /** extAttr12Name */
  extAttr12Name = '';

  /** extAttr1Name */
  extAttr1Name = '';

  /** 工单类型 0：话务工单 ，1：回访工单，2：微信工单，3：智能外呼工单 */
  extAttr2 = '';

  /** extAttr2Name */
  extAttr2Name = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_3           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr3 = '';

  /** extAttr3Name */
  extAttr3Name = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_4           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr4 = '';

  /** extAttr4Name */
  extAttr4Name = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_5           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr5 = '';

  /** extAttr5Name */
  extAttr5Name = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_6           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr6 = '';

  /** 问题点 */
  extAttr7 = '';

  /** CC需求 */
  extAttr8 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.EXT_ATTR_9           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  extAttr9 = '';

  /** extAttr9Name */
  extAttr9Name = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** finishDay */
  finishDay = '';

  /** firstDay */
  firstDay = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 在职状态 */
  hireStatusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.HISTORICAL_ADVICE           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  historicalAdvice = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.HS_CC_WORK_ORDER_ID           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  hsCcWorkOrderId = '';

  /** 缴费城市 */
  hsCityName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.IDENTITY_CARD           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  identityCard = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.IS_DELETED           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.MIMIC_BY           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** nowDay */
  nowDay = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.ORDER_ID           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  orderId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.PROCESS_INS_ID           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  processInsId = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.PROXY_BY           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** reviewContent */
  reviewContent = '';

  /** reviewPerson */
  reviewPerson = '';

  /** reviewTime */
  reviewTime = '';

  /** reviewType */
  reviewType = '';

  /** reviewTypeName */
  reviewTypeName = '';

  /** 入职呼叫启动表ID */
  rpaHireCallId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 减员确认状态 */
  sepConfirmStatusName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.TASK_STATUTS           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  taskStatuts = '';

  /** taskStatutsName */
  taskStatutsName = '';

  /** 手机号码 */
  tellphoneNum = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.UPDATE_BY           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  updateBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_CC_WORK_ORDER.UPDATE_DT           ibatorgenerated Fri Oct 20 10:35:37 CST 2017 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workitemId */
  workitemId = '';
}

class CcWorkOrderQuery {
  /** 接单客服 */
  assigneeCs = '';

  /** 接单方 */
  assigneeProvider = '';

  /** 派单客服 */
  assignerCs = '';

  /** 派单方 */
  assignerProvider = '';

  /** 完成日期起 */
  completionDtGE = '';

  /** 完成日期止 */
  completionDtLE = '';

  /** 创建日期起 */
  createDtGE = '';

  /** 创建日期止 */
  createDtLE = '';

  /** 客户id */
  custId = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 工单类型 0：话务工单 ，1：回访工单，2：微信工单，3：智能外呼工单 */
  extAttr2 = '';

  /** 外部属性4 */
  extAttr4 = '';

  /** 外部属性5 */
  extAttr5 = '';

  /** 外部属性9 */
  extAttr9 = '';

  /** 证件号码 */
  identityCard = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 参与者 */
  participant = '';

  /** 审核人 */
  reviewPerson = '';

  /** 审核类型 */
  reviewType = '';

  /** startIndex */
  startIndex = undefined;

  /** 任务状态 */
  taskStatuts = '';
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new AdjustmentTask();

  /** message */
  message = '';

  /** t */
  t = new AdjustmentTask();
}

class Contract {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agereedAmtReceiveMon = '';

  /** agreedPayDt */
  agreedPayDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** areaId */
  areaId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** attTypeDraftId */
  attTypeDraftId = '';

  /** attTypeDraftName */
  attTypeDraftName = '';

  /** attTypeLegalId */
  attTypeLegalId = '';

  /** attTypeLegalName */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  billDt = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** isIssuingSalary */
  contractCategeryName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartA = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartB = '';

  /** contractProductLineIds */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractRetrieveDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStatus = '';

  /** contractStatusName */
  contractStatusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSubType = '';

  /** 合同类别（子类）name */
  contractSubTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** contractSvcStateName */
  contractSvcStateName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractTemplateId = '';

  /** contractTerminationDate */
  contractTerminationDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 合同类别 */
  contractVersionType = '';

  /** isIssuingSalary */
  contractVersionTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByName */
  createByName = '';

  /** createByParty */
  createByParty = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  createType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  creditPeriod = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  custPayerId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custSealDt = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerSize */
  customerSize = '';

  /** defStatus */
  defStatus = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** draftRemark */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** estimateFirstBillDate */
  estimateFirstBillDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectedIncrease */
  expectedIncrease = '';

  /** expectedIncreaseOld */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务name */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /** firstWgApproveId */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区Name */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司Name */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** private String contractSvcStateName; */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** governingBranchName */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** fileId */
  importFileId = '';

  /** fileName */
  importFileName = '';

  /** inId */
  inId = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** invoiceMoney */
  invoiceMoney = '';

  /** NP-8564 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
  isAdjustRenewContract = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  isAssign = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** isDefer */
  isDefer = '';

  /** isDeferName */
  isDeferName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** isIssuingSalary */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** isSameInsur */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** isSecondaryDev */
  isSecondaryDev = '';

  /** isSecondaryDevName */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApprovalStatus = '';

  /** legalRemark */
  legalRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** nextContractId */
  nextContractId = '';

  /** nextContractName */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** noChange */
  noChange = false;

  /** 非标合同审批单:NON_STA_COCT_APPR_ID */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  parentContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  payCollectPoint = '';

  /** payMonth */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  payerName = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApprovalStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  processInstanceId = '';

  /** productLineIdLogs */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectPlanRequest = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectRemark = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerType */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 报价单集合id */
  quoIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  renewedContractNum = '';

  /** reportElEvacuatedDate */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** reportGlEvacuatedDate */
  reportGlEvacuatedDate = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** roleCode */
  roleCode = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增存量标识 （手工） */
  salFlagManual = '';

  /** salFlagManualName */
  salFlagManualName = '';

  /** salFlagName */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApprove = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealApproveStatus = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealOpinion = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signArea */
  signArea = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** signBranchTitleIdAreaId */
  signBranchTitleIdAreaId = '';

  /** signBranchTitleIdBranchId */
  signBranchTitleIdBranchId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 撤单原因 */
  stopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcRegion = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadFileName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadUrl = '';

  /** upt */
  upt = false;

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** workitemId */
  workitemId = '';
}

class ContractFile {
  /** 审批节点 */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同附件ID */
  contractFileId = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 附件ID */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 附件路径 */
  filePath = '';

  /** 附件类型 */
  fileType = '';

  /** 附件类型name */
  fileTypeName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程defId */
  processDefId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传步骤 */
  uploadStep = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractRetiree {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 人员姓名 */
  bz = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 人员姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 身份证号 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 大合同退休人员主键 */
  retireeId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class EmpBankCard {
  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.ACCOUNT_EMPLOYEE_NAME           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  accountEmployeeName = '';

  /** add */
  add = false;

  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.BANK_ACCT           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  bankAcct = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.BANK_ID           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  bankId = '';

  /** bankName */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busiName */
  busiName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.BUSI_TYPE           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  busiType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.CITY_CODE           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  cityCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.CITY_ID           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建时间到 */
  createDtEnd = '';

  /** 创建时间从 */
  createDtStart = '';

  /** 创建人姓名 */
  creatorName = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departmentId */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** disunityReason */
  disunityReason = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.EMP_BANK_CARD_ID           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  empBankCardId = '';

  /** 员工编号 */
  empCode = '';

  /** 订单id/上下岗id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** functionEnterType */
  functionEnterType = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isInterbank */
  isInterbank = '';

  /** isInterbank */
  isInterbankStr = '';

  /** isWageQuery */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** ������id.���ڼ�¼��־ʹ�� */
  oldSalesId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.OPEN_ADDRESS           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  openAddress = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.OPEN_BANK_NAME           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  openBankName = '';

  /** 其它银行名称 */
  otherBankId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_EMP_BANKCARD.PROVINCE_ID           ibatorgenerated Wed Jan 11 10:28:34 CST 2012 */
  provinceId = '';

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salesId */
  salesId = '';

  /** 销售名称 */
  salesName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 更新人姓名 */
  updaterName = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** wageClassName */
  wageClassName = '';
}

class EmpBankCardDTO {
  /** 帐户名称 */
  accountEmployeeName = '';

  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 账号 */
  bankAcct = '';

  /** 银行ID */
  bankId = '';

  /** 银行名称 */
  bankName = '';

  /** 分公司id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** 银行类型名称 */
  busiName = '';

  /** 银行类型 1: 工资 2 ：补医保 */
  busiType = '';

  /** 城市编号 */
  cityCode = '';

  /** 城市ID */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 大合同编号 */
  contractCode = '';

  /** 大合同id */
  contractId = '';

  /** 大合同名称 */
  contractName = '';

  /** 创建结束日期 */
  createDtEnd = '';

  /** 创建起始日期 */
  createDtStart = '';

  /** 创建人姓名 */
  creatorName = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 部门id */
  departmentId = '';

  /** 部门名称 */
  departmentName = '';

  /** 开户名和员工姓名不符原因 */
  disunityReason = '';

  /** 雇员银行卡ID */
  empBankCardId = '';

  /** 雇员编号 */
  empCode = '';

  /** 入离职id */
  empHireSepId = '';

  /** 雇员id */
  empId = '';

  /** 雇员名称 */
  empName = '';

  /** 进入的菜单类型 */
  functionEnterType = '';

  /** 身份证号 */
  idCardNum = '';

  /** 是否跨行（1是0否） */
  isInterbank = '';

  /** 原销售id */
  oldSalesId = '';

  /** 开户地 */
  openAddress = '';

  /** 开户行名称 */
  openBankName = '';

  /** 其它银行名称 */
  otherBankId = '';

  /** 省份ID */
  provinceId = '';

  /** 省份名称 */
  provinceName = '';

  /** 销售id */
  salesId = '';

  /** 销售名称 */
  salesName = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 修改人姓名 */
  updaterName = '';

  /** 薪资类别名称 */
  wageClassName = '';
}

class EmpBankCardQuery {
  /** 银行账号 */
  bankAcct = '';

  /** 银行ID */
  bankId = '';

  /** 客户ID */
  custId = '';

  /** 雇员编号 */
  empCode = '';

  /** endIndex */
  endIndex = undefined;

  /** 功能输入类型 */
  functionEnterType = '';

  /** 证件号码 */
  idCardNum = '';

  /** 是否有效 */
  isDeleted = '';

  /** 是否跨行发放 */
  isInterbank = '';

  /** isWageQuery */
  isWageQuery = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class EmpFeeMonthQuery {
  /** 增员状态 */
  addConfirmStatus = '';

  /** 增员状态，多个 */
  addConfirmStatusFW = '';

  /** 报增员状态范围 */
  addconfirmstatusFW = '';

  /** 变更状态 */
  alterStatus = '';

  /** 变更状态，多个 */
  alterStatusFW = '';

  /** alterstatusFW */
  alterstatusFW = '';

  /** 接单方客服 */
  assigneeCsId = '';

  /** 接单方id */
  assigneeProviderId = '';

  /** 派单方客服 */
  assignerCsId = '';

  /** 派单方id */
  assignerProviderId = '';

  /** 账单月止 */
  billMonthEnd = '';

  /** 账单月起 */
  billMonthStart = '';

  /** 客户id */
  custId = '';

  /** 雇员编码 */
  empCode = '';

  /** 雇员姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 证件号码 */
  idcNum = '';

  /** 证件类型 */
  idcType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 账单模板id */
  receivableTempltId = '';

  /** 减员状态 */
  sepConfirmStatus = '';

  /** 减员状态，多个 */
  sepConfirmStatusFW = '';

  /** 报减员状态范围 */
  sepconfirmstatusFW = '';

  /** 服务月止 */
  serviceMonthEnd = '';

  /** 服务月起 */
  serviceMonthStart = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同id */
  subcontractId = '';
}

class EmpHireSepTempAppDTO {
  /** 开户人姓名 */
  accountEmployeeName = '';

  /** 实际工作地 */
  actualWorkLoc = '';

  /** add */
  add = false;

  /** 增员确认人 */
  addConfirmBy = '';

  /** 增员确认时间 */
  addConfirmDate = '';

  /** 增员过程 */
  addConfirmPro = '';

  /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
  addConfirmStatus = '';

  /** 增员状态名称 */
  addConfirmStatusName = '';

  /** 增员接单确认时间 */
  addPerfectBy = '';

  /** 增员接单确认人 */
  addPerfectDate = '';

  /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
  addReason = '';

  /** 增员备注 */
  addRemark = '';

  /** 年龄 */
  age = '';

  /** 变更确认人 */
  alterConfirmBy = '';

  /** 变更确认时间 */
  alterConfirmDate = '';

  /** 变更确认过程 */
  alterConfirmPro = '';

  /** 变更接单确认人 */
  alterPerfectBy = '';

  /** 变更接单确时间 */
  alterPerfectDate = '';

  /** 变更备注 */
  alterRemark = '';

  /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
  alterStatus = '';

  /** 变更状态名称 */
  alterStatusName = '';

  /** 申请时间 */
  applyDt = '';

  /** 申请人 */
  applyUser = '';

  /** approvalNote */
  approvalNote = '';

  /** approvalState */
  approvalState = '';

  /** 大区类型 */
  areaType = '';

  /** 大区类型名称 */
  areaTypeName = '';

  /** 接单城市id */
  assigneeCityId = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 派单类型1 执行单2 协调单3 收集单 */
  assignmentType = '';

  /** 关联状态 */
  associationStatus = '';

  /** 银行卡号 */
  bankAcct = '';

  /** 银行卡更新人 */
  bankCardUpdateBy = '';

  /** 银行卡更新时间 */
  bankCardUpdateDt = '';

  /** baseInfo主键 */
  baseInfoId = '';

  /** 批次号,用于生成社保服务信息 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单模板id */
  billTempltId = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型 */
  category = '';

  /** 离职证明电子版本 */
  certificateSpId = '';

  /** certificateStatusName */
  certificateStatusName = '';

  /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
  changeMethod = '';

  /** 收费截至日期 */
  chargeEndDate = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 商保订单状态 */
  commInsurStatus = '';

  /** 商保订单状态name */
  commInsurStatusName = '';

  /** 确认备注 */
  confirmRemark = '';

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 法人单位id */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户方内部编号 */
  custInternalNum = '';

  /** 客户姓名 */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** 缴费实体 */
  custPayEntityName = '';

  /** 客户类型 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** 类型: 1,正常 2,大客户  */
  dataType = undefined;

  /** 申报工资 */
  decSalary = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** 客户端增员ID */
  empAddId = '';

  /** 客户端变更ID */
  empAlterId = '';

  /** feeId数组 */
  empFeeIdArray = '';

  /** 历史表主键 */
  empHireSepHisId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入职主记录ID */
  empHiresepMainId = '';

  /** 员工id */
  empId = '';

  /** 停缴id */
  empStopId = '';

  /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
  empStopProcessState = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empType = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empTypeId = undefined;

  /** 人员分类 */
  empTypeName = '';

  /** 唯一号 */
  employeeCode = '';

  /** 费用段列表 */
  employeeFeeList = [];

  /** 雇员姓名 */
  employeeName = '';

  /** 雇员状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** enhancedAgent */
  enhancedAgent = '';

  /** enhancedAgentName */
  enhancedAgentName = '';

  /** 外部供应商收费模板 */
  exFeeTemplt = '';

  /** 外部供应商账单id */
  exFeeTempltId = '';

  /** exQuotationFeeList */
  exQuotationFeeList = [];

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  feeMonth = '';

  /** 收费模板名称 */
  feeTemplt = '';

  /** 收费模板id */
  feeTempltId = '';

  /** 档案柜编号 */
  fileCabCode = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 文件夹编号 */
  folderCode = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否关联订单 */
  hasAssociations = undefined;

  /** 入职竞争对手 */
  hireCompetitor = '';

  /** 入职时间 */
  hireDt = '';

  /** 入职时间止 */
  hireEndDt = '';

  /** 入职报价单 */
  hireQuotationId = '';

  /** 入职备注 */
  hireRemark = '';

  /** hireSepTempAppId */
  hireSepTempAppId = undefined;

  /** 入职时间起 */
  hireStartDt = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 接单客服name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 内外部类型(1内部2外部3全部) */
  innerType = '';

  /** 是否需要签订劳动合同 */
  isArchive = '';

  /** 是否归档名称 */
  isArchiveName = '';

  /** 银行卡是否上传 */
  isBankCardUpload = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否入职呼叫 */
  isHireCall = '';

  /** 是否入职呼叫名称 */
  isHireCallName = '';

  /** 身份证是否上传 */
  isIDCardUpload = '';

  /** 是否单立户1 是0 否 */
  isIndependent = '';

  /** 劳动合同是否上传 */
  isLaborContractUpload = '';

  /** 是否需要签订劳动合同 */
  isNeedSign = '';

  /** 是否退费 0否  1是 */
  isRefund = '';

  /** isRelated */
  isRelated = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保中文 */
  isSameInsurName = '';

  /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
  isSepCall = '';

  /** 是否离职呼叫名 */
  isSepCallName = '';

  /** 是否有统筹医疗 */
  isThereACoordinateHealth = '';

  /** 是否有统筹医疗名称 */
  isThereACoordinateHealthText = '';

  /** 是否有社保卡 */
  isThereSsCard = '';

  /** 是否有社保卡名称 */
  isThereSsCardText = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 离职动态模板json */
  jsonStr = [];

  /** 劳动关系单位 */
  laborRelationUnit = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 材料列表 */
  materialList = [];

  /** materialSignStatus */
  materialSignStatus = undefined;

  /** materialSignStatusName */
  materialSignStatusName = '';

  /** 离职材料电子版本id */
  materialSpId = '';

  /** materialStatusName */
  materialStatusName = '';

  /** 模拟人 */
  mimicBy = '';

  /** 操作方式  单立户1、大户2 */
  modeOfOperation = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** 后指针 */
  nextPointer = '';

  /** noChange */
  noChange = false;

  /** 非社保列表 */
  nonSsGroupList = [];

  /** 银行名称 */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 挂起原因 */
  pendingReason = '';

  /** 挂起原因中文 */
  pendingReasonName = '';

  /** 人员分类id */
  personCategoryId = '';

  /** 职位id */
  positionId = '';

  /** 前指针 */
  prevPointer = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程实例id */
  processInsId = '';

  /** 供应商编码 */
  providerCode = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商客服id */
  providerCsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** 供应商集团id */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 供应商集团 */
  prvdGroupName = '';

  /** 离职材料签订形式 */
  quitSignType = undefined;

  /** quitSignTypeName */
  quitSignTypeName = '';

  /** 电子离职合同任务主键 */
  quitTaskId = undefined;

  /** 报价单编码 */
  quotationCode = '';

  /** 报价单名称 */
  quotationName = '';

  /** 减少详细原因 */
  reduceDetailReason = '';

  /** 减原详细原因名称 */
  reduceDetailReasonName = '';

  /** 客户端减员ID */
  reduceId = '';

  /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
  reduceReason = '';

  /** 减员原因名称 */
  reduceReasonName = '';

  /** 参考日期，页面传入 */
  referDate = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** 报入职人员id */
  rptHireBy = '';

  /** 报入职人 */
  rptHireByName = '';

  /** 报入职时间 */
  rptHireDt = '';

  /** 报入职日期止 */
  rptHireEndDt = '';

  /** 报入职日期起 */
  rptHireStartDt = '';

  /** 报离职人员id */
  rptSepBy = '';

  /** 报离职人 */
  rptSepByName = '';

  /** 报离职日期 */
  rptSepDt = '';

  /** 报离职日期止 */
  rptSepEndDt = '';

  /** 报离职日期起 */
  rptSepStartDt = '';

  /** 用章对象 */
  sealObject = '';

  /** 用章类型 */
  sealType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职确认人 */
  sepConfirmBy = '';

  /** 离职确认日期 */
  sepConfirmDate = '';

  /** 离职确认历史 */
  sepConfirmHis = '';

  /** 离职确认进程 */
  sepConfirmPro = '';

  /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
  sepConfirmStatus = '';

  /** 离职状态名称 */
  sepConfirmStatusName = '';

  /** 离职详细原因 */
  sepDetailReason = '';

  /** 离职详细原因名称 */
  sepDetailReasonName = '';

  /** 离职日期 */
  sepDt = '';

  /** 离职时间止 */
  sepEndDt = '';

  /** 离职接单确认人 */
  sepPerfectBy = '';

  /** 离职接单确认时间 */
  sepPerfectDate = '';

  /** 离职手续办理状态:0  未完成   1  完成 */
  sepProcessStatus = '';

  /** 离职报价单 */
  sepQuotationId = '';

  /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
  sepReason = '';

  /** 离职原因key */
  sepReasonKey = '';

  /** 离职原因名称 */
  sepReasonName = '';

  /** 离职备注 */
  sepRemark = '';

  /** 离职时间止 */
  sepStartDt = '';

  /** 离职导出类型:1离职接单确认,2离职派单确认 */
  sepType = '';

  /** 签约方分公司抬头 */
  signBranchTitle = '';

  /** 签约方分公司抬头id */
  signBranchTitleId = '';

  /** 签约方分公司抬头name */
  signBranchTitleName = '';

  /** 签单供应商 */
  signProvider = '';

  /** 签单方 */
  signProviderId = '';

  /** signStatus */
  signStatus = undefined;

  /** 短信发送日期 */
  smsSendDt = '';

  /** 短信发送状态: 0未发送, 1成功, 2失败 */
  smsSendStatus = '';

  /** 短信发送状态中文: 未发送, 成功, 失败 */
  smsSendStatusStr = '';

  /** 分拆方分公司:分拆方客服 */
  splitServiceProviderCs = '';

  /** 社保列表 */
  ssGroupList = [];

  /** 员工社保参与地 */
  ssParticipateLocation = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 1入职未生效2在职3离职 */
  status = '';

  /** 状态名称 */
  statusName = '';

  /** 小类名称 */
  subTypeName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 大类名称 */
  superTypeName = '';

  /** 总收费日期 */
  totalFeeDt = '';

  /** eos转移id */
  transferId = undefined;

  /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
  type = undefined;

  /** 机动分类项目1 */
  type1 = '';

  /** 机动分类项目2 */
  type2 = '';

  /** 机动分类项目3 */
  type3 = '';

  /** 机动分类项目4 */
  type4 = '';

  /** 机动分类项目5 */
  type5 = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class EmpHireSyncResult {
  /** add */
  add = false;

  /** batchId */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_HIRE_SYNC_RESULT.EMP_HIRE_SYNC_RESULT_ID	  	  ibatorgenerated Tue Apr 28 09:14:22 CST 2015 */
  empHireSyncResultId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_HIRE_SYNC_RESULT.EMP_NAME	  	  ibatorgenerated Tue Apr 28 09:14:22 CST 2015 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_HIRE_SYNC_RESULT.FAILURE_REASON	  	  ibatorgenerated Tue Apr 28 09:14:22 CST 2015 */
  failureReason = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_HIRE_SYNC_RESULT.ID_CARD_NUM	  	  ibatorgenerated Tue Apr 28 09:14:22 CST 2015 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_HIRE_SYNC_RESULT.STATUS	  	  ibatorgenerated Tue Apr 28 09:14:22 CST 2015 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmpSMSRecord {
  /** add */
  add = false;

  /** applyBy */
  applyBy = '';

  /** applyDt */
  applyDt = '';

  /** 申请日期止 */
  applyEndDt = '';

  /** 申请发送日期 */
  applySendDate = '';

  /** 申请发送日期止 */
  applySendEndDate = '';

  /** 申请发送日期起 */
  applySendStartDate = '';

  /** 申请日期起 */
  applyStartDt = '';

  /** 提交批次号 */
  approvelBatchId = '';

  /** approvelDt */
  approvelDt = '';

  /** 审批时间止 */
  approvelEndDt = '';

  /** approvelOpinion */
  approvelOpinion = '';

  /** 审批时间起 */
  approvelStartDt = '';

  /** 审批状态 */
  approvelStatus = '';

  /** approvelStatusName */
  approvelStatusName = '';

  /** approver */
  approver = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 手机号 */
  cellPhoneNum = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建者 */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** 创建日期止 */
  createEndDt = '';

  /** 创建日期起 */
  createStartDt = '';

  /** 创建者 */
  creater = '';

  /** 客户id */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户id */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 主键 */
  hesrId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isSend */
  isSend = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程实例id */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 实际发送时间 */
  sendEndTime = '';

  /** 实际发送时间 */
  sendStartTime = '';

  /** sendStatus */
  sendStatus = '';

  /** 实际发送时间 */
  sendTime = '';

  /** 短信内容 */
  smsText = '';

  /** 短信类型:(select  from bd_base_data where type=926) */
  smsType = '';

  /** smsTypeName */
  smsTypeName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmpSMSRecordQuery {
  /** 申请人 */
  applyBy = '';

  /** 申请日期 */
  applyDt = '';

  /** 申请日期止 */
  applyEndDt = '';

  /** 申请发送日期 */
  applySendDate = '';

  /** 申请发送日期止 */
  applySendEndDate = '';

  /** 申请发送日期起 */
  applySendStartDate = '';

  /** 申请日期起 */
  applyStartDt = '';

  /** 提交批次号 */
  approvelBatchId = '';

  /** 审批时间 */
  approvelDt = '';

  /** 审批时间止 */
  approvelEndDt = '';

  /** 审批时间起 */
  approvelStartDt = '';

  /** 审批状态 */
  approvelStatus = '';

  /** 手机号 */
  cellPhoneNum = '';

  /** 创建日期 */
  createDt = '';

  /** 创建日期止 */
  createEndDt = '';

  /** 创建日期起 */
  createStartDt = '';

  /** 创建者 */
  creater = '';

  /** 客户id */
  custId = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 主键 */
  hesrId = '';

  /** 是否有效 */
  isDeleted = '';

  /** isSend */
  isSend = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** 流程实例id */
  processInsId = '';

  /** 实际发送时间 */
  sendEndTime = '';

  /** 实际发送时间 */
  sendStartTime = '';

  /** 实际发送时间 */
  sendTime = '';

  /** 短信类型:(select  from bd_base_data where type=926) */
  smsType = '';

  /** startIndex */
  startIndex = undefined;
}

class EmployeeBaseInfo {
  /** accountEmployeeName */
  accountEmployeeName = '';

  /** add */
  add = false;

  /** 联系地址 */
  address = '';

  /** 年龄 */
  age = '';

  /** 养老金帐号 */
  annuityAccount = '';

  /** appover */
  appover = '';

  /** approveDT */
  approveDT = '';

  /** 小合同接单方客服id */
  assigneeProviderId = '';

  /** 小合同接单方客服Name */
  assigneeProviderName = '';

  /** 小合同派单方客服id */
  assignerProviderId = '';

  /** 小合同派单方客服Name */
  assignerProviderName = '';

  /** attachmentName */
  attachmentName = '';

  /** attachmentType */
  attachmentType = '';

  /** attachmentstatus */
  attachmentstatus = '';

  /** bankAcct */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 首次参加工作日期 */
  beginWorkDate = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** birthCountry */
  birthCountry = undefined;

  /** 生日 */
  birthday = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busiType */
  busiType = '';

  /** 子女情况： 1 无子女 2 独生子女 3 有子女但非独生子女。 */
  childrenStatus = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** condition */
  condition = undefined;

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 国家id */
  countryId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编码 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** dateOfIssue */
  dateOfIssue = '';

  /** del */
  del = false;

  /** disaCertValidUntil */
  disaCertValidUntil = '';

  /** disabilityLevel */
  disabilityLevel = '';

  /** 残疾类型 */
  disabilityType = '';

  /** disabledId */
  disabledId = '';

  /** disabledPeopleType */
  disabledPeopleType = '';

  /** 职务 */
  duty = '';

  /** 教育程度 */
  educationLevel = '';

  /** email */
  email = '';

  /** 紧急联系人 */
  emergencyContact = '';

  /** 紧急联系人电话 */
  emergencyPhone = '';

  /** empHireSepId */
  empHireSepId = '';

  /** empHisId */
  empHisId = '';

  /** 所在街道 */
  empStreet = '';

  /** empattachmentid */
  empattachmentid = '';

  /** 员工编号 */
  employeeCode = '';

  /** 员工id */
  employeeId = '';

  /** 员工姓名 */
  employeeName = '';

  /** 上岗状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** 民族 */
  ethnic = '';

  /** 查询使用排除的雇员id */
  exceptEmpId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 家属劳保卡号 */
  familyLaborCardNo = '';

  /** fileCabCode */
  fileCabCode = '';

  /** files */
  files = undefined;

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** filterNotPersonalOderEmp */
  filterNotPersonalOderEmp = '';

  /** folderCode */
  folderCode = '';

  /** 外语等级 */
  foreignLanguageLevel = '';

  /** 外语语种 */
  foreignLanguageType = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 性别 */
  gender = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 毕业日期 */
  graduationDt = '';

  /** guardianContact */
  guardianContact = '';

  /** guardianName */
  guardianName = '';

  /** 是否有不良记录 */
  haveBadnessRecord = '';

  /** 健康状况 */
  healthStatus = '';

  /** 入职日期 */
  hireDate = '';

  /** hukouAddress */
  hukouAddress = '';

  /** 户口类别 */
  hukouType = '';

  /** 户籍地址邮编 */
  hukouZipCode = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** 雇员证件类型名称 */
  idCardTypeName = '';

  /** 15位证件号码 */
  idc15 = '';

  /** 18位证件号码 */
  idc18 = '';

  /** inId */
  inId = '';

  /** 初始化类别 */
  initType = '';

  /** 企业内部编号 */
  internalCode = '';

  /** isArchive */
  isArchive = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** 是否市级保健对象 */
  isCareObject = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isDisabled */
  isDisabled = undefined;

  /** 是否伤残军人 */
  isDisabledMilitary = '';

  /** 是否为集体户口 */
  isGroupResident = '';

  /** isLonelyOld */
  isLonelyOld = undefined;

  /** isMartyFamily */
  isMartyFamily = undefined;

  /** isSalaryImport */
  isSalaryImport = undefined;

  /** isWageQuery */
  isWageQuery = '';

  /** 参军日期 */
  joinMilitaryDate = '';

  /** 婚姻状况 */
  marriageStatus = '';

  /** martyFamilyId */
  martyFamilyId = '';

  /** 医疗卡 */
  medicalCardId = '';

  /** 医疗保障类别 */
  medicareType = '';

  /** 模拟人 */
  mimicBy = '';

  /** 兵役状况 */
  nationalServiceStatus = '';

  /** noChange */
  noChange = false;

  /** openBankName */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 密码 */
  password = '';

  /** 薪资档案id */
  payPsnId = '';

  /** 籍贯 */
  personalRegCity = '';

  /** 政治面貌 */
  politicalStatus = '';

  /** 职位 */
  position = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 职业工种发证日期 */
  professionAwardDate = '';

  /** 职业工种发证单位 */
  professionAwardUnit = '';

  /** 职业工种等级 */
  professionLevel = '';

  /** 职业工种名称 */
  professionName = '';

  /** 专业技术职称 */
  professionTitle = '';

  /** 公积金账号 */
  providentFundAccount = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 省份id */
  provinceId = '';

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 账单id */
  receivableTempltId = '';

  /** 雇员信息备注 */
  remark = '';

  /** 居住地址 */
  residentAddress = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 转业日期 */
  retireFromMilitaryDate = '';

  /** savepath */
  savepath = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职提起 */
  sepDate = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同id */
  subcontractId = '';

  /** subsidyAppStatus */
  subsidyAppStatus = '';

  /** 补充公积金账号 */
  supProvidentFundAccount = '';

  /** 职称发证日期 */
  techAwardDate = '';

  /** 职称发证单位 */
  techAwardUnit = '';

  /** 第二次导社保是否删除该雇员的社保，临时字段 */
  tmpDeleteSS = '';

  /** type */
  type = undefined;

  /** 记录修改身份证号时的信息 */
  upateIdcRemark = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** uploadDT */
  uploadDT = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 用户名 */
  userName = '';

  /** 参加工作时间 */
  workDt = '';

  /** 用工形式0:标准工时,1:综合共时,2:不定时 */
  workType = '';

  /** 邮政编码 */
  zipCode = '';
}

class EmployeeBaseInfoDTO {
  /** accountEmployeeName */
  accountEmployeeName = '';

  /** 年龄 */
  age = '';

  /** appover */
  appover = '';

  /** approveDT */
  approveDT = '';

  /** 小合同接单方客服id */
  assigneeProviderId = '';

  /** 小合同接单方客服Name */
  assigneeProviderName = '';

  /** 小合同派单方客服id */
  assignerProviderId = '';

  /** 小合同派单方客服Name */
  assignerProviderName = '';

  /** attachmentName */
  attachmentName = '';

  /** attachmentType */
  attachmentType = '';

  /** attachmentstatus */
  attachmentstatus = '';

  /** bankAcct */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** busiType */
  busiType = '';

  /** 子女情况： 1 无子女 2 独生子女 3 有子女但非独生子女。 */
  childrenStatus = '';

  /** cityName */
  cityName = '';

  /** condition */
  condition = undefined;

  /** 客户编码 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 职务 */
  duty = '';

  /** 紧急联系人 */
  emergencyContact = '';

  /** 紧急联系人电话 */
  emergencyPhone = '';

  /** empHireSepId */
  empHireSepId = '';

  /** empHisId */
  empHisId = '';

  /** 所在街道 */
  empStreet = '';

  /** empattachmentid */
  empattachmentid = '';

  /** 员工编号 */
  employeeCode = '';

  /** 员工id */
  employeeId = '';

  /** 员工姓名 */
  employeeName = '';

  /** fileCabCode */
  fileCabCode = '';

  /** files */
  files = undefined;

  /** filterNotPersonalOderEmp */
  filterNotPersonalOderEmp = '';

  /** folderCode */
  folderCode = '';

  /** 毕业日期 */
  graduationDt = '';

  /** hukouAddress */
  hukouAddress = '';

  /** 户籍地址邮编 */
  hukouZipCode = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** 雇员证件类型名称 */
  idCardTypeName = '';

  /** 15位证件号码 */
  idc15 = '';

  /** 18位证件号码 */
  idc18 = '';

  /** 企业内部编号 */
  internalCode = '';

  /** isArchive */
  isArchive = '';

  /** 是否为集体户口 */
  isGroupResident = '';

  /** openBankName */
  openBankName = '';

  /** 薪资档案id */
  payPsnId = '';

  /** 籍贯 */
  personalRegCity = '';

  /** 职位 */
  position = '';

  /** provinceName */
  provinceName = '';

  /** 账单id */
  receivableTempltId = '';

  /** 居住地址 */
  residentAddress = '';

  /** savepath */
  savepath = '';

  /** status */
  status = '';

  /** 小合同id */
  subcontractId = '';

  /** 第二次导社保是否删除该雇员的社保，临时字段 */
  tmpDeleteSS = '';

  /** type */
  type = undefined;

  /** 记录修改身份证号时的信息 */
  upateIdcRemark = '';

  /** uploadDT */
  uploadDT = '';

  /** 参加工作时间 */
  workDt = '';

  /** 用工形式0:标准工时,1:综合共时,2:不定时 */
  workType = '';
}

class EmployeeFee {
  /** add */
  add = false;

  /** 调整情况分类：1费用添加、2费用清空、3比例调整、4基数调整、5基数+比例调整、6其他调整 */
  adjSituType = '';

  /** 金额 */
  amount = undefined;

  /** 金额(不含税) */
  amtNoTax = '';

  /** 附加税费 */
  atr = '';

  /** 基数绑定级次 */
  baseBindingLevel = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单起始月 */
  billStartMonth = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 类型 */
  category = '';

  /** 收费结束时间 */
  chargeEndDate = '';

  /** 缴费频率 */
  chargeRate = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** cityId */
  cityId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 企业附加金额 */
  eAdditionalAmt = undefined;

  /** 企业金额 */
  eAmt = undefined;

  /** 企业基数 */
  eBase = undefined;

  /** eBillTemplt */
  eBillTemplt = '';

  /** 企业账单模板id */
  eBillTempltId = '';

  /** 企业计算方法:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
  eCalculationMethod = '';

  /** 企业收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  eFeeMonth = '';

  /** 收费模板名 */
  eFeeTemplt = '';

  /** 企业收费模板id */
  eFeeTempltId = '';

  /** 企业频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  eFrequency = '';

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业提前几个月收,默认为0，选项0-3 */
  eMonthInAdvance = '';

  /** 企业精确值0：0位小数1：1位小数2：2位小数5： 精确值 */
  ePrecision = '';

  /** 企业比例 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** 费用段历史id */
  empFeeHisId = '';

  /** 费用段id */
  empFeeId = '';

  /** 费用段操作id */
  empFeeOprId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 外部供应商账单起始月 */
  exBillStartMonth = '';

  /** 供应商收费月 */
  exFeeMonth = '';

  /** 供应商收费模板名 */
  exFeeTemplt = '';

  /** 外部供应商收费模板id */
  exFeeTempltId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否一次性付费 */
  isOneTimePay = '';

  /** 是否显示 1:是0:否 */
  isShow = '';

  /** 是否更新月度表1:是0:否 */
  isUptFeeMon = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 原金额 */
  oldAmount = '';

  /** 原账单起始月 */
  oldBillStartMonth = '';

  /** 个人附加金额 */
  pAdditionalAmt = undefined;

  /** 个人金额 */
  pAmt = undefined;

  /** 个人基数 */
  pBase = undefined;

  /** pBillTemplt */
  pBillTemplt = '';

  /** 个人部分账单模板id */
  pBillTempltId = '';

  /** 个人计算方式:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
  pCalculationMethod = '';

  /** 个人收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  pFeeMonth = '';

  /** 收费模板名 */
  pFeeTemplt = '';

  /** 个人收费模板id */
  pFeeTempltId = '';

  /** 个人频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  pFrequency = '';

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人提前几个月收,提前几个月收,默认为0，选项0-3 */
  pMonthInAdvance = '';

  /** 个人精度0：0位小数1：1位小数2：2位小数5： 精确值 */
  pPrecision = '';

  /** 个人比例 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** 支付频率1:月缴,2季度缴,3年缴(不足一年按年缴),4年缴(不足一年按月缴) */
  payFrequency = '';

  /** 支付最后服务年月 */
  payLastServiceMonth = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 产品比例id */
  productRatioId = '';

  /** 产品比例名称 */
  productRatioName = '';

  /** 产品类型id */
  productTypeId = undefined;

  /** 供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单id */
  quotationId = '';

  /** 报价单子项id */
  quotationItemId = '';

  /** 应收金额 */
  receivableAmt = undefined;

  /** 应收几个月 */
  receivableMonth = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保组id */
  ssGroupId = '';

  /** 社保组名称 */
  ssGroupName = '';

  /** 社保福利包id */
  ssWelfarePkgId = '';

  /** 社保福利包名称 */
  ssWelfarePkgName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 标签 */
  tag = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 增值税 */
  vat = '';

  /** 增值税率 */
  vatr = '';

  /** 实收金额 */
  verifyAmt = undefined;

  /** 实收金额(不含税) */
  verifyAmtNoTax = '';

  /** 实收金额增值税 */
  verifyAmtVat = '';
}

class EmployeeFeeMonth {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.AMOUNT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  amount = '';

  /** 金额(不含税) */
  amtNoTax = '';

  /** 附加税费 */
  atr = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.BILL_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 账单月止 */
  billEndMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.BILL_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 账单月起 */
  billMonth = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** chargeEndDate */
  chargeEndDate = '';

  /** chargeStartDate */
  chargeStartDate = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 大合同ID */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_ADDITIONAL_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eAdditionalAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_BASE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eBase = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_BILL_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eBillTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_FEE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eFeeMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_FEE_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eFeeTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_FREQUENCY	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eFrequency = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_MONTH_IN_ADVANCE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eMonthInAdvance = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_RATIO	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  eRatio = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_FEE_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empFeeId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_FEE_MONTH_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empFeeMonthId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_HIRE_SEP_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empHireSepId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  empId = '';

  /** 结束查询月份 */
  endMonth = '';

  /** exBillMonth */
  exBillMonth = '';

  /** 供应商账单起始月 */
  exBillStartMonth = '';

  /** exFeeTempltId */
  exFeeTempltId = '';

  /** 供应商收费模板Name */
  exFeeTempltName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** frequencyName */
  frequencyName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否是供应商费用信息月度信息 0,1 */
  isPdFlag = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.IS_VALID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 月份数 */
  monthNums = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_ADDITIONAL_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pAdditionalAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_BASE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pBase = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_BILL_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pBillTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_FEE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pFeeMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_FEE_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pFeeTempltId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_FREQUENCY	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pFrequency = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_MONTH_IN_ADVANCE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pMonthInAdvance = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_RATIO	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  pRatio = '';

  /** 供应商的报价单名称 */
  pdQuotationName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.PRODUCT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  productId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.PRODUCT_RATIO_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  productRatioId = '';

  /** productRatioName */
  productRatioName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.QUOTATION_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  quotationId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.QUOTATION_DETAIL_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  quotationItemId = '';

  /** quotationName */
  quotationName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.REMARK	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SERVICE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 收费月止 */
  serviceEndMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SERVICE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 收费月起 */
  serviceMonth = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SS_GROUP_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  ssGroupId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SS_WELFARE_PKG_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
  ssWelfarePkgId = '';

  /** 开始查询月份 */
  startMonth = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同ID */
  subcontractId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 增值税 */
  vat = '';

  /** 增值税率 */
  vatr = '';
}

class EmployeeFeeMonthVO {
  /** 按钮查询状态 */
  buttonQueryStatus = '';

  /** 费用段月度 */
  employeeFeeMonth = new EmployeeFeeMonth();

  /** num */
  num = undefined;

  /** 状态 */
  state = '';
}

class EmployeeHireSep {
  /** 开户人姓名 */
  accountEmployeeName = '';

  /** 实际工作地 */
  actualWorkLoc = '';

  /** add */
  add = false;

  /** 增员确认人 */
  addConfirmBy = '';

  /** 增员确认时间 */
  addConfirmDate = '';

  /** 增员过程 */
  addConfirmPro = '';

  /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
  addConfirmStatus = '';

  /** 增员状态名称 */
  addConfirmStatusName = '';

  /** 增员接单确认时间 */
  addPerfectBy = '';

  /** 增员接单确认人 */
  addPerfectDate = '';

  /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
  addReason = '';

  /** 增员备注 */
  addRemark = '';

  /** 年龄 */
  age = '';

  /** 变更确认人 */
  alterConfirmBy = '';

  /** 变更确认时间 */
  alterConfirmDate = '';

  /** 变更确认过程 */
  alterConfirmPro = '';

  /** 变更接单确认人 */
  alterPerfectBy = '';

  /** 变更接单确时间 */
  alterPerfectDate = '';

  /** 变更备注 */
  alterRemark = '';

  /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
  alterStatus = '';

  /** 变更状态名称 */
  alterStatusName = '';

  /** 大区类型 */
  areaType = '';

  /** 大区类型名称 */
  areaTypeName = '';

  /** 接单城市id */
  assigneeCityId = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 派单类型1 执行单2 协调单3 收集单 */
  assignmentType = '';

  /** 关联状态 */
  associationStatus = '';

  /** 银行卡号 */
  bankAcct = '';

  /** 银行卡更新人 */
  bankCardUpdateBy = '';

  /** 银行卡更新时间 */
  bankCardUpdateDt = '';

  /** baseInfo主键 */
  baseInfoId = '';

  /** 批次号,用于生成社保服务信息 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单模板id */
  billTempltId = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型 */
  category = '';

  /** 离职证明电子版本 */
  certificateSpId = '';

  /** certificateStatusName */
  certificateStatusName = '';

  /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
  changeMethod = '';

  /** 收费截至日期 */
  chargeEndDate = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 商保订单状态 */
  commInsurStatus = '';

  /** 商保订单状态name */
  commInsurStatusName = '';

  /** 确认备注 */
  confirmRemark = '';

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 法人单位id */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户方内部编号 */
  custInternalNum = '';

  /** 客户姓名 */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** 缴费实体 */
  custPayEntityName = '';

  /** 客户类型 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** 类型: 1,正常 2,大客户  */
  dataType = undefined;

  /** 申报工资 */
  decSalary = '';

  /** decSalaryRemark */
  decSalaryRemark = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** 客户端增员ID */
  empAddId = '';

  /** 客户端变更ID */
  empAlterId = '';

  /** feeId数组 */
  empFeeIdArray = '';

  /** 历史表主键 */
  empHireSepHisId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入职主记录ID */
  empHiresepMainId = '';

  /** 员工id */
  empId = '';

  /** 停缴id */
  empStopId = '';

  /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
  empStopProcessState = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empType = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empTypeId = undefined;

  /** 人员分类 */
  empTypeName = '';

  /** 唯一号 */
  employeeCode = '';

  /** 费用段列表 */
  employeeFeeList = [];

  /** 雇员姓名 */
  employeeName = '';

  /** 雇员状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** enhancedAgent */
  enhancedAgent = '';

  /** enhancedAgentName */
  enhancedAgentName = '';

  /** 外部供应商收费模板 */
  exFeeTemplt = '';

  /** 外部供应商账单id */
  exFeeTempltId = '';

  /** exQuotationFeeList */
  exQuotationFeeList = [];

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  feeMonth = '';

  /** 收费模板名称 */
  feeTemplt = '';

  /** 收费模板id */
  feeTempltId = '';

  /** 档案柜编号 */
  fileCabCode = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 文件夹编号 */
  folderCode = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否关联订单 */
  hasAssociations = undefined;

  /** 入职竞争对手 */
  hireCompetitor = '';

  /** 入职时间 */
  hireDt = '';

  /** 入职时间止 */
  hireEndDt = '';

  /** 入职报价单 */
  hireQuotationId = '';

  /** 入职备注 */
  hireRemark = '';

  /** 入职时间起 */
  hireStartDt = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 接单客服name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 内外部类型(1内部2外部3全部) */
  innerType = '';

  /** 是否需要实做 */
  isAddProcess = '';

  /** 增员是否需要实做 */
  isAddProcessName = '';

  /** 是否需要签订劳动合同 */
  isArchive = '';

  /** 是否归档名称 */
  isArchiveName = '';

  /** 银行卡是否上传 */
  isBankCardUpload = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否入职呼叫 */
  isHireCall = '';

  /** 是否入职呼叫名称 */
  isHireCallName = '';

  /** 身份证是否上传 */
  isIDCardUpload = '';

  /** 是否单立户1 是0 否 */
  isIndependent = '';

  /** 劳动合同是否上传 */
  isLaborContractUpload = '';

  /** 是否需要签订劳动合同 */
  isNeedSign = '';

  /** 是否需要实做 */
  isReduceProcess = '';

  /** 是否退费 0否  1是 */
  isRefund = '';

  /** isRelated */
  isRelated = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保中文 */
  isSameInsurName = '';

  /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
  isSepCall = '';

  /** 是否离职呼叫名 */
  isSepCallName = '';

  /** 是否有统筹医疗 */
  isThereACoordinateHealth = '';

  /** 是否有统筹医疗名称 */
  isThereACoordinateHealthText = '';

  /** 是否有社保卡 */
  isThereSsCard = '';

  /** 是否有社保卡名称 */
  isThereSsCardText = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 离职动态模板json */
  jsonStr = [];

  /** 劳动关系单位 */
  laborRelationUnit = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 材料列表 */
  materialList = [];

  /** materialSignStatus */
  materialSignStatus = undefined;

  /** materialSignStatusName */
  materialSignStatusName = '';

  /** 离职材料电子版本id */
  materialSpId = '';

  /** materialStatusName */
  materialStatusName = '';

  /** 模拟人 */
  mimicBy = '';

  /** 操作方式  单立户1、大户2 */
  modeOfOperation = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** 后指针 */
  nextPointer = '';

  /** noChange */
  noChange = false;

  /** 非社保列表 */
  nonSsGroupList = [];

  /** 银行名称 */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 挂起原因 */
  pendingReason = '';

  /** 挂起原因中文 */
  pendingReasonName = '';

  /** 人员分类id */
  personCategoryId = '';

  /** 职位id */
  positionId = '';

  /** 前指针 */
  prevPointer = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程实例化id */
  processInsId = '';

  /** 供应商编码 */
  providerCode = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商客服id */
  providerCsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** 供应商集团id */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 供应商集团 */
  prvdGroupName = '';

  /** 离职材料签订形式 */
  quitSignType = undefined;

  /** quitSignTypeName */
  quitSignTypeName = '';

  /** 电子离职合同任务主键 */
  quitTaskId = undefined;

  /** 报价单编码 */
  quotationCode = '';

  /** 报价单名称 */
  quotationName = '';

  /** 减少详细原因 */
  reduceDetailReason = '';

  /** 减原详细原因名称 */
  reduceDetailReasonName = '';

  /** 客户端减员ID */
  reduceId = '';

  /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
  reduceReason = '';

  /** 减员原因名称 */
  reduceReasonName = '';

  /** 参考日期，页面传入 */
  referDate = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** 报入职人员id */
  rptHireBy = '';

  /** 报入职人 */
  rptHireByName = '';

  /** 报入职时间 */
  rptHireDt = '';

  /** 报入职日期止 */
  rptHireEndDt = '';

  /** 报入职日期起 */
  rptHireStartDt = '';

  /** 报离职人员id */
  rptSepBy = '';

  /** 报离职人 */
  rptSepByName = '';

  /** 报离职日期 */
  rptSepDt = '';

  /** 报离职日期止 */
  rptSepEndDt = '';

  /** 报离职日期起 */
  rptSepStartDt = '';

  /** 用章对象 */
  sealObject = '';

  /** 用章类型 */
  sealType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职确认人 */
  sepConfirmBy = '';

  /** 离职确认日期 */
  sepConfirmDate = '';

  /** 离职确认历史 */
  sepConfirmHis = '';

  /** 离职确认进程 */
  sepConfirmPro = '';

  /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
  sepConfirmStatus = '';

  /** 离职状态名称 */
  sepConfirmStatusName = '';

  /** 离职详细原因 */
  sepDetailReason = '';

  /** 离职详细原因名称 */
  sepDetailReasonName = '';

  /** 离职日期 */
  sepDt = '';

  /** 离职时间止 */
  sepEndDt = '';

  /** 离职接单确认人 */
  sepPerfectBy = '';

  /** 离职接单确认时间 */
  sepPerfectDate = '';

  /** 离职手续办理状态:0  未完成   1  完成 */
  sepProcessStatus = '';

  /** 离职报价单 */
  sepQuotationId = '';

  /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
  sepReason = '';

  /** 离职原因key */
  sepReasonKey = '';

  /** 离职原因名称 */
  sepReasonName = '';

  /** 离职备注 */
  sepRemark = '';

  /** 离职时间止 */
  sepStartDt = '';

  /** 离职导出类型:1离职接单确认,2离职派单确认 */
  sepType = '';

  /** sigle */
  sigle = false;

  /** 签约方分公司抬头 */
  signBranchTitle = '';

  /** 签约方分公司抬头id */
  signBranchTitleId = '';

  /** 签约方分公司抬头name */
  signBranchTitleName = '';

  /** 签单供应商 */
  signProvider = '';

  /** 签单方 */
  signProviderId = '';

  /** signStatus */
  signStatus = undefined;

  /** 短信发送日期 */
  smsSendDt = '';

  /** 短信发送状态: 0未发送, 1成功, 2失败 */
  smsSendStatus = '';

  /** 短信发送状态中文: 未发送, 成功, 失败 */
  smsSendStatusStr = '';

  /** 分拆方分公司:分拆方客服 */
  splitServiceProviderCs = '';

  /** 社保列表 */
  ssGroupList = [];

  /** 员工社保参与地 */
  ssParticipateLocation = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 1入职未生效2在职3离职 */
  status = '';

  /** 状态名称 */
  statusName = '';

  /** 小类名称 */
  subTypeName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 大类名称 */
  superTypeName = '';

  /** 总收费日期 */
  totalFeeDt = '';

  /** eos转移id */
  transferId = undefined;

  /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
  type = undefined;

  /** 机动分类项目1 */
  type1 = '';

  /** 机动分类项目2 */
  type2 = '';

  /** 机动分类项目3 */
  type3 = '';

  /** 机动分类项目4 */
  type4 = '';

  /** 机动分类项目5 */
  type5 = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class EmployeeHireSepVO {
  /** 账单起始月 */
  billStartMonth = '';

  /** 计算方式 */
  calculateMethod = '';

  /** 收费开始月 */
  chargeStartDate = '';

  /** 商报确认状态 */
  commInsurStatus = '';

  /** 确认备注 */
  confirmRemark = '';

  /** 雇员信息,生成时才需要 */
  employeeBaseInfo = new EmployeeBaseInfo();

  /** 个人订单 */
  employeeHireSep = new EmployeeHireSep();

  /** 个人订单列表 */
  employeeHireSepList = [];

  /** 供应商账单起始月 */
  exBillStartMonth = '';

  /** 是否有劳动合同,生成时才需要 */
  flag = false;

  /** inputNextBillMonth */
  inputNextBillMonth = false;

  /** 是否新批次0不是新的批次,1是新的批次 */
  isNewBatch = undefined;

  /** 劳动合同,生成时才需要 */
  laborContract = new LaborContract();

  /** 非社保公积金列表 */
  nonSsGroupList = [];

  /** 报价单id */
  quotationId = '';

  /** 社保公积金列表 */
  ssGroupList = [];

  /** 类型，1:增员,2变更 */
  type = undefined;
}

class EmployeeRelative {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 员工id */
  empId = '';

  /** 员工亲属id */
  empRelativesId = '';

  /** 员工唯一号 */
  employeeCode = '';

  /** 员工姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否有效 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 生日 */
  relativeBirthday = '';

  /** 性别0 女  1 男 */
  relativeGender = '';

  /** 证件号码 */
  relativeIdCardNum = '';

  /** 证件类型 */
  relativeIdCardType = '';

  /** 亲属姓名 */
  relativeName = '';

  /** 雇员信息备注 */
  relativeRemark = '';

  /** 亲属类型1：配偶 2：子女 */
  relativeType = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmployeeTransfer {
  /** 开户人姓名 */
  accountEmployeeName = '';

  /** 实际工作地 */
  actualWorkLoc = '';

  /** add */
  add = false;

  /** 增员确认人 */
  addConfirmBy = '';

  /** 增员确认时间 */
  addConfirmDate = '';

  /** 增员过程 */
  addConfirmPro = '';

  /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
  addConfirmStatus = '';

  /** 增员状态名称 */
  addConfirmStatusName = '';

  /** 增员接单确认时间 */
  addPerfectBy = '';

  /** 增员接单确认人 */
  addPerfectDate = '';

  /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
  addReason = '';

  /** 增员备注 */
  addRemark = '';

  /** 年龄 */
  age = '';

  /** 变更确认人 */
  alterConfirmBy = '';

  /** 变更确认时间 */
  alterConfirmDate = '';

  /** 变更确认过程 */
  alterConfirmPro = '';

  /** 变更接单确认人 */
  alterPerfectBy = '';

  /** 变更接单确时间 */
  alterPerfectDate = '';

  /** 变更备注 */
  alterRemark = '';

  /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
  alterStatus = '';

  /** 变更状态名称 */
  alterStatusName = '';

  /** 大区类型 */
  areaType = '';

  /** 大区类型名称 */
  areaTypeName = '';

  /** 接单城市id */
  assigneeCityId = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 派单类型1 执行单2 协调单3 收集单 */
  assignmentType = '';

  /** 关联状态 */
  associationStatus = '';

  /** 银行卡号 */
  bankAcct = '';

  /** 银行卡更新人 */
  bankCardUpdateBy = '';

  /** 银行卡更新时间 */
  bankCardUpdateDt = '';

  /** baseInfo主键 */
  baseInfoId = '';

  /** 批次号,用于生成社保服务信息 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单模板id */
  billTempltId = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型 */
  category = '';

  /** 离职证明电子版本 */
  certificateSpId = '';

  /** certificateStatusName */
  certificateStatusName = '';

  /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
  changeMethod = '';

  /** 收费截至日期 */
  chargeEndDate = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 商保订单状态 */
  commInsurStatus = '';

  /** 商保订单状态name */
  commInsurStatusName = '';

  /** 确认备注 */
  confirmRemark = '';

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 法人单位id */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户方内部编号 */
  custInternalNum = '';

  /** 客户姓名 */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** 缴费实体 */
  custPayEntityName = '';

  /** 客户类型 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** 类型: 1,正常 2,大客户  */
  dataType = undefined;

  /** 申报工资 */
  decSalary = '';

  /** decSalaryRemark */
  decSalaryRemark = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** 客户端增员ID */
  empAddId = '';

  /** 客户端变更ID */
  empAlterId = '';

  /** feeId数组 */
  empFeeIdArray = '';

  /** 历史表主键 */
  empHireSepHisId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入职主记录ID */
  empHiresepMainId = '';

  /** 员工id */
  empId = '';

  /** 停缴id */
  empStopId = '';

  /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
  empStopProcessState = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empType = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empTypeId = undefined;

  /** 人员分类 */
  empTypeName = '';

  /** 唯一号 */
  employeeCode = '';

  /** 费用段列表 */
  employeeFeeList = [];

  /** 雇员姓名 */
  employeeName = '';

  /** 雇员状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** enhancedAgent */
  enhancedAgent = '';

  /** enhancedAgentName */
  enhancedAgentName = '';

  /** exBillStartMonth */
  exBillStartMonth = '';

  /** 外部供应商收费模板 */
  exFeeTemplt = '';

  /** exFeeTempltId */
  exFeeTempltId = '';

  /** exQuotationFeeList */
  exQuotationFeeList = [];

  /** exQuotationId */
  exQuotationId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  feeMonth = '';

  /** 收费模板名称 */
  feeTemplt = '';

  /** 收费模板id */
  feeTempltId = '';

  /** 档案柜编号 */
  fileCabCode = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** firstDayOfTrans */
  firstDayOfTrans = '';

  /** 文件夹编号 */
  folderCode = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否关联订单 */
  hasAssociations = undefined;

  /** 入职竞争对手 */
  hireCompetitor = '';

  /** 入职时间 */
  hireDt = '';

  /** 入职时间止 */
  hireEndDt = '';

  /** 入职报价单 */
  hireQuotationId = '';

  /** 入职备注 */
  hireRemark = '';

  /** 入职时间起 */
  hireStartDt = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 接单客服name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 内外部类型(1内部2外部3全部) */
  innerType = '';

  /** 是否需要实做 */
  isAddProcess = '';

  /** 增员是否需要实做 */
  isAddProcessName = '';

  /** 是否需要签订劳动合同 */
  isArchive = '';

  /** 是否归档名称 */
  isArchiveName = '';

  /** 银行卡是否上传 */
  isBankCardUpload = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否拷贝社保 */
  isCopySs = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否入职呼叫 */
  isHireCall = '';

  /** 是否入职呼叫名称 */
  isHireCallName = '';

  /** 身份证是否上传 */
  isIDCardUpload = '';

  /** 是否单立户1 是0 否 */
  isIndependent = '';

  /** 劳动合同是否上传 */
  isLaborContractUpload = '';

  /** 上家客户是否退费 */
  isLastCustRefund = '';

  /** 是否需要重新签署 */
  isNeedResign = '';

  /** 是否需要签订劳动合同 */
  isNeedSign = '';

  /** 是否需要实做 */
  isReduceProcess = '';

  /** 是否退费 0否  1是 */
  isRefund = '';

  /** isRelated */
  isRelated = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保中文 */
  isSameInsurName = '';

  /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
  isSepCall = '';

  /** 是否离职呼叫名 */
  isSepCallName = '';

  /** 是否有统筹医疗 */
  isThereACoordinateHealth = '';

  /** 是否有统筹医疗名称 */
  isThereACoordinateHealthText = '';

  /** 是否有社保卡 */
  isThereSsCard = '';

  /** 是否有社保卡名称 */
  isThereSsCardText = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 离职动态模板json */
  jsonStr = [];

  /** 劳动关系单位 */
  laborRelationUnit = '';

  /** lastDayOfPreviousMonth */
  lastDayOfPreviousMonth = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 材料列表 */
  materialList = [];

  /** materialSignStatus */
  materialSignStatus = undefined;

  /** materialSignStatusName */
  materialSignStatusName = '';

  /** 离职材料电子版本id */
  materialSpId = '';

  /** materialStatusName */
  materialStatusName = '';

  /** 模拟人 */
  mimicBy = '';

  /** 操作方式  单立户1、大户2 */
  modeOfOperation = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** newContractId */
  newContractId = '';

  /** newCustId */
  newCustId = '';

  /** newEmpHireSepId */
  newEmpHireSepId = '';

  /** newHireQuotationId */
  newHireQuotationId = '';

  /** newStatus */
  newStatus = '';

  /** newSubcontractId */
  newSubcontractId = '';

  /** 后指针 */
  nextPointer = '';

  /** noChange */
  noChange = false;

  /** 非社保列表 */
  nonSsGroupList = [];

  /** oldEmpHireSepId */
  oldEmpHireSepId = '';

  /** oldStatus */
  oldStatus = '';

  /** 银行名称 */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 挂起原因 */
  pendingReason = '';

  /** 挂起原因中文 */
  pendingReasonName = '';

  /** 人员分类id */
  personCategoryId = '';

  /** 职位id */
  positionId = '';

  /** 前指针 */
  prevPointer = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程实例化id */
  processInsId = '';

  /** providentFundBillTempltId */
  providentFundBillTempltId = '';

  /** providentFundExFeeTempltId */
  providentFundExFeeTempltId = '';

  /** providentFundExStartMonth */
  providentFundExStartMonth = '';

  /** providentFundFeeTempltId */
  providentFundFeeTempltId = '';

  /** providentFundStartMonth */
  providentFundStartMonth = '';

  /** 供应商编码 */
  providerCode = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商客服id */
  providerCsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** 供应商集团id */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 供应商集团 */
  prvdGroupName = '';

  /** 离职材料签订形式 */
  quitSignType = undefined;

  /** quitSignTypeName */
  quitSignTypeName = '';

  /** 电子离职合同任务主键 */
  quitTaskId = undefined;

  /** 报价单编码 */
  quotationCode = '';

  /** 报价单名称 */
  quotationName = '';

  /** 减少详细原因 */
  reduceDetailReason = '';

  /** 减原详细原因名称 */
  reduceDetailReasonName = '';

  /** 客户端减员ID */
  reduceId = '';

  /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
  reduceReason = '';

  /** 减员原因名称 */
  reduceReasonName = '';

  /** 参考日期，页面传入 */
  referDate = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** 报入职人员id */
  rptHireBy = '';

  /** 报入职人 */
  rptHireByName = '';

  /** 报入职时间 */
  rptHireDt = '';

  /** 报入职日期止 */
  rptHireEndDt = '';

  /** 报入职日期起 */
  rptHireStartDt = '';

  /** 报离职人员id */
  rptSepBy = '';

  /** 报离职人 */
  rptSepByName = '';

  /** 报离职日期 */
  rptSepDt = '';

  /** 报离职日期止 */
  rptSepEndDt = '';

  /** 报离职日期起 */
  rptSepStartDt = '';

  /** 用章对象 */
  sealObject = '';

  /** 用章类型 */
  sealType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职确认人 */
  sepConfirmBy = '';

  /** 离职确认日期 */
  sepConfirmDate = '';

  /** 离职确认历史 */
  sepConfirmHis = '';

  /** 离职确认进程 */
  sepConfirmPro = '';

  /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
  sepConfirmStatus = '';

  /** 离职状态名称 */
  sepConfirmStatusName = '';

  /** 离职详细原因 */
  sepDetailReason = '';

  /** 离职详细原因名称 */
  sepDetailReasonName = '';

  /** 离职日期 */
  sepDt = '';

  /** 离职时间止 */
  sepEndDt = '';

  /** 离职接单确认人 */
  sepPerfectBy = '';

  /** 离职接单确认时间 */
  sepPerfectDate = '';

  /** 离职手续办理状态:0  未完成   1  完成 */
  sepProcessStatus = '';

  /** 离职报价单 */
  sepQuotationId = '';

  /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
  sepReason = '';

  /** 离职原因key */
  sepReasonKey = '';

  /** 离职原因名称 */
  sepReasonName = '';

  /** 离职备注 */
  sepRemark = '';

  /** 离职时间止 */
  sepStartDt = '';

  /** 离职导出类型:1离职接单确认,2离职派单确认 */
  sepType = '';

  /** sigle */
  sigle = false;

  /** 签约方分公司抬头 */
  signBranchTitle = '';

  /** 签约方分公司抬头id */
  signBranchTitleId = '';

  /** 签约方分公司抬头name */
  signBranchTitleName = '';

  /** 签单供应商 */
  signProvider = '';

  /** 签单方 */
  signProviderId = '';

  /** signStatus */
  signStatus = undefined;

  /** 短信发送日期 */
  smsSendDt = '';

  /** 短信发送状态: 0未发送, 1成功, 2失败 */
  smsSendStatus = '';

  /** 短信发送状态中文: 未发送, 成功, 失败 */
  smsSendStatusStr = '';

  /** socialSecurityBillTempltId */
  socialSecurityBillTempltId = '';

  /** socialSecurityExFeeTempltId */
  socialSecurityExFeeTempltId = '';

  /** socialSecurityExStartMonth */
  socialSecurityExStartMonth = '';

  /** socialSecurityFeeTempltId */
  socialSecurityFeeTempltId = '';

  /** socialSecurityStartMonth */
  socialSecurityStartMonth = '';

  /** 分拆方分公司:分拆方客服 */
  splitServiceProviderCs = '';

  /** 社保列表 */
  ssGroupList = [];

  /** 员工社保参与地 */
  ssParticipateLocation = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 1入职未生效2在职3离职 */
  status = '';

  /** 状态名称 */
  statusName = '';

  /** 小类名称 */
  subTypeName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 大类名称 */
  superTypeName = '';

  /** 总收费日期 */
  totalFeeDt = '';

  /** 转移生效月 */
  transferEffectiveMonth = '';

  /** eos转移id */
  transferId = undefined;

  /** 转移列表 */
  transferList = [];

  /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
  type = undefined;

  /** 机动分类项目1 */
  type1 = '';

  /** 机动分类项目2 */
  type2 = '';

  /** 机动分类项目3 */
  type3 = '';

  /** 机动分类项目4 */
  type4 = '';

  /** 机动分类项目5 */
  type5 = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class EmployeeTransferResult {
  /** add */
  add = false;

  /** 批次号 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 小合同创建日期结束 */
  createEndDt = '';

  /** 小合同创建日期开始 */
  createStartDt = '';

  /** 客户编码 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 雇员编码 */
  empCode = '';

  /** 雇员id */
  empId = '';

  /** 雇员姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 失败原因 */
  failureReason = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 转移结果id */
  hsEmpTransResultId = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否复制社保 */
  isCopySs = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否上家客户退费 */
  isLastCustRefund = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 新小合同id */
  newSubcontractId = '';

  /** noChange */
  noChange = false;

  /** 原小合同id */
  oldSubcontractId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 转移结果 1成功0失败 */
  transResult = '';

  /** 转移结果Name */
  transResultName = '';

  /** 转移生效日 */
  transferEffectiveMonth = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class FilterEntity {
  /** accountEmployeeName */
  accountEmployeeName = '';

  /** add */
  add = false;

  /** 增员状态 */
  addConfirmStatus = '';

  /** 年龄 */
  age = '';

  /** appover */
  appover = '';

  /** approveDT */
  approveDT = '';

  /** 小合同接单方客服id */
  assigneeProviderId = '';

  /** 小合同接单方客服Name */
  assigneeProviderName = '';

  /** 小合同派单方客服id */
  assignerProviderId = '';

  /** 小合同派单方客服Name */
  assignerProviderName = '';

  /** attachmentName */
  attachmentName = '';

  /** attachmentType */
  attachmentType = '';

  /** attachmentstatus */
  attachmentstatus = '';

  /** 银行卡号 */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 生日月份 */
  birthMon = '';

  /** 生日年份 */
  birthYear = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busiType */
  busiType = '';

  /** 子女情况： 1 无子女 2 独生子女 3 有子女但非独生子女。 */
  childrenStatus = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** condition */
  condition = undefined;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 员工类型 */
  contractType = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编码 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 1大型，2中型，3小型 */
  customerSize = '';

  /** del */
  del = false;

  /** 职务 */
  duty = '';

  /** 紧急联系人 */
  emergencyContact = '';

  /** 紧急联系人电话 */
  emergencyPhone = '';

  /** empHireSepId */
  empHireSepId = '';

  /** empHisId */
  empHisId = '';

  /** 所在街道 */
  empStreet = '';

  /** empattachmentid */
  empattachmentid = '';

  /** 唯一号 */
  employeeCode = '';

  /** employeeId */
  employeeId = '';

  /** 姓名 */
  employeeName = '';

  /** 雇员状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileCabCode */
  fileCabCode = '';

  /** files */
  files = undefined;

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** filterNotPersonalOderEmp */
  filterNotPersonalOderEmp = '';

  /** folderCode */
  folderCode = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 毕业日期 */
  graduationDt = '';

  /** 入职时间起 */
  hireDt = '';

  /** 入职时间止 */
  hireEndDt = '';

  /** hukouAddress */
  hukouAddress = '';

  /** 户籍地址邮编 */
  hukouZipCode = '';

  /** 证件号码 */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** 雇员证件类型名称 */
  idCardTypeName = '';

  /** 15位证件号码 */
  idc15 = '';

  /** 18位证件号码 */
  idc18 = '';

  /** inId */
  inId = '';

  /** 企业内部编号 */
  internalCode = '';

  /** 是否标识 */
  isArchive = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否为集体户口 */
  isGroupResident = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** openBankName */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 薪资档案id */
  payPsnId = '';

  /** 籍贯 */
  personalRegCity = '';

  /** 职位 */
  position = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 账单id */
  receivableTempltId = '';

  /** 居住地址 */
  residentAddress = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** savepath */
  savepath = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职时间起 */
  sepDt = '';

  /** 离职时间止 */
  sepEndDt = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同id */
  subcontractId = '';

  /** 第二次导社保是否删除该雇员的社保，临时字段 */
  tmpDeleteSS = '';

  /** type */
  type = undefined;

  /** 记录修改身份证号时的信息 */
  upateIdcRemark = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** uploadDT */
  uploadDT = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 参加工作时间 */
  workDt = '';

  /** 用工形式0:标准工时,1:综合共时,2:不定时 */
  workType = '';
}

class HireFromAppQuery {
  /** 关联状态 */
  associationStatus = '';

  /** 手机号码 */
  cellphone = '';

  /** 城市id */
  cityId = '';

  /** 提交结束日期 */
  commitEndDt = '';

  /** 提交开始日期 */
  commitStartDt = '';

  /** 客户名称 */
  custName = '';

  /** 雇员姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 证件号码 */
  idCardNum = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';
}

class HireMaterialActreceive {
  /** add */
  add = false;

  /** approveOpinion */
  approveOpinion = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** categoryInfoId */
  categoryInfoId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** forwardTime */
  forwardTime = '';

  /** forwarder */
  forwarder = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** materialCollectDt */
  materialCollectDt = '';

  /** materialCollector */
  materialCollector = '';

  /** materialId */
  materialId = '';

  /** materialName */
  materialName = '';

  /** materialReceivedId */
  materialReceivedId = '';

  /** materialRequiredId */
  materialRequiredId = '';

  /** 模拟人 */
  mimicBy = '';

  /** newremark */
  newremark = '';

  /** noChange */
  noChange = false;

  /** paraData */
  paraData = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processInsId */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商名称 */
  providerName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 服务名称 */
  svcName = '';

  /** transBy */
  transBy = '';

  /** type */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workItemId */
  workItemId = '';
}

class HireMaterialReceive {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.CATEGORY_INFO_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  categoryInfoId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custId */
  custId = '';

  /** custMaterialId */
  custMaterialId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hasReceived */
  hasReceived = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.IS_CUST           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  isCust = undefined;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialId = undefined;

  /** materialName */
  materialName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.MATERIAL_REQUIRED_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialRequiredId = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** providerId */
  providerId = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商名称 */
  providerName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** received */
  received = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceTransactionId */
  serviceTransactionId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.STATE           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  state = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** svcId */
  svcId = undefined;

  /** 服务名称 */
  svcName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_MATERIAL_RECEIVE.TYPE           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  type = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HireMaterialReceiveHis {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.CATEGORY_INFO_ID	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  categoryInfoId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.IS_CUST	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  isCust = undefined;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.MATERIAL_ID	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  materialId = undefined;

  /** materialName */
  materialName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  HS_HIRE_MATERIAL_RECEIVE_HIS.MATERIAL_REQUIRED_HIS_ID	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  materialRequiredHisId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.MATERIAL_REQUIRED_ID	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  materialRequiredId = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** providerId */
  providerId = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.REMARK	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceTransactionId */
  serviceTransactionId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.STATE	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  state = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** svcId */
  svcId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_HIRE_MATERIAL_RECEIVE_HIS.TYPE	  	  ibatorgenerated Wed Nov 30 13:28:26 CST 2011 */
  type = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HireMaterialSubmit {
  /** empHireSepId */
  empHireSepId = '';

  /** inList */
  inList = [];
}

class HireServiceTransaction {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.CATEGORY_INFO_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  categoryInfoId = undefined;

  /** 子列表 */
  children = [];

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.PROCESS_DEPARTMENT_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  processDepartmentId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.PROCESS_DT           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  processDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.PROCESSOR_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  processorId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.PROVIDER_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  providerId = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerName */
  providerName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.SERVICE_TRANSACTION_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  serviceTransactionId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.STATUS           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  status = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_HIRE_SERVICE_TRANSACTION.SVC_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  svcId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HiresepImgInfo {
  /** absPath */
  absPath = '';

  /** add */
  add = false;

  /** attachmentName */
  attachmentName = '';

  /** batchId */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empHiresepImageId */
  empHiresepImageId = '';

  /** empHiresepMainId */
  empHiresepMainId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** imageName */
  imageName = '';

  /** imageType */
  imageType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isMain */
  isMain = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** originalId */
  originalId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** relPath */
  relPath = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** showType */
  showType = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class HiresepMain {
  /** accountId */
  accountId = '';

  /** add */
  add = false;

  /** associationStatus */
  associationStatus = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** beginWorkDate */
  beginWorkDate = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** birthday */
  birthday = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** city */
  city = '';

  /** cityId */
  cityId = '';

  /** 低代码对应表单数据ID */
  clcDataId = '';

  /** 低代码对应模板ID */
  clcTemplateId = '';

  /** 低代码对应指定版本模板ID */
  clcTemplateJsonId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 档案所在地 */
  documentLocation = '';

  /** educationLevel */
  educationLevel = '';

  /** educationLevelStr */
  educationLevelStr = '';

  /** email */
  email = '';

  /** 紧急联系人 */
  emergencyContact = '';

  /** 紧急联系人电话 */
  emergencyPhone = '';

  /** empCode */
  empCode = '';

  /** empHireSepId */
  empHireSepId = '';

  /** empHiresepMainId */
  empHiresepMainId = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** 人员类别 */
  empType = '';

  /** 人员类别名称 */
  empTypeName = '';

  /** empolymentCertCode */
  empolymentCertCode = '';

  /** ethnic */
  ethnic = '';

  /** ethnicStr */
  ethnicStr = '';

  /** eventCode */
  eventCode = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 拼接好的地址 */
  fullAddress = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** gender */
  gender = '';

  /** genderStr */
  genderStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hasEmpolymentCert */
  hasEmpolymentCert = '';

  /** hasFinanceSs */
  hasFinanceSs = '';

  /** hasNanjingHealthIns */
  hasNanjingHealthIns = '';

  /** hasPayLargeAmt */
  hasPayLargeAmt = '';

  /** hasRetireBook */
  hasRetireBook = '';

  /** hasSsCard */
  hasSsCard = '';

  /** hasUnemployBenifit */
  hasUnemployBenifit = '';

  /** healthStatus */
  healthStatus = '';

  /** healthStatusStr */
  healthStatusStr = '';

  /** hospital1Id */
  hospital1Id = '';

  /** hospital2Id */
  hospital2Id = '';

  /** hospital3Id */
  hospital3Id = '';

  /** hospital4Id */
  hospital4Id = '';

  /** hospital5Id */
  hospital5Id = '';

  /** 户口地址 */
  hukouAddress = '';

  /** 户口所在市id */
  hukouCityId = '';

  /** 户口所在市名 */
  hukouCityName = '';

  /** 户口所在省id */
  hukouProvinceId = '';

  /** 户口所在省名 */
  hukouProvinceName = '';

  /** 户口性质 */
  hukouType = '';

  /** 户口性质中文 */
  hukouTypeName = '';

  /** 户口邮编 */
  hukouZipCode = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardValidEnd */
  idCardValidEnd = '';

  /** idCardValidStart */
  idCardValidStart = '';

  /** inId */
  inId = '';

  /** isAgricultural */
  isAgricultural = '';

  /** isAgriculturalMedical */
  isAgriculturalMedical = '';

  /** isBankPaySs */
  isBankPaySs = '';

  /** isBeijingPaid */
  isBeijingPaid = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** isChangchunBabyHealth */
  isChangchunBabyHealth = '';

  /** isChangchunHealth */
  isChangchunHealth = '';

  /** isChangchunStopSs */
  isChangchunStopSs = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isFirstSecurity */
  isFirstSecurity = '';

  /** isFundLoans */
  isFundLoans = '';

  /** isGraduatedPeriod */
  isGraduatedPeriod = '';

  /** 是否为工伤期、医疗期、孕期、产期、哺乳期，0：否 1：是 */
  isInjuryPeriod = '';

  /** isLegalPerson */
  isLegalPerson = '';

  /** isMakeupStopSs */
  isMakeupStopSs = '';

  /** 是否医疗期 */
  isMedicalPeriod = '';

  /** isMovetoShenzhen */
  isMovetoShenzhen = '';

  /** 近期是否有公积金贷款需求 */
  isNeedFundLoan = '';

  /** 是否孕期、产期、哺乳期 */
  isPregnancyPeriod = '';

  /** isTaiyuanProvidentFund */
  isTaiyuanProvidentFund = '';

  /** isTaiyuanSocialSecurity */
  isTaiyuanSocialSecurity = '';

  /** isTransfered30 */
  isTransfered30 = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 是否工伤期 */
  isWorkInjuryPeriod = '';

  /** jsonStr */
  jsonStr = '';

  /** longtermWorkplace */
  longtermWorkplace = '';

  /** marriageStatus */
  marriageStatus = '';

  /** 婚姻状况中文 */
  marriageStatusName = '';

  /** 模拟人 */
  mimicBy = '';

  /** mobilePhoneNum */
  mobilePhoneNum = '';

  /** needTransferCtg */
  needTransferCtg = '';

  /** noChange */
  noChange = false;

  /** oriDocPlace */
  oriDocPlace = '';

  /** pfAccount */
  pfAccount = '';

  /** pfNo12 */
  pfNo12 = '';

  /** pfSituation */
  pfSituation = '';

  /** phoneNum */
  phoneNum = '';

  /** politicalStatus */
  politicalStatus = '';

  /** politicalStatusStr */
  politicalStatusStr = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** rejectReason */
  rejectReason = '';

  /** residentAddress */
  residentAddress = '';

  /** residentCityId */
  residentCityId = '';

  /** residentProvinceId */
  residentProvinceId = '';

  /** residentZipCode */
  residentZipCode = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** retireBookExpiration */
  retireBookExpiration = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 是否单立户 */
  sfDlh = '';

  /** 数据来源 0：微信 1：HRO */
  src = undefined;

  /** 数据来源中文 0：微信 1：HRO */
  srcStr = '';

  /** ssNumber */
  ssNumber = '';

  /** ssSituation */
  ssSituation = '';

  /** ssStatus */
  ssStatus = '';

  /** status */
  status = '';

  /** statusStr */
  statusStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 修改人 */
  updateBy = '';

  /** updateByName */
  updateByName = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HiresepWechatField {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fieldKey */
  fieldKey = '';

  /** fieldLabel */
  fieldLabel = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** wechatFieldId */
  wechatFieldId = '';
}

class HsCcJob {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** callResult */
  callResult = '';

  /** callTaskId */
  callTaskId = undefined;

  /** callType */
  callType = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custId */
  custId = undefined;

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** distributeTime */
  distributeTime = '';

  /** distributer */
  distributer = '';

  /** emergencyLevel */
  emergencyLevel = undefined;

  /** empHireSepId */
  empHireSepId = undefined;

  /** empId */
  empId = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDeleted */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** isWorking */
  isWorking = '';

  /** laborContractId */
  laborContractId = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** noticeType */
  noticeType = '';

  /** otherCallRemark */
  otherCallRemark = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processEndDt */
  processEndDt = '';

  /** processLoc */
  processLoc = '';

  /** processStartDt */
  processStartDt = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** reMark */
  reMark = '';

  /** receiver */
  receiver = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startTime */
  startTime = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** taskStartCsId */
  taskStartCsId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HsCcMaterial {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** callTasId */
  callTasId = undefined;

  /** ccMaterialId */
  ccMaterialId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** materialId */
  materialId = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** subtypeId */
  subtypeId = undefined;

  /** typeHs */
  typeHs = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HsCcStandardLanguage {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** callSubtype */
  callSubtype = undefined;

  /** callinType */
  callinType = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isDeleted */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** standardLanguage */
  standardLanguage = '';

  /** standardLanguageId */
  standardLanguageId = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class HsCcStatus {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** callResult */
  callResult = '';

  /** callStatusId */
  callStatusId = undefined;

  /** callTaskId */
  callTaskId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empIsHandle */
  empIsHandle = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** isConnect */
  isConnect = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** noticeDt */
  noticeDt = '';

  /** noticeStatus */
  noticeStatus = '';

  /** notifier */
  notifier = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realName */
  realName = '';

  /** rejectionReason */
  rejectionReason = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImgDetail {
  /** add */
  add = false;

  /** 重庆市大足县龙岗街道双塔路112号2单元7-1 */
  address = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 1990年2月26日 */
  birthday = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** cropped_image */
  cropped_image = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 处理花费时间 */
  duration = undefined;

  /** empHiresepImageId */
  empHiresepImageId = '';

  /** empHiresepMainId */
  empHiresepMainId = '';

  /** empHiresepScanId */
  empHiresepScanId = '';

  /** 错误原因 */
  errorMsg = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 500225199002260015 */
  id_number = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否成功 1:是 0:否 */
  isSuc = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 郭天亮 */
  name = '';

  /** noChange */
  noChange = false;

  /** 原图路径 */
  oriFileName = '';

  /** 汉 */
  people = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 响应码 */
  responseCode = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 男 */
  sex = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 目标路径 */
  targetFileName = '';

  /** 第二代身份证 */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpOrderInfo {
  /** add */
  add = false;

  /** baseFile */
  baseFile = '';

  /** baseFileId */
  baseFileId = '';

  /** baseFileImpDt */
  baseFileImpDt = '';

  /** baseFileImporter */
  baseFileImporter = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByName */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** feeFile */
  feeFile = '';

  /** feeFileId */
  feeFileId = '';

  /** feeFileImpDt */
  feeFileImpDt = '';

  /** feeFileImporter */
  feeFileImporter = '';

  /** feeTemplateFileId */
  feeTemplateFileId = '';

  /** feeTemplateFileName */
  feeTemplateFileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** impName */
  impName = '';

  /** impOrderId */
  impOrderId = '';

  /** impOrderInfoItemList */
  impOrderInfoItemList = [];

  /** impOrderType */
  impOrderType = '';

  /** impTag */
  impTag = '';

  /** impType */
  impType = '';

  /** 1:普通批量导入2:纯商报批量导入 */
  importType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationCode */
  quotationCode = '';

  /** quotationId */
  quotationId = '';

  /** quotationName */
  quotationName = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpOrderInfoItem {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column IMP_ORDER_INFO_ITEM.IMP_ORDER_ID	  	  ibatorgenerated Fri Jul 06 17:16:25 CST 2012 */
  impOrderId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column IMP_ORDER_INFO_ITEM.IMP_ORDER_ITEM_ID	  	  ibatorgenerated Fri Jul 06 17:16:25 CST 2012 */
  impOrderItemId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column IMP_ORDER_INFO_ITEM.ITEM_ID	  	  ibatorgenerated Fri Jul 06 17:16:25 CST 2012 */
  itemId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column IMP_ORDER_INFO_ITEM.ITEM_NAME	  	  ibatorgenerated Fri Jul 06 17:16:25 CST 2012 */
  itemName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column IMP_ORDER_INFO_ITEM.ITEM_TYPE	  	  ibatorgenerated Fri Jul 06 17:16:25 CST 2012 */
  itemType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column IMP_ORDER_INFO_ITEM.SEQ_NUM	  	  ibatorgenerated Fri Jul 06 17:16:25 CST 2012 */
  seqNum = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpOrderPro {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单起始月 */
  billStartMonth = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 收费起始月 */
  chargeStartMon = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** custCode */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 供应商总账单起始月 */
  exBillStartMonth = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 导入文件ID */
  impFileId = '';

  /** impFileName */
  impFileName = '';

  /** 导入任务名称 */
  impName = '';

  /** 主键 */
  impOrderProId = '';

  /** 导入状态(1:新建,2:导入完成,3:作废) */
  impTag = '';

  /** impTagName */
  impTagName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 导入产品子项 */
  proItems = [];

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerType */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 导入模板ID */
  tempFileId = '';

  /** tempFileName */
  tempFileName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** uploadBy */
  uploadBy = '';

  /** uploadDt */
  uploadDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpOrderProItem {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 若产品类型为1则为社保公积金产品所属社保组id, <br>	  为2则为产品大类id */
  belongGroupId = '';

  /** 冗余所属组名称 */
  belongGroupName = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 主键 */
  impOrderItemId = '';

  /** 主键 */
  impOrderProId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 子项类型,1:社保公积金类,2普通产品类 */
  proType = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 冗余产品名称 */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 显示顺序 */
  seqNum = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ImpOrderProQuery {
  /** 账单开始月 */
  billStartMonth = '';

  /** 收费开始月 */
  chargeStartMon = '';

  /** 创建日期起 */
  createDt = '';

  /** 创建日期止 */
  createDtEnd = '';

  /** 客户id */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入文件id */
  impFileId = '';

  /** 导入名称 */
  impName = '';

  /** 导入订单产品id */
  impOrderProId = '';

  /** 导入标记 */
  impTag = '';

  /** 是否删除 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 供应商类型 */
  providerType = '';

  /** startIndex */
  startIndex = undefined;
}

class ImpOrderProResultQuery {
  /** 客户编码 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 唯一号 */
  empCode = '';

  /** 订单id */
  empHireSepId = '';

  /** 雇员id */
  empId = '';

  /** 雇员姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误信息 */
  errorInfo = '';

  /** 错误类型 */
  errorType = '';

  /** 导入订单产品id */
  impOrderProId = '';

  /** 导入结果订单id */
  impOrderResultId = '';

  /** 导入标记 */
  impTag = '';

  /** 是否删除 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 小合同id */
  subcontractId = '';
}

class ImpOrderQuery {
  /** 基础文件Id */
  baseFileId = '';

  /** 创建人 */
  createBy = '';

  /** 创建日期起 */
  createDt = '';

  /** 创建日期止 */
  createDtEnd = '';

  /** 客户id */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误类型 */
  errType = '';

  /** 费用段文件Id */
  feeFileId = '';

  /** 导入名称 */
  impName = '';

  /** 导入订单id */
  impOrderId = '';

  /** 导入类型 */
  impOrderType = '';

  /** 导入标记 */
  impTag = '';

  /** 1:普通批量导入2:纯商报批量导入 */
  importType = '';

  /** 是否有效 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 报价单id */
  quotationId = '';

  /** 备注 */
  remark = '';

  /** startIndex */
  startIndex = undefined;
}

class LaborContract {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 增员状态 */
  addConfirmStatus = '';

  /** 增员状态name */
  addConfirmStatusName = '';

  /** 增员状态 decode值 NP-8383 */
  addConfirmStatusText = '';

  /** addOrModify */
  addOrModify = undefined;

  /** 接单客服id */
  assigneeCsId = '';

  /** assigneeCsInstanceId */
  assigneeCsInstanceId = '';

  /** 接单客服name */
  assigneeCsName = '';

  /** 接单方客服对应联系方式 */
  assigneeCsTel = '';

  /** 接单方name */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服id */
  assignerCsId = '';

  /** assignerCsInstanceId */
  assignerCsInstanceId = '';

  /** 派单客服name */
  assignerCsName = '';

  /** 派单方客服对应联系方式 */
  assignerCsTel = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 取消聘用原因 */
  cancelHireReason = '';

  /** cityId */
  cityId = '';

  /** 城市 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 契约锁完成时间 */
  completeTime = '';

  /** 手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 劳动合同编号 */
  contractId = '';

  /** 劳动合同期限 */
  contractPeriod = '';

  /** 合同原则 */
  contractPrinciple = '';

  /** 合同性质：1新签、2续签、3变更、4补充 */
  contractProperty = undefined;

  /** 合同性质名称 */
  contractPropertyName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 合同类型 */
  contractType = '';

  /** 员工类别 */
  contractTypeEx = '';

  /** 合同类型显示名称 */
  contractTypeName = '';

  /** 员工类别(显示) */
  contractTypeNameEx = '';

  /** 合同版本 */
  contractVersion = '';

  /** 合同版本显示名称 */
  contractVersionName = '';

  /** 法人公司个数 */
  corporationCount = undefined;

  /** 法人单位ID */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 当前备注修改时间 NP-5286 */
  currTimeStamp = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 日期字段 */
  dateType = '';

  /** 期限 */
  deadLine = '';

  /** del */
  del = false;

  /** 电子合同id */
  eleContractId = '';

  /** 电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
  eleContractStatus = undefined;

  /** 电子合同状态（显示） */
  eleContractStatusName = '';

  /** eos中add表id */
  empAddId = '';

  /** 员工唯一号 */
  empCode = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入离职状态 */
  empHireStatus = '';

  /** 入离职状态 decode值 NP-8383 */
  empHireStatusText = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** 工号 */
  employeeNo = '';

  /** 员工签署日期 */
  employeeSignTime = '';

  /** 员工签署日期小于 */
  employeeSignTimeFrom = '';

  /** 员工签署日期大于 */
  employeeSignTimeTo = '';

  /** 结束时间 */
  endDt = '';

  /** 结束时间2 */
  endDt2 = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS劳动合同签署提醒流程实例ID */
  eosAssigneeCsInstanceId = '';

  /** 大客户续签劳动合同表id */
  eosReLaborContractId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 失败原因 */
  failureReason = '';

  /** 上传附件文件id */
  fileId = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 证件类型name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否删除 */
  isDeleted = '';

  /** 是否需要外呼 */
  isNeedCall = '';

  /** 是否需要外呼Name */
  isNeedCallName = '';

  /** 是否试用期 */
  isTrial = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 劳动合同id集合 */
  laborContractIds = '';

  /** 劳动合同list */
  laborContractList = [];

  /** 劳动合同动态字段 */
  laborParams = [];

  /** 模拟人 */
  mimicBy = '';

  /** 大于该日期的输入日期内容 */
  moreThanDt = '';

  /** 新增备注 NP-5286 */
  newRemark = '';

  /** noChange */
  noChange = false;

  /** 无固定期限劳动合同起始时间 */
  noFixedStartDt = '';

  /** 无固定期限试用期结束时间 */
  noFixedTrialEndDt = '';

  /** 无固定期限试用期月数 */
  noFixedTrialPeriod = '';

  /** 无固定期限试用期起始时间 */
  noFixedTrialStartDt = '';

  /** 无固定期限试用期工资百分比 */
  noFixedTrialWagePer = '';

  /** notOrder */
  notOrder = '';

  /** 原劳动合同id */
  oldLaborContractId = '';

  /** 劳动合同来源：(0手动创建,1自动创建) */
  origin = '';

  /** 劳动合同来源名称：(0手动创建,1自动创建) */
  originName = '';

  /** 其他劳动合同相关说明事项 */
  otherLabor = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程定义Id */
  processDefId = '';

  /** 供应商id(主签) */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 转正工资 */
  regularWage = '';

  /** 备注 */
  remark = '';

  /** 提醒标志 */
  remindMark = '';

  /** 大客户续签eos续签状态值(1:已提交、2:待提交、3:已取消) */
  renewStatus = '';

  /** 大客户续签eos续签状态文本 */
  renewStatusText = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 派遣期限结束 */
  sendPeriodEndDt = '';

  /** 派遣期限起始 */
  sendPeriodStartDt = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** 签订操作人 */
  signBy = '';

  /** 签订操作人显示名称 */
  signByName = '';

  /** 合同签订日期 */
  signDate = '';

  /** 合同生效日期 */
  signDt = '';

  /** 签署日期2 */
  signDt2 = '';

  /** 签单地 */
  signLoc = '';

  /** 签单地 */
  signLocName = '';

  /** 合同签订地 */
  signPlace = undefined;

  /** 印章流程个数 */
  signProcessCount = undefined;

  /** 印章流程ID */
  signProcessId = '';

  /** 合同版本名称 */
  signProcessName = '';

  /** 签署状态 */
  signStatus = '';

  /** 签署状态显示名称 */
  signStatusName = '';

  /** 合同签订形式 */
  signType = undefined;

  /** 合同签订形式(显示) */
  signTypeName = '';

  /** 开始时间 */
  startDt = '';

  /** 开始时间2 */
  startDt2 = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 提交时间 */
  submitDt = '';

  /** 大客户续签eos提交结束时间 */
  submitEndDt = '';

  /** 大客户续签eos提交开始时间 */
  submitStartDt = '';

  /** 同步状态 */
  syncStatus = '';

  /** 同步状态(显示) */
  syncStatusName = '';

  /** 试用结束时间 */
  trialEndDt = '';

  /** 试用期限 */
  trialPeriod = '';

  /** 试用开始时间 */
  trialStartDt = '';

  /** 试用工资 */
  trialWage = '';

  /** 试用期工资百分比 */
  trialWagePer = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 当前操作人Id NP-5286 */
  userId = '';

  /** userIdType */
  userIdType = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本地 */
  versionLoc = '';

  /** 版本地 */
  versionLocName = '';

  /** 工作城市 */
  workCity = '';

  /** 工作岗位 */
  workPost = '';

  /** 工作制 */
  workSystem = '';

  /** 工作制中文1, '标准工时',2,'不定时工时', 3, '综合工时', 4, '特殊工时（不定时工时/综合工时） */
  workSystemStr = '';

  /** 工作单位 */
  workUnit = '';

  /** workitemId */
  workitemId = '';
}

class MakeupPay {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.CREATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.CREATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.IS_DELETED           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MAKEUP_PAY_END_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MAKEUP_PAY_PROCESS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MAKEUP_PAY_START_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayStartMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.MIMIC_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.PRODUCT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.PROXY_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.SS_MAKEUP_PAY_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMakeupPayId = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.UPDATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY.UPDATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class MakeupPayMon {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  amt = undefined;

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMon */
  annualPaymentMon = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAdditionalAmt = undefined;

  /** eAdditionalAmtOld */
  eAdditionalAmtOld = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAmt = undefined;

  /** eAmtOld */
  eAmtOld = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eBase = undefined;

  /** 企业计算方法 */
  eCalculationMethod = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_LATE_FEE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eLateFee = undefined;

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业精确值 */
  ePrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.E_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** eRecordSum */
  eRecordSum = '';

  /** empCode */
  empCode = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** isSsExclued */
  isSsExclued = '';

  /** isSsExcluedFlag */
  isSsExcluedFlag = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.MAKEUP_PAY_PRO_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  makeupPayProMon = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pBase = undefined;

  /** 个人计算方法 */
  pCalculationMethod = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_LATE_FEE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pLateFee = undefined;

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人精确值 */
  pPrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.P_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** pRecordSum */
  pRecordSum = '';

  /** payFrequency */
  payFrequency = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.PRODUCT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.PRODUCT_RATIO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** rMon */
  rMon = '';

  /** recordSum */
  recordSum = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** returnsEamt */
  returnsEamt = '';

  /** returnsPamt */
  returnsPamt = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** ssInfoIds */
  ssInfoIds = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_MAKEUP_PAY_MON_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMakeupPayMonId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_MAKEUP_PAY_MON.SS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssMon = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** welfareProcessorName */
  welfareProcessorName = '';
}

class Material {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_NAME           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class OneTimeFrequencyVO {
  /** 账单频率列表 */
  list = [];

  /** 账单模板id */
  receivableTempltId = '';
}

class OrderSsGroupSvc {
  /** add */
  add = false;

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** custPayerId */
  custPayerId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** detailId */
  detailId = '';

  /** empHireSepId */
  empHireSepId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** orderSsGroupSvcId */
  orderSsGroupSvcId = undefined;

  /** pAcct */
  pAcct = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processRemark */
  processRemark = '';

  /** processType */
  processType = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** reduceDetailId */
  reduceDetailId = '';

  /** reduceSendChannel */
  reduceSendChannel = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sendChannel */
  sendChannel = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssGroupType */
  ssGroupType = '';

  /** status */
  status = undefined;

  /** statusEx */
  statusEx = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** welfareEndMon */
  welfareEndMon = '';

  /** welfareProcessor */
  welfareProcessor = '';
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class ProcessInfo {
  /** acct */
  acct = '';

  /** add */
  add = false;

  /** addConfirmStatus */
  addConfirmStatus = '';

  /** addConfirmStatusName */
  addConfirmStatusName = '';

  /** allRemark */
  allRemark = '';

  /** alterStatus */
  alterStatus = '';

  /** alterStatusName */
  alterStatusName = '';

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** areaType */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** 接单客服id */
  assigneeCs = '';

  /** assigneeCsName */
  assigneeCsName = '';

  /** 派单客服id */
  assignerCs = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.CUST_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** 缴费实体名称 */
  custPayEntityName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模，1大型客户，2中型客户，3小型客户 */
  customerSize = '';

  /** 客户规模(显示) */
  customerSizeName = '';

  /** 申报工资 */
  decSalary = undefined;

  /** del */
  del = false;

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_HIRE_SEP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empHireSepId = undefined;

  /** empHireSepStatus */
  empHireSepStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** filterEndMon */
  filterEndMon = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hireDt */
  hireDt = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** inId */
  inId = '';

  /** insuranceName */
  insuranceName = '';

  /** 增员是否需要实做 */
  isAddProcess = '';

  /** 增员是否需要实做 */
  isAddProcessName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isIndependent */
  isIndependent = '';

  /** 减员是否需要实做 */
  isReduceProcess = '';

  /** 减员是否需要实做 */
  isReduceProcessName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.NEXT_POINT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  nextPoint = undefined;

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.P_ACCT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAcct = undefined;

  /** pAcctEx */
  pAcctEx = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 派遣单位名称 */
  payerName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PRE_POINT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  prePoint = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processDt = '';

  /** processEndDt */
  processEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_REMARK           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_TYPE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processType = undefined;

  /** processTypeName */
  processTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESSOR_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processorId = undefined;

  /** processorName */
  processorName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** queryMode */
  queryMode = '';

  /** reduceSendChannel */
  reduceSendChannel = '';

  /** reduceSendChannelName */
  reduceSendChannelName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** rptHireDt */
  rptHireDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.S_NO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  sNo = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** sendChannel */
  sendChannel = '';

  /** sendChannelName */
  sendChannelName = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** 签单客服id */
  signCs = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STATUS           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopBy = undefined;

  /** stopByName */
  stopByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopDt = '';

  /** stopEndDt */
  stopEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_REMARK           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_TYPE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopType = undefined;

  /** stopTypeName */
  stopTypeName = '';

  /** strStatus */
  strStatus = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** subcontractId */
  subcontractId = '';

  /** subcontractName */
  subcontractName = '';

  /** 统一社会信用码 */
  taxpayerIdentifier = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** verificationFile */
  verificationFile = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_END_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESSOR           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_START_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class ProductRatioDetail {
  /** add */
  add = false;

  /** adjustNotes */
  adjustNotes = '';

  /** adjustType */
  adjustType = '';

  /** adjustTypeName */
  adjustTypeName = '';

  /** 年度平均工资 */
  annualAvgSalary = '';

  /** 年度最低工资 */
  annualMinSalary = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** eMaxBase */
  eMaxBase = undefined;

  /** eMinBase */
  eMinBase = undefined;

  /** endMonth */
  endMonth = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** pMaxBase */
  pMaxBase = undefined;

  /** pMinBase */
  pMinBase = undefined;

  /** policyLink */
  policyLink = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productRatioAppId */
  productRatioAppId = '';

  /** productRatioDetailId */
  productRatioDetailId = '';

  /** productRatioId */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startMonth */
  startMonth = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QueryMaterialObj {
  /** cityId */
  cityId = '';

  /** custCode */
  custCode = '';

  /** custName */
  custName = '';

  /** empCode */
  empCode = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** id 串 */
  ids = '';

  /** 材料名 */
  materialName = '';

  /** 材料id */
  mid = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 收缴状态 */
  status = '';

  /** subcontractAlias */
  subcontractAlias = '';

  /** 转发人 */
  transBy = '';

  /** 类型 1 入职, 2 离职. */
  type = '';
}

class Quotation {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.APPROVE_PROCESS_INS_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveProcessInsId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.APPROVE_STATUS	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** attId */
  attId = '';

  /** attName */
  attName = '';

  /** auditOpinion */
  auditOpinion = '';

  /** 审批类型 */
  auditType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateType = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.CITE_QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  citeQuotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.CITY_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建者name */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** createDtFrom */
  createDtFrom = '';

  /** createDtTo */
  createDtTo = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.CURRENCY	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  currency = undefined;

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.CUST_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.DEPARTMENT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  departmentId = undefined;

  /** depthMark */
  depthMark = '';

  /** effectBy */
  effectBy = '';

  /** effectDt */
  effectDt = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.EXCHANGE_RATE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  exchangeRate = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** governingArea */
  governingArea = '';

  /** governingBranch */
  governingBranch = '';

  /** groupType */
  groupType = '';

  /** groupTypeName */
  groupTypeName = '';

  /** ifAf */
  ifAf = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.IF_FIRST_AUDIT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifFirstAudit = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.IF_NATIONWIDE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifNationwide = undefined;

  /** ifSs */
  ifSs = '';

  /** inId */
  inId = '';

  /** invalidBy */
  invalidBy = '';

  /** invalidDt */
  invalidDt = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否包含退休产品 */
  isRetireeProduct = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.IS_SPECIAL_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isSpecialPrice = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** markType */
  markType = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.NEW_SALE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  newSaleId = undefined;

  /** newSaleName */
  newSaleName = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.OLD_SALE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  oldSaleId = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** processInsId */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_CODE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationCode = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationGroupName */
  quotationGroupName = '';

  /** quotationHisId */
  quotationHisId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_NAME	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_SPECIAL_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_TOTAL_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalCost = undefined;

  /** 职场健康总售价 */
  quotationTotalOhPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_TOTAL_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.QUOTATION_TOTAL_SAL_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalSalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.STATUS	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.SUPPLT_MED_INSUR_HEADCOUNT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suppltMedInsurHeadcount = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION.SVC_AREA	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  svcArea = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workitemId */
  workitemId = '';
}

class ReceivableDisplay {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 账单显示id */
  efReceivableDisplayId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否显示 */
  isDisplay = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 项目名称 */
  itemName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 次序 */
  order = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 账单id */
  receivableTempltId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ReceivableFrequency {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = undefined;

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custId */
  custId = undefined;

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费月,月收：无,两月收21:奇数月;22:偶数月.,三月收31:1,4,7,10;32:2,5,8,11;33:3,6,9,12.,半年收61:1,7;62:2,8;63:3,9;64:4,10;65:5,11;66:6,12.,年收：无 */
  feeMonth = '';

  /** 收费月Name */
  feeMonthName = '';

  /** feeType */
  feeType = undefined;

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** 收费频率id */
  frequencyId = '';

  /** 频率名称，可空 */
  frequencyName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否默认 */
  isDefault = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否被引用 */
  isQuoted = false;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = undefined;

  /** quotationName */
  quotationName = '';

  /** 账单模板id */
  receivableTempltId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ReceivablePrecision {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市ID */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 单位部分进位 1:四舍五入、2:见零进整、3:不调整、4:先四舍五入再见零进整 */
  eNotation = undefined;

  /** 单位部分精度:1不调整2元3角4分 */
  ePrecision = undefined;

  /** 精度配置id */
  efReceivablePrecisionId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 社保组名称 */
  insuranceName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 个人部分进位 1:四舍五入、2:见零进整、3:不调整、4:先四舍五入再见零进整 */
  pNotation = undefined;

  /** 个人部分精度:1不调整2元3角4分 */
  pPrecision = undefined;

  /** 精度配置描述 */
  precisionDesc = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = undefined;

  /** 产品名称 */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 账单模板id */
  receivableTempltId = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保组ID */
  ssGroupId = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ReceivableTemplate {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 补充说明 */
  additionalNotes = '';

  /** agreedBillGenDt */
  agreedBillGenDt = '';

  /** agreedBillLockDt */
  agreedBillLockDt = '';

  /** agreedCommitDt */
  agreedCommitDt = '';

  /** agreedPayDt */
  agreedPayDt = '';

  /** agreedWageArriveDay */
  agreedWageArriveDay = '';

  /** amtReceivedMon */
  amtReceivedMon = '';

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** 审批过程 */
  approvalProcess = '';

  /** 审批意见 */
  approvalRemarks = '';

  /** 审批状态:1待提交、2待大区客服总监审批、3待质控审批、4驳回、5审批通过 */
  approvalStatus = undefined;

  /** approveDt */
  approveDt = '';

  /** areaName */
  areaName = '';

  /** 驳回原因 */
  backOpinion = '';

  /** 开户银行 */
  bank = '';

  /** 银行账号 */
  bankAcct = '';

  /** 收款方所属银行 */
  bankAcctId = undefined;

  /** 所属银行 */
  bankId = undefined;

  /** 所属银行名 */
  bankIdName = '';

  /** 开户名 */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchName */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractCode */
  contractCode = '';

  /** contractId */
  contractId = undefined;

  /** contractName */
  contractName = '';

  /** 合同类别（子类） */
  contractSubType = undefined;

  /** 合同类别名（子类） */
  contractSubTypeName = '';

  /** 合同类别 */
  contractType = undefined;

  /** 合同类别名 */
  contractTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建人name */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编码 */
  custCode = '';

  /** custId */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户付款方编码 */
  custPayerCode = '';

  /** custPayerId */
  custPayerId = '';

  /** 客户付款方名称 */
  custPayerName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departmentId */
  departmentId = '';

  /** displayItem */
  displayItem = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = undefined;

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** invoiceType */
  invoiceType = '';

  /** 开票方式(显示) */
  invoiceTypeName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否生成账单 */
  isGenBill = '';

  /** isIssuingSalary */
  isIssuingSalary = undefined;

  /** isPfExclued */
  isPfExclued = '';

  /** isShowType */
  isShowType = '';

  /** isSsExclued */
  isSsExclued = '';

  /** isTaxExclued */
  isTaxExclued = '';

  /** isWageExclued */
  isWageExclued = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** itemLength */
  itemLength = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** notDisplayItem */
  notDisplayItem = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** participant */
  participant = '';

  /** payeeId */
  payeeId = '';

  /** 付款方Name */
  payeeName = '';

  /** payerId */
  payerId = '';

  /** 账单方Name */
  payerName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** 流程实例ID */
  processInsId = '';

  /** 处理时间 */
  processingDt = '';

  /** 处理人 */
  processor = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realName */
  realName = '';

  /** receivableTempltAppDelId */
  receivableTempltAppDelId = '';

  /** receivableTempltAppId */
  receivableTempltAppId = undefined;

  /** receivableTempltId */
  receivableTempltId = '';

  /** receivableTempltName */
  receivableTempltName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** rptConfigId */
  rptConfigId = '';

  /** rptTempltPath */
  rptTempltPath = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signBranchTitleId */
  signBranchTitleId = '';

  /** 签约方公司抬头 */
  signBranchTitleName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** titleName */
  titleName = '';

  /** 修改人 */
  updateBy = '';

  /** 更新人name */
  updateByName = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workItemId */
  workItemId = '';
}

class ReceivableTemplateImpDetailQuery {
  /** add */
  add = false;

  /** 导入批次id */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ReceivableTemplateImpQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入日期到 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 导入日期从 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ReceivableTemplateInter {
  /** add */
  add = false;

  /** bank */
  bank = '';

  /** bankAcct */
  bankAcct = '';

  /** bankName */
  bankName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** id */
  id = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** languageType */
  languageType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** receivableTempltId */
  receivableTempltId = '';

  /** receivableTempltName */
  receivableTempltName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ReceiveDisplayAndPrecisionVO {
  /** 项目长度 */
  itemLength = undefined;

  /** 左侧项目 */
  leftItemString = '';

  /** 账单频率列表 */
  receivableFrequencyList = [];

  /** 账单精度列表 */
  receivablePrecisionList = [];

  /** 账单模板id */
  receivableTempltId = '';

  /** 右侧项目 */
  rightItemString = '';
}

class Remit {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  amt = undefined;

  /** annualMonthEFee */
  annualMonthEFee = undefined;

  /** annualMonthPFee */
  annualMonthPFee = undefined;

  /** annualPaymentMon */
  annualPaymentMon = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.CREATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.CREATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eBase = undefined;

  /** 企业计算方法 */
  eCalculationMethod = '';

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业精确值 */
  ePrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.E_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.IS_DELETED           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  isDeleted = '';

  /** isOneTimePay */
  isOneTimePay = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.MIMIC_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_ADDITIONAL_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAdditionalAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_AMT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAmt = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_BASE           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pBase = undefined;

  /** 个人计算方法 */
  pCalculationMethod = '';

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人精确值 */
  pPrecision = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.P_RATIO           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** payFrequency */
  payFrequency = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PRODUCT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productId = undefined;

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PRODUCT_RATIO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  productRatioId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.PROXY_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_GROUP_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_INFO_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.SS_REMIT_ID           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssRemitId = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.UPDATE_BY           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.UPDATE_DT           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_END_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_PROCESS_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_REMIT.WELFARE_START_MON           ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class Subcontract {
  /** activityNameEn */
  activityNameEn = '';

  /** 活动状态 */
  activityStatus = '';

  /** add */
  add = false;

  /** 调基任务收集邮箱 */
  adjEmail = '';

  /** 审批意见 */
  approveOpinion = '';

  /** 审批状态  1/审批中、2/审批通过、 3/驳回、4/终止  */
  approveStatus = '';

  /** 大区类型 */
  areaType = '';

  /** 大区类型名 */
  areaTypeName = '';

  /** 派单时间 */
  assignDt = '';

  /** 接单客服分公司 */
  assigneeBranchId = '';

  /** 接单城市 */
  assigneeCity = '';

  /** 接单方 */
  assigneeCs = '';

  /** 接单客服id */
  assigneeCsId = '';

  /** 接单方供应商 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服分公司 */
  assignerBranchId = '';

  /** 派单客服 */
  assignerCs = '';

  /** 派单客服id */
  assignerCsId = '';

  /** 派单人id */
  assignerId = '';

  /** 派单方供应商 */
  assignerProvider = '';

  /** 派单方id */
  assignerProviderId = '';

  /** 派单类型1 执行单2 协调单3 收集单 */
  assignmentType = '';

  /** 派单类型 */
  assignmentTypeName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单模板id */
  billTempltId = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 社保套餐Id */
  comboId = undefined;

  /** 联系人 */
  contact = '';

  /** 联系地址 */
  contactAddress = '';

  /** 联系方式变更日期 */
  contactChangeDt = '';

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编码 */
  contractCode = '';

  /** 合同人数 */
  contractHeadcount = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 合同开始日期 */
  contractStartDate = '';

  /** 合同技术日期 */
  contractStopDate = '';

  /** 合同子类 */
  contractSubType = '';

  /** 合同类型 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 法人公司列表 */
  corporations = [];

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建日期止 */
  createEndDt = '';

  /** 创建日期起 */
  createStartDt = '';

  /** 客户账单模板id */
  custBillTempltId = '';

  /** 客户编码 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 缴费实体Id */
  custPayEntityId = undefined;

  /** 缴费实体名称  */
  custPayEntityName = '';

  /** 付款方表ID */
  custPayerId = undefined;

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 部门id */
  departmentId = '';

  /** 收费模板名 */
  eFeeTemplt = '';

  /** 收费模板id */
  eFeeTempltId = '';

  /** 电子邮件 */
  email = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empType = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 传真 */
  fax = '';

  /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  feeMonth = '';

  /** 收费模板名称 */
  feeTemplt = '';

  /** 收费模板id */
  feeTempltId = '';

  /** 文件id */
  fileId = '';

  /** 文件名 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 入职报价单id */
  hireQuotationId = '';

  /** inId */
  inId = '';

  /** 是否存档,0:否;1:是 */
  isArchive = '';

  /** 是否存档中文 */
  isArchiveName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否优选 */
  isFirstChoice = '';

  /** 是否优选名 */
  isFirstChoiceName = '';

  /** 是否单立户1 是0 否 */
  isIndependent = '';

  /** 是否需要签订劳动合同,0:否;1:是 */
  isLaborContractNeed = '';

  /** 是否需要签订劳动合同中文 */
  isLaborContractNeedName = '';

  /** 是否电话通知,0:否;1:是 */
  isPhoneCall = '';

  /** 是否电话通知中文 */
  isPhoneCallName = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = undefined;

  /** 是否一地投保 */
  isSameInsur = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同原则 */
  laborContractPrinciple = '';

  /** 劳动合同版本,1:范本;2:客户版本 */
  laborContractVersion = '';

  /** 劳动合同版本名 */
  laborContractVersionName = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 责任客服名 */
  liabilityCsName = '';

  /** 模拟人 */
  mimicBy = '';

  /** 操作方式  单立户1、大户2 */
  modeOfOperation = '';

  /** 操作方式名 */
  modeOfOperationName = '';

  /** 操作方式字符  单立户、大户 */
  modeOfOperationStr = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** noChange */
  noChange = false;

  /** 老合同编码 */
  oldContractCode = '';

  /** 老合同id */
  oldContractId = '';

  /** 老合同名称 */
  oldContractName = '';

  /** 订单人数 */
  orderNumber = '';

  /** 其他劳动合同相关说明事项 */
  otherLabor = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 收款方 */
  payeeName = '';

  /** 付款方 */
  payerName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程定义id */
  processDefId = '';

  /** 流程id */
  processInsId = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型，1内部，2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单编码 */
  quotationCode = '';

  /** 报价单id */
  quotationId = '';

  /** 被订单引用个数 */
  quotedNumber = undefined;

  /** 账单模板名 */
  receivableTempltName = '';

  /** 备注 */
  remark = '';

  /** 提醒类型:1新增小合同 ,2小合同转移 */
  remindType = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 签单客服分公司 */
  signBranchId = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** 签约方公司抬头名 */
  signBranchTitleName = '';

  /** 签单客服 */
  signCs = '';

  /** 签单客服id */
  signCsId = '';

  /** 合同签订地 1:派单地 2:接单地 3:派单地+接单地 4:拆分方 */
  signPlace = undefined;

  /** 印章流程列表 */
  signProcesses = [];

  /** 签单方供应商 */
  signProvider = '';

  /** 签单方id */
  signProviderId = '';

  /** 合同签订形式 1:电子版 2:纸质版 */
  signType = undefined;

  /** 特殊说明 */
  specialDesc = '';

  /** 分拆服务，逗号分隔 */
  splitServices = '';

  /** 社保套餐名称 */
  ssComboName = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  state = '';

  /** 状态名 */
  stateName = '';

  /** 状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编码 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 小合同分拆方列表 */
  subcontractSplitServiceList = [];

  /** 小合同类型 */
  subcontractTypeName = '';

  /** 服务子类 */
  svcSubtypeName = '';

  /** 服务类型 */
  svcTypeName = '';

  /** 统一社会信用码 */
  taxpayerIdentifier = '';

  /** 付款方式   代收代付通过供应商1、代收代付不通过供应商2 */
  termsOfPayment = '';

  /** 交接单id */
  transferId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 薪资类别id */
  wageClassId = '';

  /** 福利办理方 */
  welfareProcessor = '';

  /** workitemId */
  workitemId = '';

  /** 邮编 */
  zipCode = '';
}

class SubcontractSplitService {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** cs */
  cs = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.CS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  state = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.SUBCONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  subcontractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.SUBCONTRACT_SERVICE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  subcontractServiceId = '';

  /** svcBranchId */
  svcBranchId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.SVC_ITEM_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcItemId = '';

  /** svcProvider */
  svcProvider = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_SERVICE.SVC_PROVIDER_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcProviderId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class TransferInfo {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 约定发薪日期 */
  agreedPayDt = '';

  /** 约定到款日 */
  agreedWageArriveDay = '';

  /** areaName */
  areaName = '';

  /** 指派处理人 */
  arrayParticipant = undefined;

  /** bankCardSystemMaintenance */
  bankCardSystemMaintenance = undefined;

  /** bankCardSystemMaintenanceName */
  bankCardSystemMaintenanceName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单日期 */
  billDt = '';

  /** billFrequency */
  billFrequency = undefined;

  /** billLockDate */
  billLockDate = undefined;

  /** billTemplateId */
  billTemplateId = undefined;

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchName */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** calculationType */
  calculationType = undefined;

  /** calculationTypeName */
  calculationTypeName = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractCode */
  contractCode = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractId */
  contractId = undefined;

  /** contractName */
  contractName = '';

  /** contractSigningForm */
  contractSigningForm = undefined;

  /** contractStartDate */
  contractStartDate = '';

  /** contractStartDateEnd */
  contractStartDateEnd = '';

  /** contractStartDateStart */
  contractStartDateStart = '';

  /** contractStartTimeDuration */
  contractStartTimeDuration = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 合同类别（子类） */
  contractSubType = undefined;

  /** 合同类别名（子类） */
  contractSubTypeName = '';

  /** 合同类别 */
  contractType = undefined;

  /** 合同类别名 */
  contractTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = undefined;

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerConfirmsPayrollBill */
  customerConfirmsPayrollBill = undefined;

  /** customerInvoiceDeadlineReq */
  customerInvoiceDeadlineReq = '';

  /** customerOtherServiceContent */
  customerOtherServiceContent = '';

  /** customerProvidesSalaryDate */
  customerProvidesSalaryDate = undefined;

  /** customerSalaryPaymentDate */
  customerSalaryPaymentDate = undefined;

  /** customerSalaryPaymentTime */
  customerSalaryPaymentTime = undefined;

  /** customerVersionContractDesc */
  customerVersionContractDesc = '';

  /** customersMonthlyDeclCutoff */
  customersMonthlyDeclCutoff = undefined;

  /** del */
  del = false;

  /** employeeAddReportingMethod */
  employeeAddReportingMethod = undefined;

  /** employeeDataCollection */
  employeeDataCollection = undefined;

  /** employeeReduReportingMethod */
  employeeReduReportingMethod = undefined;

  /** employeeServiceType */
  employeeServiceType = '';

  /** employeesBankRequirements */
  employeesBankRequirements = '';

  /** endIndex */
  endIndex = undefined;

  /** enhancedAgent */
  enhancedAgent = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileId */
  fileId = undefined;

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** inId */
  inId = '';

  /** inserviceBizAppMethod */
  inserviceBizAppMethod = undefined;

  /** insuranceLocation */
  insuranceLocation = undefined;

  /** invoiceMethod */
  invoiceMethod = undefined;

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否代发薪资 */
  isIssuingSalary = undefined;

  /** isSalaryChanged */
  isSalaryChanged = undefined;

  /** isSocialInsuranceDataPull */
  isSocialInsuranceDataPull = undefined;

  /** isSocialInsuranceDataPullName */
  isSocialInsuranceDataPullName = '';

  /** isStandardCustomer */
  isStandardCustomer = undefined;

  /** isStandardCustomerName */
  isStandardCustomerName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** laborContractVersion */
  laborContractVersion = undefined;

  /** liabilityCs */
  liabilityCs = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 模拟人 */
  mimicBy = '';

  /** modifiedFields */
  modifiedFields = '';

  /** monthlyPayrollBillDate */
  monthlyPayrollBillDate = undefined;

  /** noChange */
  noChange = false;

  /** numberOfPayrolls */
  numberOfPayrolls = undefined;

  /** otherSalaryNotes */
  otherSalaryNotes = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** payslipFormat */
  payslipFormat = undefined;

  /** payslipFormatName */
  payslipFormatName = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processInsId */
  processInsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationNames */
  quotationNames = '';

  /** receivableTempltName */
  receivableTempltName = '';

  /** redFields */
  redFields = [];

  /** rejectBy */
  rejectBy = undefined;

  /** rejectDate */
  rejectDate = '';

  /** rejectReason */
  rejectReason = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salaryCutoffDateOperMethod */
  salaryCutoffDateOperMethod = undefined;

  /** salaryCutoffDateOperMethodName */
  salaryCutoffDateOperMethodName = '';

  /** salaryPaymentLocation */
  salaryPaymentLocation = undefined;

  /** salaryPaymentLocationName */
  salaryPaymentLocationName = '';

  /** salaryServiceFeeList */
  salaryServiceFeeList = [];

  /** salaryTempId */
  salaryTempId = undefined;

  /** salaryTransferProcessingDt */
  salaryTransferProcessingDt = '';

  /** salaryTransferProcessor */
  salaryTransferProcessor = undefined;

  /** salaryTransferProcessorName */
  salaryTransferProcessorName = '';

  /** salaryTransferStatus */
  salaryTransferStatus = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceContent */
  serviceContent = '';

  /** serviceFeeList */
  serviceFeeList = [];

  /** socialFundOtherNotes */
  socialFundOtherNotes = '';

  /** socialInsurancePullMethod */
  socialInsurancePullMethod = '';

  /** socialInsurancePullMethodName */
  socialInsurancePullMethodName = '';

  /** socialInsurancePullMonth */
  socialInsurancePullMonth = '';

  /** socialInsurancePullMonthName */
  socialInsurancePullMonthName = '';

  /** specialNotesOnInvoicing */
  specialNotesOnInvoicing = '';

  /** standaloneEmploymentDateAgName */
  standaloneEmploymentDateAgName = '';

  /** standaloneEmploymentDateAgr */
  standaloneEmploymentDateAgr = undefined;

  /** standalonePayrollTaxSerSup */
  standalonePayrollTaxSerSup = '';

  /** standalonePayrollTaxServicName */
  standalonePayrollTaxServicName = '';

  /** standalonePayrollTaxService */
  standalonePayrollTaxService = undefined;

  /** standaloneTaxDeclCompTime */
  standaloneTaxDeclCompTime = '';

  /** standaloneTaxDeclCompTimeName */
  standaloneTaxDeclCompTimeName = '';

  /** standaloneTaxDeclPrinciple */
  standaloneTaxDeclPrinciple = '';

  /** standaloneTaxDeclPrincipleName */
  standaloneTaxDeclPrincipleName = '';

  /** standaloneTaxDeclSpecified */
  standaloneTaxDeclSpecified = '';

  /** startIndex */
  startIndex = undefined;

  /** startPaymentAgreementDate */
  startPaymentAgreementDate = '';

  /** startPaymentMonth */
  startPaymentMonth = undefined;

  /** stopPaymentAgreementDate */
  stopPaymentAgreementDate = '';

  /** stopPaymentMonth */
  stopPaymentMonth = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** suppMedicalInsuranceAgree */
  suppMedicalInsuranceAgree = '';

  /** suppPaymentAgreement */
  suppPaymentAgreement = '';

  /** suppServiceFeeList */
  suppServiceFeeList = [];

  /** titleName */
  titleName = '';

  /** transferId */
  transferId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** whetherTheCustomerHasSigne */
  whetherTheCustomerHasSigne = undefined;

  /** whetherTheCustomerHasSigneName */
  whetherTheCustomerHasSigneName = '';

  /** workItemId */
  workItemId = '';

  /** workingHoursSystem */
  workingHoursSystem = '';
}

class TransferInfoQuery {
  /** 地区类型，0或1 */
  areaType = '';

  /** 城市id */
  cityId = '';

  /** 合同编码 */
  contractCode = '';

  /** 合同名称 */
  contractName = '';

  /** 合同结束日期 */
  contractStartDateEnd = '';

  /** 合同开始日期 */
  contractStartDateStart = '';

  /** 合同服务状态，0或1 */
  contractSvcState = '';

  /** 合同类型 */
  contractType = '';

  /** 当前销售 */
  currentSales = '';

  /** 客户id */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 所属大区 */
  governingArea = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 是否有接单信息，0没有1有 */
  hasTransferInfo = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class TransferInfoVO {
  /** 小合同列表 */
  list = [];

  /** 交接单信息 */
  transferInfo = new TransferInfo();
}

class impErrorTypeDTO {
  /** 错误id */
  errorId = undefined;

  /** 错误信息 */
  errorInfo = undefined;

  /** 错误类型 */
  errorType = undefined;

  /** 传入的参数 */
  exceptId = undefined;

  /** 导入类别 */
  typeId = undefined;
}

class processInfoDTO {
  /** acct */
  acct = '';

  /** addConfirmStatus */
  addConfirmStatus = '';

  /** addConfirmStatusName */
  addConfirmStatusName = '';

  /** allRemark */
  allRemark = '';

  /** alterStatus */
  alterStatus = '';

  /** alterStatusName */
  alterStatusName = '';

  /** applicant */
  applicant = '';

  /** applyDt */
  applyDt = '';

  /** applyProcessList */
  applyProcessList = [];

  /** areaType */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** 接单客服id */
  assigneeCs = '';

  /** assigneeCsName */
  assigneeCsName = '';

  /** 派单客服id */
  assignerCs = '';

  /** batchId */
  batchId = '';

  /** changeRemark */
  changeRemark = '';

  /** cityId */
  cityId = '';

  /** contractId */
  contractId = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.CUST_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  custId = undefined;

  /** custName */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = '';

  /** empCode */
  empCode = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_HIRE_SEP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empHireSepId = undefined;

  /** empHireSepStatus */
  empHireSepStatus = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.EMP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  empId = '';

  /** empName */
  empName = '';

  /** filterEndMon */
  filterEndMon = '';

  /** hireDt */
  hireDt = '';

  /** idCardNum */
  idCardNum = '';

  /** insuranceName */
  insuranceName = '';

  /** isIndependent */
  isIndependent = '';

  /** lockMon */
  lockMon = '';

  /** lockMonM */
  lockMonM = '';

  /** makeupPayList */
  makeupPayList = [];

  /** makeupPayMonList */
  makeupPayMonList = [];

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.NEXT_POINT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  nextPoint = undefined;

  /** orderSsGroupSvc */
  orderSsGroupSvc = new OrderSsGroupSvc();

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.P_ACCT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  pAcct = undefined;

  /** pAcctEx */
  pAcctEx = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PRE_POINT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  prePoint = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processDt = '';

  /** processEndDt */
  processEndDt = '';

  /** processInfo */
  processInfo = new ProcessInfo();

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_REMARK           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processRemark = '';

  /** processRemark2 */
  processRemark2 = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESS_TYPE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processType = undefined;

  /** processTypeName */
  processTypeName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.PROCESSOR_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  processorId = undefined;

  /** processorName */
  processorName = '';

  /** queryMode */
  queryMode = '';

  /** remitList */
  remitList = [];

  /** rptHireDt */
  rptHireDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.S_NO           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  sNo = '';

  /** sepConfirmStatus */
  sepConfirmStatus = '';

  /** sepConfirmStatusName */
  sepConfirmStatusName = '';

  /** 签单客服id */
  signCs = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_GROUP_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssGroupId = undefined;

  /** ssGroupName */
  ssGroupName = '';

  /** ssGroupType */
  ssGroupType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.SS_INFO_ID           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  ssInfoId = undefined;

  /** ssType */
  ssType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STATUS           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_BY           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopBy = undefined;

  /** stopByName */
  stopByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_DT           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopDt = '';

  /** stopEndDt */
  stopEndDt = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_REMARK           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopRemark = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.STOP_TYPE           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  stopType = undefined;

  /** stopTypeName */
  stopTypeName = '';

  /** strStatus */
  strStatus = '';

  /** subcontractId */
  subcontractId = '';

  /** subcontractName */
  subcontractName = '';

  /** typeId */
  typeId = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_END_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareEndMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESS_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessMon = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_PROCESSOR           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareProcessor = undefined;

  /** welfareProcessorName */
  welfareProcessorName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SS_PROCESS_INFO.WELFARE_START_MON           @ibatorgenerated Tue Jan 31 16:10:37 CST 2012 */
  welfareStartMon = '';
}

class roleQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 业务类别 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 姓名 */
  realName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 角色编码 */
  roleCode = '';

  /** 角色层级 */
  roleGrade = '';

  /** 角色id */
  roleId = '';

  /** 角色名称 */
  roleName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

export const emphiresep = {
  AdjustmentQuery,
  AdjustmentTask,
  AdjustmentTaskItem,
  AdjustmentTaskMinItem,
  AgentWageIdCardNumManageQuery,
  BaseEntity,
  BatchAlterEmpOrder,
  BatchAlterEmpOrderQuery,
  BatchDeleteProduct,
  BatchDeleteProductQuery,
  CallCenterQuery,
  CallCenterVO,
  CcWorkOrder,
  CcWorkOrderQuery,
  CommonResponse,
  Contract,
  ContractFile,
  ContractRetiree,
  DropdownList,
  EmpBankCard,
  EmpBankCardDTO,
  EmpBankCardQuery,
  EmpFeeMonthQuery,
  EmpHireSepTempAppDTO,
  EmpHireSyncResult,
  EmpSMSRecord,
  EmpSMSRecordQuery,
  EmployeeBaseInfo,
  EmployeeBaseInfoDTO,
  EmployeeFee,
  EmployeeFeeMonth,
  EmployeeFeeMonthVO,
  EmployeeHireSep,
  EmployeeHireSepVO,
  EmployeeRelative,
  EmployeeTransfer,
  EmployeeTransferResult,
  ExportQuery,
  FilterEntity,
  HireFromAppQuery,
  HireMaterialActreceive,
  HireMaterialReceive,
  HireMaterialReceiveHis,
  HireMaterialSubmit,
  HireServiceTransaction,
  HiresepImgInfo,
  HiresepMain,
  HiresepWechatField,
  HsCcJob,
  HsCcMaterial,
  HsCcStandardLanguage,
  HsCcStatus,
  ImgDetail,
  ImpOrderInfo,
  ImpOrderInfoItem,
  ImpOrderPro,
  ImpOrderProItem,
  ImpOrderProQuery,
  ImpOrderProResultQuery,
  ImpOrderQuery,
  LaborContract,
  MakeupPay,
  MakeupPayMon,
  Material,
  OneTimeFrequencyVO,
  OrderSsGroupSvc,
  Page,
  ProcessInfo,
  ProductRatioDetail,
  QueryMaterialObj,
  Quotation,
  ReceivableDisplay,
  ReceivableFrequency,
  ReceivablePrecision,
  ReceivableTemplate,
  ReceivableTemplateImpDetailQuery,
  ReceivableTemplateImpQuery,
  ReceivableTemplateInter,
  ReceiveDisplayAndPrecisionVO,
  Remit,
  Subcontract,
  SubcontractSplitService,
  TransferInfo,
  TransferInfoQuery,
  TransferInfoVO,
  impErrorTypeDTO,
  processInfoDTO,
  roleQuery,
};
