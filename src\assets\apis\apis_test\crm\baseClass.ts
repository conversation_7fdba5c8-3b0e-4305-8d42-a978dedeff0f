class BeforeSaleReportQueryBean {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectedSignDateEd */
  expectedSignDateEd = '';

  /** expectedSignDateSt */
  expectedSignDateSt = '';

  /** expectedSignNumEd */
  expectedSignNumEd = '';

  /** expectedSignNumSt */
  expectedSignNumSt = '';

  /** expectedSignPriceEd */
  expectedSignPriceEd = '';

  /** expectedSignPriceSt */
  expectedSignPriceSt = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productlineSalerAreaId */
  productlineSalerAreaId = '';

  /** productlineSalerProviderId */
  productlineSalerProviderId = '';

  /** productlineSalername */
  productlineSalername = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** trackDateEd */
  trackDateEd = '';

  /** trackDateSt */
  trackDateSt = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** visitStage */
  visitStage = '';
}

class BusinessLogQuery {
  /** 批次 */
  batchId = '';

  /** custCode */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户原名称 */
  custName = '';

  /** ePropertyName */
  ePropertyName = '';

  /** endIndex */
  endIndex = undefined;

  /** executeDate */
  executeDate = '';

  /** executeDateEd */
  executeDateEd = '';

  /** executeDateSt */
  executeDateSt = '';

  /** expType */
  expType = '';

  /** exportType */
  exportType = '';

  /** 主键 */
  logId = '';

  /** newAreaId */
  newAreaId = '';

  /** newAreaText */
  newAreaText = '';

  /** newCityName */
  newCityName = '';

  /** newDeptName */
  newDeptName = '';

  /** newDeptPre */
  newDeptPre = '';

  /** newSaleId */
  newSaleId = '';

  /** 现销售 */
  newSaler = '';

  /** newexists */
  newexists = '';

  /** newgoverningBranch */
  newgoverningBranch = '';

  /** newgoverningBranchText */
  newgoverningBranchText = '';

  /** oldAreaId */
  oldAreaId = '';

  /** oldAreaText */
  oldAreaText = '';

  /** oldCityName */
  oldCityName = '';

  /** oldDeptName */
  oldDeptName = '';

  /** oldDeptPre */
  oldDeptPre = '';

  /** oldSaleId */
  oldSaleId = '';

  /** 原销售 */
  oldSaler = '';

  /** oldexists */
  oldexists = '';

  /** oldgoverningBranch */
  oldgoverningBranch = '';

  /** oldgoverningBranchText */
  oldgoverningBranchText = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品线创建时间 */
  plCreatDt = '';

  /** 产品线修改时间 */
  plUpdateDt = '';

  /** 客户产品线主键ID */
  proKeyId = '';

  /** 产品线 */
  productLine = '';

  /** 产品线名称 */
  productName = '';

  /** realName */
  realName = '';

  /** 备注 */
  remark = '';

  /** startIndex */
  startIndex = undefined;

  /** 类型ID	  <p>	  101: 客户建立	  <p>	  102:客户删除	  <p>	  103:客户进入共享	  <p>	  104:正在跟进分配	  <p>	  105:客户名称修改	  <p>	  106:共享区分配	  <p>	  107:删除区分配	  <p>	  108:客户基本信息修改	  <P>	  109:市场活动预录入客户分配 */
  typeId = '';

  /** typeName */
  typeName = '';
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new ContractAttachmentDTO();

  /** message */
  message = '';

  /** t */
  t = new ContractAttachmentDTO();
}

class Contract {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** add */
  add = false;

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agereedAmtReceiveMon = '';

  /** agreedPayDt */
  agreedPayDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** areaId */
  areaId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  areaType = '';

  /** areaTypeName */
  areaTypeName = '';

  /** attTypeDraftId */
  attTypeDraftId = '';

  /** attTypeDraftName */
  attTypeDraftName = '';

  /** attTypeLegalId */
  attTypeLegalId = '';

  /** attTypeLegalName */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  billDt = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市 */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** isIssuingSalary */
  contractCategeryName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractHeadcount = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartA = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractPartB = '';

  /** contractProductLineIds */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractRetrieveDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStatus = '';

  /** contractStatusName */
  contractStatusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractStopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSubType = '';

  /** 合同类别（子类）name */
  contractSubTypeName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** contractSvcStateName */
  contractSvcStateName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractTemplateId = '';

  /** contractTerminationDate */
  contractTerminationDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 合同类别 */
  contractVersionType = '';

  /** isIssuingSalary */
  contractVersionTypeName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByName */
  createByName = '';

  /** createByParty */
  createByParty = '';

  /** 创建日期 */
  createDt = '';

  /** createDtEnd */
  createDtEnd = '';

  /** createDtStart */
  createDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  createType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  creditPeriod = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  custPayerId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  custSealDt = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerSize */
  customerSize = '';

  /** defStatus */
  defStatus = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** draftRemark */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** estimateFirstBillDate */
  estimateFirstBillDate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** expectedIncrease */
  expectedIncrease = '';

  /** expectedIncreaseOld */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务name */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /** firstWgApproveId */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区Name */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司Name */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** private String contractSvcStateName; */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** governingBranchName */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** fileId */
  importFileId = '';

  /** fileName */
  importFileName = '';

  /** inId */
  inId = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** invoiceMoney */
  invoiceMoney = '';

  /** NP-8564 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
  isAdjustRenewContract = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  isAssign = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** isDefer */
  isDefer = '';

  /** isDeferName */
  isDeferName = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** isIssuingSalary */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** isSameInsur */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** isSecondaryDev */
  isSecondaryDev = '';

  /** isSecondaryDevName */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  legalApprovalStatus = '';

  /** legalRemark */
  legalRemark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** nextContractId */
  nextContractId = '';

  /** nextContractName */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** noChange */
  noChange = false;

  /** 非标合同审批单:NON_STA_COCT_APPR_ID */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  parentContractId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  payCollectPoint = '';

  /** payMonth */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  payerName = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApproval = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  prepayApprovalStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** processDefId */
  processDefId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  processInstanceId = '';

  /** productLineIdLogs */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectPlanRequest = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  projectRemark = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** providerType */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 报价单集合id */
  quoIds = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  renewedContractNum = '';

  /** reportElEvacuatedDate */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** reportGlEvacuatedDate */
  reportGlEvacuatedDate = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** roleCode */
  roleCode = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增存量标识 （手工） */
  salFlagManual = '';

  /** salFlagManualName */
  salFlagManualName = '';

  /** salFlagName */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApprove = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealApproveStatus = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  sealOpinion = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signArea */
  signArea = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** signBranchTitleIdAreaId */
  signBranchTitleIdAreaId = '';

  /** signBranchTitleIdBranchId */
  signBranchTitleIdBranchId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 撤单原因 */
  stopReason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  svcRegion = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadFileName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
  uploadUrl = '';

  /** upt */
  upt = false;

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** workitemId */
  workitemId = '';
}

class ContractAttachmentDTO {
  /** add */
  add = false;

  /** 文件名 */
  attName = '';

  /** 对象类型 1、 报价单   2、 合同法务   3、 合同终稿 */
  attType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 上传文件id */
  fileId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** crm附件表id */
  slAttachmentId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 对象ID 报价单ID或合同ID */
  tagId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractDTO {
  /** 流程节点ID */
  activityDefId = '';

  /** 审批步骤 */
  activityNameCn = '';

  /** 流程节点表的活动英文名称 */
  activityNameEn = '';

  /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
  activityStatus = '';

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** 约定到款月 */
  agereedAmtReceiveMon = '';

  /** 薪资发放日/约定薪资发放日 */
  agreedPayDt = '';

  /** 约定到款日 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** 审批通过时间 */
  approveDt = '';

  /** 审批通过时间到 */
  approveDtEnd = '';

  /** 审批通过时间从 */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** 大区ID */
  areaId = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaType = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaTypeName = '';

  /** 终稿 */
  attTypeDraftId = '';

  /** 终稿name */
  attTypeDraftName = '';

  /** 法务 */
  attTypeLegalId = '';

  /** 法务name */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 账单日期 */
  billDt = '';

  /** 城市 */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** 已经确认的工作流程 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 签约人均金额 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** contractCategeryName */
  contractCategeryName = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同创建人 */
  contractCreateBy = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同附件集合 */
  contractFileList = [];

  /** 合同文件名 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** 签约人数 */
  contractHeadcount = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 合同甲方 */
  contractPartA = '';

  /** 合同乙方 */
  contractPartB = '';

  /** 合同产品线id集合 */
  contractProductLineIds = '';

  /** 合同退休人员集合 */
  contractRetireeList = [];

  /** 客户盖章后，合同回收时间 */
  contractRetrieveDt = '';

  /** 合同起始日期 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** 合同状态：0 初始；1 审批中；2 审批通过；3 退回修改；4 驳回终止 */
  contractStatus = '';

  /** 合同状态name */
  contractStatusName = '';

  /** 合同结束日期 */
  contractStopDate = '';

  /** 合同终止原因 */
  contractStopReason = '';

  /** 合同小类 */
  contractSubType = '';

  /** 合同类别（子类）名称 */
  contractSubTypeName = '';

  /** 合同服务状态：0新签1续签2过期3终止服务 */
  contractSvcState = '';

  /** 合同服务状态name */
  contractSvcStateName = '';

  /** 合同模板编号 */
  contractTemplateId = '';

  /** 新平台合同终止时填写的终止时间 */
  contractTerminationDate = '';

  /** 合同大类 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** contractVersionTypeName */
  contractVersionTypeName = '';

  /** 创建人 */
  createByName = '';

  /** 创建人 */
  createByParty = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 合同生成方式 */
  createType = '';

  /** 账期（天） */
  creditPeriod = '';

  /** 客服审批 */
  csApproval = '';

  /** 客服审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** 必须是同一个客户，当前执行的合同的编号，如果续签多次，这个编号是最新的合同编号 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户盖章时间 */
  custSealDt = '';

  /** 供应商ID */
  departmentId = '';

  /** 供应商名称 */
  departmentName = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** 草稿备注 */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** 预估首次账单日期 */
  estimateFirstBillDate = '';

  /** 预计12个月内可达到人数 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 预计增长人数 */
  expectedIncrease = '';

  /** 原预计增长人数 */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** 合同审批的首个法务 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务名称 */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /**  合同审批的首个易薪税审批人员 */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区名称 */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司名称 */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** 未来商机 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** 现销售所属大区名称 */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 现销售所属分公司名称 */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** 导入文件ID */
  importFileId = '';

  /** 导入文件名称 */
  importFileName = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** 开票金额 */
  invoiceMoney = '';

  /** 开票张数 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？ */
  isAdjustRenewContract = '';

  /** 是否派单 */
  isAssign = '';

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** 是否降价、垫付、账期延期 1:是,0:否 */
  isDefer = '';

  /** 是否降价、垫付、账期延期名称 */
  isDeferName = '';

  /** isDeleted */
  isDeleted = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** 是否代发薪资 */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 是否正常审批 */
  isNormalApprove = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否有赠送退休额度 */
  isRetQuotaGranted = '';

  /** 是否包含退休业务 */
  isRetirementBusiness = '';

  /** 是否包含退休业务name */
  isRetirementBusinessName = '';

  /** 是否抢单 */
  isRob = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** 是否二次开发 1:是,0:否 */
  isSecondaryDev = '';

  /** 是否二次开发名称 */
  isSecondaryDevName = '';

  /** 含差旅服务 */
  isTravelServices = '';

  /** 含差旅服务(展示) */
  isTravelServicesName = '';

  /** 法务审批 */
  legalApproval = '';

  /** 法务审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  legalApprovalStatus = '';

  /** 合同附件备注 */
  legalRemark = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 责任客服所属分公司id */
  liabilityCsDepartmentId = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 责任客服职员代码 */
  libilityCsCode = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** 备注 */
  memo = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** 续签合同ID, 存放续签的大合同ID */
  nextContractId = '';

  /** 续签合同名称 */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** 非标合同审批单 */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 父合同id编号 */
  parentContractId = '';

  /** 付款和收款要点 */
  payCollectPoint = '';

  /** 薪资发放月 */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** 垫款额度 */
  prepayAmt = '';

  /** 垫付审批 */
  prepayApproval = '';

  /** 垫款审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  prepayApprovalStatus = '';

  /** 流程定义id */
  processDefId = '';

  /** 对应的流程实例ID */
  processInstanceId = '';

  /** 产品线id集合 */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** 项目前期计划或实施要求 */
  projectPlanRequest = '';

  /** 全国项目交接表单 */
  projectRemark = '';

  /** 供应商类型 */
  providerType = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 报价单集合id */
  quoIds = '';

  /** 被续签的旧合同号 */
  renewedContractNum = '';

  /** 客服反馈撤单预警日期大于等于 */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** 客服反馈撤单预警日期小于等于 */
  reportGlEvacuatedDate = '';

  /** 赠送退休数量 */
  retirementGiftCount = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增/存量标识 （手工） */
  salFlagManual = '';

  /** 1 纯新增,2 存量,3 纯新增/存量,4 纯新增+滚动存量,5 滚动存量,6 滚动存量+存量 */
  salFlagManualName = '';

  /** 新增/存量标识 （系统）：1历史纯新增、2滚动新增、3存量、4当月启动纯新增、-1未有首版账单 */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** 销售审批 */
  salesApprove = '';

  /** 销售报价审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  salesApproveStatus = '';

  /** 现销售职员代码 */
  salesCode = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** 用章审核状态 0：初始态3：通过 -3：退回修改 -4：驳回终止 */
  sealApproveStatus = '';

  /** 公司盖章时间 */
  sealDt = '';

  /** 用章审核 */
  sealOpinion = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** 撤单原因 */
  stopReason = '';

  /** 终止服务系统操作时间 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 销售补充标志 0需要销售补充信息1审批 */
  supplyMark = '';

  /** 销售补充附件说明(历史) */
  supplyShow = '';

  /** 销售补充附件名称 */
  supplyShowFileName = '';

  /** 销售补充附件路径 */
  supplyShowFilePath = '';

  /** 销售补充附件说明(新增) */
  supplyShowNew = '';

  /** 服务区域 */
  svcRegion = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** 交接单流程ID */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** 销售--客服交接单 */
  transferRemark = '';

  /** 差旅服务费比例% */
  travelServicesRatio = '';

  /** 交接上传文件名 */
  uploadFileName = '';

  /** 交接上传URL */
  uploadUrl = '';

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];

  /** 职场健康 预付款金额 */
  whAdvancePaymentAmt = '';

  /** 职场健康 预付款时间 */
  whAdvancePaymentDt = '';

  /** 提成销售 */
  whCommissionSale = '';

  /** 提成销售name */
  whCommissionSaleName = '';

  /** 职场健康 合同寄送地址 */
  whContractSendAddress = '';

  /** 职场健康 预计垫付时长（天） */
  whExpectedPrepayDay = '';

  /** 职场健康 尾款金额 */
  whFinalPaymentAmt = '';

  /** 职场健康 尾款时间 */
  whFinalPaymentDt = '';

  /** 职场健康 开票顺序 */
  whInvoiceOrder = '';

  /** 开票顺序name */
  whInvoiceOrderName = '';

  /** 职场健康 项目编号 */
  whItemCode = '';

  /** 职场健康 毛利率% */
  whMargin = '';

  /** 体检税率% */
  whPeRate = '';

  /** 职场健康 垫付备注 */
  whPrepayRemark = '';

  /** 职场健康 采购发票内容 */
  whPurchaseInvoiceContent = '';

  /** 职场健康 采购发票类型 */
  whPurchaseInvoiceType = '';

  /** 职场健康 采购发票类型name */
  whPurchaseInvoiceTypeName = '';

  /** 职场健康 返佣收入 */
  whRebateIncome = '';

  /** 职场健康 返佣税费 */
  whRebateTax = '';

  /** 职场健康 销售发票内容 */
  whSaleInvoiceContent = '';

  /** 职场健康 销售发票类型 */
  whSaleInvoiceType = '';

  /** 职场健康 销售发票类型name */
  whSaleInvoiceTypeName = '';

  /** 职场健康 支付供货商货款时间 */
  whSupplierPaymentDt = '';

  /** 工作流id */
  workitemId = '';
}

class ContractFile {
  /** 审批节点 */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同附件ID */
  contractFileId = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 附件ID */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 附件路径 */
  filePath = '';

  /** 附件类型 */
  fileType = '';

  /** 附件类型name */
  fileTypeName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程defId */
  processDefId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传步骤 */
  uploadStep = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ContractQuery {
  /** 流程节点ID */
  activityDefId = '';

  /** 新增驳回原因list */
  addSlDisaReasonList = [];

  /** 审批通过日期到 */
  approveDtEnd = '';

  /** 审批通过日期从 */
  approveDtStart = '';

  /** 区域类型 */
  areaType = '';

  /** 合同类别 */
  contractCategery = '';

  /** 合同编号 */
  contractCode = '';

  /** 最终结束日期从 */
  contractEndDateFrom = '';

  /** 最终结束日期到 */
  contractEndDateTo = '';

  /** 合同名称 */
  contractName = '';

  /** 开始时间到 */
  contractStartDateEnd = '';

  /** 开始时间从 */
  contractStartDateStart = '';

  /** 合同审批状态 */
  contractStatus = '';

  /** 合同小类 */
  contractSubType = '';

  /** 合同状态 */
  contractSvcState = '';

  /** 合同状态集合 */
  contractSvcStateList = [];

  /** 合同大类 */
  contractType = '';

  /** 合同类别 */
  contractVersionType = '';

  /** 创建人 */
  createByName = '';

  /** 创建人查询条件 */
  createByParty = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 现销售 */
  currentSales = '';

  /** 客户编号 */
  custCode = '';

  /** 客户名称 */
  custName = '';

  /** 客户显示编号 */
  custViewCode = '';

  /** 驳回原因批次 */
  disaBatchId = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否增强型代理 */
  enhancedAgent = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售 */
  formerSales = '';

  /** 现销售所属大区 */
  governingArea = '';

  /** 现销售所属分公司 */
  governingBranch = '';

  /** 是否为已有客户所推荐 */
  isCustRecommend = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** processDefId */
  processDefId = '';

  /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
  queryType = '';

  /** 客服反馈撤单预警日期大于等于 */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单预警日期小于等于 */
  reportGlEvacuatedDate = '';

  /** 卡权限(1：客户权限，3：订单权限也就是小合同权限) */
  restrictType = '';

  /** 风险金比例% */
  riskPremiumRatio = '';

  /** 风险分担比例% */
  riskSharingRatio = '';

  /** 签约方公司抬头 */
  signBranchTitleId = '';

  /** 驳回原因list */
  slDisaReasonList = [];

  /** startIndex */
  startIndex = undefined;

  /** 终止服务日期从 */
  stopSvcDt = '';

  /** 终止服务日期到 */
  stopSvcEndDt = '';

  /** 修改驳回原因list */
  uptSlDisaReasonList = [];
}

class ContractRetiree {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 人员姓名 */
  bz = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 大合同ID号 */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 人员姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 身份证号 */
  idCardNum = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 大合同退休人员主键 */
  retireeId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CrmBusinessLog {
  /** add */
  add = false;

  /** 批次 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户原名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** ePropertyName */
  ePropertyName = '';

  /** endIndex */
  endIndex = undefined;

  /** executeDate */
  executeDate = '';

  /** executeDateEd */
  executeDateEd = '';

  /** executeDateSt */
  executeDateSt = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** exportType */
  exportType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 主键 */
  logId = '';

  /** 模拟人 */
  mimicBy = '';

  /** newAreaId */
  newAreaId = '';

  /** newAreaText */
  newAreaText = '';

  /** newCityName */
  newCityName = '';

  /** newDeptName */
  newDeptName = '';

  /** newDeptPre */
  newDeptPre = '';

  /** newSaleId */
  newSaleId = '';

  /** 现销售 */
  newSaler = '';

  /** newexists */
  newexists = '';

  /** newgoverningBranch */
  newgoverningBranch = '';

  /** newgoverningBranchText */
  newgoverningBranchText = '';

  /** noChange */
  noChange = false;

  /** oldAreaId */
  oldAreaId = '';

  /** oldAreaText */
  oldAreaText = '';

  /** oldCityName */
  oldCityName = '';

  /** oldDeptName */
  oldDeptName = '';

  /** oldDeptPre */
  oldDeptPre = '';

  /** oldSaleId */
  oldSaleId = '';

  /** 原销售 */
  oldSaler = '';

  /** oldexists */
  oldexists = '';

  /** oldgoverningBranch */
  oldgoverningBranch = '';

  /** oldgoverningBranchText */
  oldgoverningBranchText = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 产品线创建时间 */
  plCreatDt = '';

  /** 产品线修改时间 */
  plUpdateDt = '';

  /** 客户产品线主键ID */
  proKeyId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品线 */
  productLine = '';

  /** 产品线名称 */
  productName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realName */
  realName = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 类型ID	  <p>	  101: 客户建立	  <p>	  102:客户删除	  <p>	  103:客户进入共享	  <p>	  104:正在跟进分配	  <p>	  105:客户名称修改	  <p>	  106:共享区分配	  <p>	  107:删除区分配	  <p>	  108:客户基本信息修改	  <P>	  109:市场活动预录入客户分配 */
  typeId = '';

  /** typeName */
  typeName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CrmContractPayerDTO {
  /** 发票抬头 */
  checkTitle = '';

  /** 合同ID */
  contractId = '';

  /** 合同与客户付款方关系id */
  contractPayerId = '';

  /** 付款方表ID */
  custPayerId = '';

  /** 付款方名称 */
  payerName = '';
}

class CrmContractQuotationDTO {
  /** 合同ID */
  contractId = '';

  /** 合同绑定报价单id */
  contractQuotationId = '';

  /** 报价单编号 */
  quotationCode = '';

  /** 报价单id */
  quotationId = '';

  /** 报价单名称 */
  quotationName = '';

  /** 总的销售价格 */
  quotationTotalSalPrice = '';

  /** 备注 */
  remark = '';

  /** 报价单状态 */
  status = '';

  /** 客户补医保投保人数 */
  suppltMedInsurHeadCount = '';
}

class CrmLittleContractProductlineDTO {
  /** 平均价格 */
  averageMoney = '';

  /** 人数 */
  compactNumber = '';

  /** 合同ID */
  contractId = '';

  /** 0无效 1有效 */
  isEnable = '';

  /** 产品线id */
  productlineId = '';

  /** 产品线名称 */
  productlineName = '';

  /** 大合同产品线子表id */
  smcontractProductlineId = '';
}

class CrmPage {
  /** pageNum */
  pageNum = undefined;

  /** pageSize */
  pageSize = undefined;

  /** rows */
  rows = [];

  /** total */
  total = undefined;

  /** totalPage */
  totalPage = undefined;
}

class CustLogQuery {
  /** 执行城市 */
  cityId = '';

  /** 竞争对手 */
  competitorId = '';

  /** loseId */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** expType */
  expType = '';

  /** 预计签约日期至 */
  expectedSignDateEd = '';

  /** 预计签约日期起 */
  expectedSignDateSt = '';

  /** 签约人数至 */
  expectedSignNumEd = '';

  /** 签约人数从 */
  expectedSignNumSt = '';

  /** 价格区间至 */
  expectedSignPriceEd = '';

  /** 价格区间从 */
  expectedSignPriceSt = '';

  /** 丢单日期到 */
  loseDtEd = '';

  /** 丢单日期从 */
  loseDtSt = '';

  /** 丢单类型 */
  loseType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 大区 */
  productlineSalerAreaId = '';

  /** 分公司 */
  productlineSalerProviderId = '';

  /** 销售 */
  productlineSalername = '';

  /** startIndex */
  startIndex = undefined;

  /** 截止日期 */
  trackDateEd = '';

  /** 起始日期 */
  trackDateSt = '';

  /** 拜访阶段 */
  visitStage = '';
}

class CustLoseReason {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 竞争对手 */
  competitorId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custId */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 丢单日期 */
  loseDt = '';

  /** 主键 */
  loseId = '';

  /** 丢单原因 */
  loseResaon = '';

  /** 丢单类型 */
  loseType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustProductLine {
  /** custId */
  custId = '';

  /** productLine */
  productLine = [];
}

class CustReportQuery {
  /** 大区id */
  areaId = '';

  /** 客户城市 */
  cityName = '';

  /** 客户编号 */
  custCode = '';

  /** 创建日期到 */
  custDtEnd = '';

  /** 创建日期从 */
  custDtFrom = '';

  /** custIsDeleted */
  custIsDeleted = '';

  /** 客户名称 */
  custName = '';

  /** 产品线获取日期到 */
  custProductLindDtEnd = '';

  /** 产品线获取日期从 */
  custProductLindDtFrom = '';

  /** 客户状态 */
  custStatus = '';

  /** 分公司id */
  departmentId = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 销售姓名 */
  saleName = '';

  /** startIndex */
  startIndex = undefined;
}

class CustTrack {
  /** add */
  add = false;

  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 沟通内容摘要 */
  commuContent = '';

  /** 沟通目标 */
  commuObjective = '';

  /** 沟通结果及跟进计划 */
  commuResult = '';

  /** 竞争对手--------(服务信息字段结束) */
  competitor = '';

  /** 联系电话 */
  contactCell = '';

  /** 联系人性别 */
  contactGender = '';

  /** 联系人 */
  contacter = '';

  /** 联系人手机 */
  contacterPhone = '';

  /** 联系人职位 */
  contacterPost = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建时间到 */
  createDtEnd = '';

  /** 创建时间从 */
  createDtStart = '';

  /** 创建人姓名 */
  creatorName = '';

  /** 客户编号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 我心目中的客户分级 */
  custLevelInSale = '';

  /** 客户名称 */
  custName = '';

  /** 主键 */
  custTrackId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户决策人员角色 */
  customerDecisionRole = '';

  /** 客户关注点 */
  customerFocus = '';

  /** del */
  del = false;

  /** departmentId */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 邮件 */
  email = '';

  /** 员工编号 */
  empCode = '';

  /** 订单id/上下岗id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 执行城市 */
  execCityId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 预计签约日期 */
  expectedSignDate = '';

  /** 预计签约人数 */
  expectedSignNum = '';

  /** 预计签约价格 */
  expectedSignPrice = '';

  /** 传真--------(基本信息卡字段结束) */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 关注点描述 */
  focusDesc = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号码 */
  idCardNum = '';

  /** 非正式客户id */
  ifmCustId = '';

  /** 主键 */
  ifmCustTrackId = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 下一步工作安排 */
  nextWorkPlan = '';

  /** noChange */
  noChange = false;

  /** 原hro供应商 */
  oldHroPrvd = '';

  /** ������id.���ڼ�¼��־ʹ�� */
  oldSalesId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** proLineIdStr */
  proLineIdStr = '';

  /** proLineStr */
  proLineStr = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注-------------(销售跟进字段结束) */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 销售所需支持 */
  salersSupport = '';

  /** salesId */
  salesId = '';

  /** salesIdFilterTrack */
  salesIdFilterTrack = '';

  /** 销售名称 */
  salesName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态,0:未生效(默认),1:生效 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 服务类型字符串 */
  svcTypeStr = '';

  /** 跟进日期 */
  trackDate = '';

  /** trackDateEnd */
  trackDateEnd = '';

  /** trackDateStart */
  trackDateStart = '';

  /** 1,售前<br>	  2,售后 */
  trackType = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 更新人姓名 */
  updaterName = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 拜访阶段 */
  visitStage = '';

  /** visitStageName */
  visitStageName = '';
}

class Customer {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** contact1Post */
  contact1Post = '';

  /** contactTel */
  contactTel = '';

  /** corpId */
  corpId = '';

  /** countryId */
  countryId = '';

  /** createDt */
  createDt = '';

  /** crmCustId */
  crmCustId = '';

  /** custCode */
  custCode = '';

  /** custEnglishName */
  custEnglishName = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** custShortName */
  custShortName = '';

  /** ePropertyId */
  ePropertyId = '';

  /** eSizeId */
  eSizeId = '';

  /** email */
  email = '';

  /** haveBranches */
  haveBranches = '';

  /** hrContract */
  hrContract = '';

  /** industryId */
  industryId = '';

  /** organizationCode */
  organizationCode = '';

  /** reqAquirementDt */
  reqAquirementDt = '';

  /** reqAquirementTypeId */
  reqAquirementTypeId = '';

  /** salesId */
  salesId = '';

  /** salesName */
  salesName = '';

  /** sourceId */
  sourceId = '';

  /** updateDt */
  updateDt = '';

  /** userName */
  userName = '';

  /** workAddress */
  workAddress = '';
}

class CustomerLost {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** loseId */
  competitorId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** loseId */
  custCode = '';

  /** loseId */
  custId = '';

  /** loseId */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** loseId */
  loseDt = '';

  /** loseId */
  loseDtEd = '';

  /** loseId */
  loseDtSt = '';

  /** loseId */
  loseId = '';

  /** loseId */
  loseResaon = '';

  /** loseId */
  loseType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CustomerPayerDTO {
  /** 开户名 */
  accountName = '';

  /** 审核日期 */
  approveDt = '';

  /** 审核状态:0待修改,1待审批,2审批通过 */
  approveStatus = '';

  /** 审核人 */
  approver = '';

  /** 帐号 */
  bankAcct = '';

  /** 开户行名称 */
  bankName = '';

  /** 1：工行  0：他行 */
  bankType = '';

  /** 帐单年月 0 收当月 -1收上月 */
  billMon = '';

  /** 发票抬头 */
  checkTitle = '';

  /** 城市id */
  cityId = '';

  /** 联系人 */
  contact = '';

  /** 联系地址 */
  contactAddress = '';

  /** 电子邮件 */
  contactEmail = '';

  /** 传真 */
  contactFax = '';

  /** 联系电话 */
  contactTel1 = '';

  /** 联系电话 */
  contactTel2 = '';

  /** 邮政编码 */
  contactZipCode = '';

  /** 客户资质:1,一般纳税人、2小规模纳税人 */
  custAptitude = '';

  /** 客户资质Name */
  custAptitudeText = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户付款方表id */
  custPayerId = '';

  /** 附件id */
  fileId = '';

  /** 附件名称 */
  fileName = '';

  /** 发票地址 */
  invoiceAddress = '';

  /** 特殊开发票说明 */
  invoiceDesc = '';

  /** 电子发票邮箱 */
  invoiceEmail = '';

  /** 发票电话 */
  invoiceTel = '';

  /** 删除标志 */
  isDeleted = '';

  /** 是否开特殊发票0: 否1:是 */
  isInvoice = '';

  /** 付款方名称 */
  payerName = '';

  /** 省份id */
  provinceId = '';

  /** 代理人 */
  proxyBy = '';

  /** 备注 */
  remark = '';

  /** 纳税人识别号 */
  taxpayerIdentifier = '';
}

class CustomerPayerQuery {
  /** 客户id */
  custId = '';

  /** 付款方编号 */
  custPayerId = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 付款方名称 */
  payerName = '';

  /** startIndex */
  startIndex = undefined;
}

class CustomerVisitQuery {
  /** accountsBill */
  accountsBill = '';

  /** areaId */
  areaId = '';

  /** branchId */
  branchId = '';

  /** competitors */
  competitors = '';

  /** competitorsServiceinfo */
  competitorsServiceinfo = '';

  /** competitorsStrong */
  competitorsStrong = '';

  /** continuedTime */
  continuedTime = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** customerNeed */
  customerNeed = '';

  /** customerQuestion */
  customerQuestion = '';

  /** customerVisitId */
  customerVisitId = '';

  /** deptttype */
  deptttype = '';

  /** endIndex */
  endIndex = undefined;

  /** enterpriseScale */
  enterpriseScale = '';

  /** estimatedTime */
  estimatedTime = '';

  /** followingInfo */
  followingInfo = '';

  /** followingInfoEdit */
  followingInfoEdit = '';

  /** isExecute */
  isExecute = '';

  /** isExecuteStr */
  isExecuteStr = '';

  /** jointMember */
  jointMember = '';

  /** jointMemberStr */
  jointMemberStr = '';

  /** memberCount */
  memberCount = '';

  /** optimizationMethods */
  optimizationMethods = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** realName */
  realName = '';

  /** serviceCitys */
  serviceCitys = '';

  /** serviceCount */
  serviceCount = '';

  /** serviceCountIndaili */
  serviceCountIndaili = '';

  /** serviceCountInproxy */
  serviceCountInproxy = '';

  /** serviceProducts */
  serviceProducts = '';

  /** setCustomerAlias */
  setCustomerAlias = '';

  /** startIndex */
  startIndex = undefined;

  /** visitAddress */
  visitAddress = '';

  /** visitDate */
  visitDate = '';

  /** visitDateEd */
  visitDateEd = '';

  /** visitor */
  visitor = '';
}

class DebtReminderCriteria {
  /** add */
  add = false;

  /** agreedWageArriveDay */
  agreedWageArriveDay = '';

  /** amtReceiveMon */
  amtReceiveMon = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** billCreator */
  billCreator = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.BILL_MONTH	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  billMonth = '';

  /** billType */
  billType = '';

  /** billVersion */
  billVersion = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cashDt */
  cashDt = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractCode */
  contractCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.CONTRACT_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  contractId = '';

  /** contractName */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** currentGoverningArea */
  currentGoverningArea = '';

  /** currentGoverningAreaId */
  currentGoverningAreaId = '';

  /** currentGoverningBranch */
  currentGoverningBranch = '';

  /** currentGoverningBranchId */
  currentGoverningBranchId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.CURRENT_SALES	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  currentSales = '';

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.CUST_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.DEBT_REMINDER_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  debtReminderId = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.IS_SEND_MAIL	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.EXP_PAYMENT_String	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  expPaymentDate = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** finReceivableYm */
  finReceivableYm = '';

  /** formerGoverningArea */
  formerGoverningArea = '';

  /** formerGoverningAreaId */
  formerGoverningAreaId = '';

  /** formerGoverningBranch */
  formerGoverningBranch = '';

  /** formerGoverningBranchId */
  formerGoverningBranchId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.FORMER_SALES	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  formerSales = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** minorAdjustment */
  minorAdjustment = '';

  /** noChange */
  noChange = false;

  /** overdrafAmt */
  overdrafAmt = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** payeeBranch */
  payeeBranch = '';

  /** payeeBranchId */
  payeeBranchId = '';

  /** payerBranch */
  payerBranch = '';

  /** payerBranchId */
  payerBranchId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  reason = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON_CREATE_BY	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  reasonCreateBy = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON_CREATE_DT	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  reasonCreateDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON_TYPE	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  reasonType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.RECEIVABLE_AMT	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  receivableAmt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.EF_RECEIVABLE_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  receivableId = '';

  /** receivableTemplate */
  receivableTemplate = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.EF_RECEIVABLE_TEMPLT_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  receivableTempltId = '';

  /** remindDate */
  remindDate = '';

  /** remindEndDt */
  remindEndDt = '';

  /** remindStartDt */
  remindStartDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REMIND_String	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  remindString = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REMINDER_RECIPIENT	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  reminderRecipient = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REMINDER_RECIPIENT_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  reminderRecipientId = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.STATUS	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
  status = '';

  /** statusName */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** verifyAmt */
  verifyAmt = '';

  /** verifyStatus */
  verifyStatus = '';
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class FilterEntity {
  /** activityNameCn */
  activityNameCn = '';

  /** activityNameEn */
  activityNameEn = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByStr */
  createByStr = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** disaBatchId */
  disaBatchId = '';

  /** disaReasonId */
  disaReasonId = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** reasonBz */
  reasonBz = '';

  /** reasonId */
  reasonId = '';

  /** reasonStr */
  reasonStr = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** workitemId */
  workitemId = '';
}

class FormalCust {
  /** 市场活动编号 */
  activityCode = '';

  /** 市场活动id */
  activityId = '';

  /** 市场活动名称 */
  activityName = '';

  /** add */
  add = false;

  /** approveOpinion */
  approveOpinion = '';

  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 银行账号 */
  bankAcct = '';

  /** 开户行名称 */
  bankName = '';

  /** 银行开户名 */
  bankNum = '';

  /** 所属银行, 取值为数据字典type=911记录中base_data_code值. */
  bankType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 大客户申请时间 */
  bigCompanyApplyDate = '';

  /** 大客户申请人 */
  bigCompanyApplyMan = '';

  /** 大客户审批意见 */
  bigCompanyAudit = '';

  /** 大客户审核时间 */
  bigCompanyAuditDate = '';

  /** 大客户审核人 */
  bigCompanyAuditMan = '';

  /** 大客户处理原由 */
  bigCompanyCause = '';

  /** 大客户处理流程实例id */
  bigCompanyProcessId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务结束时间 */
  bizEndDt = '';

  /** 业务范围 */
  bizScope = '';

  /** 业务开始时间 */
  bizStartDt = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 渠道ID */
  channelId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 公司所在城市编码 */
  companyCityId = '';

  /** 公司所在城市 */
  companyCityName = '';

  /** 联系人1职位 */
  contact1Post = '';

  /** 联系人2职位 */
  contact2Post = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人手机2 */
  contactCell2 = '';

  /** 联系人性别1 */
  contactGender1 = '';

  /** 联系人性别2 */
  contactGender2 = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 核心业务需求 */
  coreBizReqId = '';

  /** 核心业务需求 */
  coreBizReqName = '';

  /** 钉钉客户编号 */
  corpId = '';

  /** 国家ID */
  countryId = '';

  /** 国家Name */
  countryName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建时间到 */
  createDtEnd = '';

  /** 创建时间从 */
  createDtStart = '';

  /** 创建人姓名 */
  creatorName = '';

  /** 客户资信ID */
  creditHisId = '';

  /** 客户资信Name */
  creditHisName = '';

  /** 客户办公地址所在区县ID （前期可不用） */
  csOfficeCountyId = '';

  /** 客户办公地址所在区县Name */
  csOfficeCountyName = '';

  /** 客户编码 */
  custCode = '';

  /** 客户英文名称 */
  custEnglishName = '';

  /** 客户id */
  custId = '';

  /** 客户中文名称 */
  custName = '';

  /** 客户缩写名称 */
  custShortName = '';

  /** 新增<br>	  从陌生拜访转移 */
  custTransferStatus = '';

  /** 客户类型:0 直销 1 渠道,2:市场 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 分公司id */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 分支机构分布情况 */
  distributionBranches = '';

  /** 企业性质ID */
  ePropertyId = '';

  /** 企业性质Name */
  ePropertyName = '';

  /** 企业规模ID */
  eSizeId = '';

  /** 企业规模Name */
  eSizeName = '';

  /** 联系人邮件 */
  email = '';

  /** 联系人邮件2 */
  email2 = '';

  /** 员工编号 */
  empCode = '';

  /** 订单id/上下岗id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 比较的客户名称 */
  equalCustName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 联系人传真 */
  fax = '';

  /** 联系人传真2 */
  fax2 = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 成立月份 */
  foundMonth = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 如果与company_id相同，表示自己是同一个集团，如果是不一样的，表示是某个集团下的一个成员，程序需要控制只有两层关系 */
  groupId = '';

  /** 是否有分支机构 ：1有，0 否' */
  haveBranches = '';

  /** 历史客户编号 */
  hisFormalCust = {};

  /** 人力资源联系人 */
  hrContract = '';

  /** 证件号码 */
  idCardNum = '';

  /** 导入批次号 */
  impBatchId = '';

  /** inId */
  inId = '';

  /** 所属行业ID */
  industryId = '';

  /** 所属行业Name */
  industryName = '';

  /** 保险联系人 */
  insuranceContact = '';

  /** 保险联系电话 */
  insuranceContactTel = '';

  /** 情况简介 */
  introduction = '';

  /** 是否是大公司 */
  isBigCompany = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否签约合同<br>	  1是<br>	  0否 */
  isContract = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否删除 */
  isDeletedText = '';

  /** 是否上市ID */
  isPublicTradedId = '';

  /** 是否上市Name */
  isPublicTradedName = '';

  /** 是否签约 */
  isSign = '';

  /** 是否签约text */
  isSignText = '';

  /** 1:未删除0:已删除2:缓存状态 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 法人代表 */
  legalRep = '';

  /** 备注 */
  memo = '';

  /** 微博公众号 */
  microblogPublicAccount = '';

  /** 模拟人 */
  mimicBy = '';

  /** 审批意见 */
  newApproveOpinion = '';

  /** noChange */
  noChange = false;

  /** ������id.���ڼ�¼��־ʹ�� */
  oldSalesId = '';

  /** 组织机构代码 */
  organizationCode = '';

  /** 组织备注 */
  organizationRemark = '';

  /** 使用过外包服务商ID */
  outSvcProviderId = '';

  /** 使用过外包服务商Name */
  outSvcProviderName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 审批人 */
  participant = '';

  /** 产品线获取日期到 */
  proLineGetDtEnd = '';

  /** 产品线获取日期从 */
  proLineGetDtStart = '';

  /** 产品id串 */
  proLineIdStr = '';

  /** proLineIdStrRemain */
  proLineIdStrRemain = '';

  /** proLinePKStr */
  proLinePKStr = '';

  /** 产品线销售id */
  proLineSalesId = '';

  /** 销售姓名 */
  proLineSalesName = '';

  /** 产品线 */
  proLineStr = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 非正式客户id */
  prospectiveCustId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 注册地址 */
  regAddress = '';

  /** 注册资金（万） */
  regCapital = '';

  /** 注册到期日期 */
  regEndDt = '';

  /** 注册开始日期 */
  regStartDt = '';

  /** 相关业务需求 */
  relaReqId = '';

  /** 相关业务需求 */
  relaReqName = '';

  /** 获得需求时间 */
  reqAquirementDt = '';

  /** 需求获得方式ID */
  reqAquirementTypeId = '';

  /** 需求获得方式Name */
  reqAquirementTypeName = '';

  /** 需求备注 */
  reqRemark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 销售人员所在城市编码 */
  salesCityId = '';

  /** 销售人员所在城市 */
  salesCityName = '';

  /** 所属销售人员 */
  salesCode = '';

  /** 销售组 */
  salesGroup = '';

  /** 客户编号 */
  salesId = '';

  /** 销售人员名字 */
  salesName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 客户来源ID */
  sourceId = '';

  /** 客户来源Name */
  sourceName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** tag */
  tag = '';

  /** 临时客户编号 */
  tempCustCode = '';

  /** 已经恢复次数 */
  timesOfRecovery = '';

  /** 转移时间 */
  transTime = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 更新人姓名 */
  updaterName = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 访问状态 */
  visitStatus = '';

  /** 网址 */
  website = '';

  /** 微信公众号 */
  wechatPublicAccount = '';

  /** 办公地址 */
  workAddress = '';

  /** 邮政编码（办公地址） */
  zipCode = '';
}

class FormalCustVo {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** contact1Post */
  contact1Post = '';

  /** contactTel */
  contactTel = '';

  /** corpId */
  corpId = '';

  /** crmCustId */
  crmCustId = '';

  /** custCode */
  custCode = '';

  /** custEnglishName */
  custEnglishName = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** custShortName */
  custShortName = '';

  /** ePropertyId */
  ePropertyId = '';

  /** ePropertyName */
  ePropertyName = '';

  /** eSizeId */
  eSizeId = '';

  /** eSizeName */
  eSizeName = '';

  /** haveBranches */
  haveBranches = '';

  /** hrContract */
  hrContract = '';

  /** industryId */
  industryId = '';

  /** industryName */
  industryName = '';

  /** organizationCode */
  organizationCode = '';

  /** productLine */
  productLine = [];

  /** prospectiveCustId */
  prospectiveCustId = '';

  /** reqAquirementDt */
  reqAquirementDt = '';

  /** reqAquirementTypeId */
  reqAquirementTypeId = '';

  /** reqAquirementTypeName */
  reqAquirementTypeName = '';

  /** salesCode */
  salesCode = '';

  /** salesId */
  salesId = '';

  /** salesName */
  salesName = '';

  /** sourceId */
  sourceId = '';

  /** sourceName */
  sourceName = '';

  /** userName */
  userName = '';

  /** workAddress */
  workAddress = '';
}

class FormalQuery {
  /** 大区 */
  areaId = '';

  /** 分公司id */
  branchId = '';

  /** 客户所在城市 */
  companyCityId = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** 飞正式客户id */
  ifmCustId = '';

  /** 删除状态 */
  isDeleted = '';

  /** 销售是否签约 */
  isSign = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品线获取日期到 */
  proLineGetDtEnd = '';

  /** 产品线获取日期从 */
  proLineGetDtStart = '';

  /** 产品线销售id */
  proLineSalesId = '';

  /** 销售名字 */
  proLineSalesName = '';

  /** salesIdFilterTrack */
  salesIdFilterTrack = '';

  /** startIndex */
  startIndex = undefined;

  /** 跟进日期到 */
  trackDateEnd = '';

  /** 跟进日期从 */
  trackDateStart = '';
}

class GlobalResult {
  /** code */
  code = '';

  /** data */
  data = new CrmPage();

  /** message */
  message = '';
}

class HsSlCustomerVisit {
  /** accountsBill */
  accountsBill = '';

  /** add */
  add = false;

  /** areaId */
  areaId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchId */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** competitors */
  competitors = '';

  /** competitorsServiceinfo */
  competitorsServiceinfo = '';

  /** competitorsStrong */
  competitorsStrong = '';

  /** continuedTime */
  continuedTime = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** customerNeed */
  customerNeed = '';

  /** customerQuestion */
  customerQuestion = '';

  /** customerVisitId */
  customerVisitId = '';

  /** del */
  del = false;

  /** deptttype */
  deptttype = '';

  /** endIndex */
  endIndex = undefined;

  /** enterpriseScale */
  enterpriseScale = '';

  /** estimatedTime */
  estimatedTime = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** followingInfo */
  followingInfo = '';

  /** followingInfoEdit */
  followingInfoEdit = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isExecute */
  isExecute = '';

  /** isExecuteStr */
  isExecuteStr = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** jointMember */
  jointMember = '';

  /** jointMemberStr */
  jointMemberStr = '';

  /** memberCount */
  memberCount = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** optimizationMethods */
  optimizationMethods = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realName */
  realName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceCitys */
  serviceCitys = '';

  /** serviceCount */
  serviceCount = '';

  /** serviceCountIndaili */
  serviceCountIndaili = '';

  /** serviceCountInproxy */
  serviceCountInproxy = '';

  /** serviceProducts */
  serviceProducts = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** visitAddress */
  visitAddress = '';

  /** visitDate */
  visitDate = '';

  /** visitDateEd */
  visitDateEd = '';

  /** visitor */
  visitor = '';
}

class IfmCust {
  /** 市场活动编号 */
  activityCode = '';

  /** 市场活动id */
  activityId = '';

  /** 市场活动名称 */
  activityName = '';

  /** add */
  add = false;

  /** 审批状态:<br>	  0:初始(默认)<br>	  0:初始<br>	  1:提交审核,<br>	  2:者转化成功,<br>	  -1:转化失败, */
  approvalStatus = '';

  /** approvalStatusName */
  approvalStatusName = '';

  /** approveOpinion */
  approveOpinion = '';

  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 银行账号 */
  bankAcct = '';

  /** 开户行名称 */
  bankName = '';

  /** 银行开户名 */
  bankNum = '';

  /** 所属银行, 取值为数据字典type=911记录中base_data_code值. */
  bankType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 大客户申请时间 */
  bigCompanyApplyDate = '';

  /** 大客户申请人 */
  bigCompanyApplyMan = '';

  /** 大客户审批意见 */
  bigCompanyAudit = '';

  /** 大客户审核时间 */
  bigCompanyAuditDate = '';

  /** 大客户审核人 */
  bigCompanyAuditMan = '';

  /** 大客户处理原由 */
  bigCompanyCause = '';

  /** 大客户处理流程实例id */
  bigCompanyProcessId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务结束时间 */
  bizEndDt = '';

  /** 业务范围 */
  bizScope = '';

  /** 业务开始时间 */
  bizStartDt = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 同一个客户名称在审批过程中存在的次数 */
  cNameReptCount = '';

  /** 渠道ID */
  channelId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 公司所在城市编码 */
  companyCityId = '';

  /** 公司所在城市 */
  companyCityName = '';

  /** 联系人1职位 */
  contact1Post = '';

  /** 联系人2职位 */
  contact2Post = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人手机2 */
  contactCell2 = '';

  /** 联系人性别1 */
  contactGender1 = '';

  /** 联系人性别2 */
  contactGender2 = '';

  /** 联系人电话 */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 核心业务需求 */
  coreBizReqId = '';

  /** 核心业务需求 */
  coreBizReqName = '';

  /** 钉钉客户编号 */
  corpId = '';

  /** 国家ID */
  countryId = '';

  /** 国家Name */
  countryName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 创建时间到 */
  createDtEnd = '';

  /** 创建时间从 */
  createDtStart = '';

  /** 创建人姓名 */
  creatorName = '';

  /** 客户资信ID */
  creditHisId = '';

  /** 客户资信Name */
  creditHisName = '';

  /** 客户办公地址所在区县ID （前期可不用） */
  csOfficeCountyId = '';

  /** 客户办公地址所在区县Name */
  csOfficeCountyName = '';

  /** currentOwnerSalesId */
  currentOwnerSalesId = '';

  /** 客户编码 */
  custCode = '';

  /** 客户英文名称 */
  custEnglishName = '';

  /** 客户id */
  custId = '';

  /** 客户中文名称 */
  custName = '';

  /** 客户缩写名称 */
  custShortName = '';

  /** 客户状态(客户为市场客户的时候存值):<br>	  1,新增<br>	  2,已有未签约<br>	  3,已有已签约 */
  custStatus = '';

  /** 新增<br>	  从陌生拜访转移 */
  custTransferStatus = '';

  /** 客户类型:0 直销 1 渠道,2:市场 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 分公司id */
  departmentId = '';

  /** departmentName */
  departmentName = '';

  /** 分支机构分布情况 */
  distributionBranches = '';

  /** 企业性质ID */
  ePropertyId = '';

  /** 企业性质Name */
  ePropertyName = '';

  /** 企业规模ID */
  eSizeId = '';

  /** 企业规模Name */
  eSizeName = '';

  /** 联系人邮件 */
  email = '';

  /** 联系人邮件2 */
  email2 = '';

  /** 员工编号 */
  empCode = '';

  /** 订单id/上下岗id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 员工姓名 */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 比较的客户名称 */
  equalCustName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 联系人传真 */
  fax = '';

  /** 联系人传真2 */
  fax2 = '';

  /** fileId */
  fileId = '';

  /** fileIds */
  fileIds = [];

  /** fileName */
  fileName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 成立月份 */
  foundMonth = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 如果与company_id相同，表示自己是同一个集团，如果是不一样的，表示是某个集团下的一个成员，程序需要控制只有两层关系 */
  groupId = '';

  /** 是否有分支机构 ：1有，0 否' */
  haveBranches = '';

  /** 历史客户编号 */
  hisFormalCust = new FormalCust();

  /** 人力资源联系人 */
  hrContract = '';

  /** 证件号码 */
  idCardNum = '';

  /** 客户id */
  ifmCustId = '';

  /** 非正式客户类别,<br>	  1:陌拜客户<br>	  2:注册客户<br>	  3:市场客户 */
  ifmCustType = '';

  /** 导入批次号 */
  impBatchId = '';

  /** inId */
  inId = '';

  /** 所属行业ID */
  industryId = '';

  /** 所属行业Name */
  industryName = '';

  /** 保险联系人 */
  insuranceContact = '';

  /** 保险联系电话 */
  insuranceContactTel = '';

  /** 情况简介 */
  introduction = '';

  /** 是否是大公司 */
  isBigCompany = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否签约合同<br>	  1是<br>	  0否 */
  isContract = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否删除 */
  isDeletedText = '';

  /** 是否上市ID */
  isPublicTradedId = '';

  /** 是否上市Name */
  isPublicTradedName = '';

  /** 是否签约 */
  isSign = '';

  /** 是否签约text */
  isSignText = '';

  /** 1:未删除0:已删除2:缓存状态 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 法人代表 */
  legalRep = '';

  /** 备注 */
  memo = '';

  /** 微博公众号 */
  microblogPublicAccount = '';

  /** 模拟人 */
  mimicBy = '';

  /** 审批意见 */
  newApproveOpinion = '';

  /** noChange */
  noChange = false;

  /** ������id.���ڼ�¼��־ʹ�� */
  oldSalesId = '';

  /** 组织机构代码 */
  organizationCode = '';

  /** 组织备注 */
  organizationRemark = '';

  /** 使用过外包服务商ID */
  outSvcProviderId = '';

  /** 使用过外包服务商Name */
  outSvcProviderName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 审批人 */
  participant = '';

  /** 产品线获取日期到 */
  proLineGetDtEnd = '';

  /** 产品线获取日期从 */
  proLineGetDtStart = '';

  /** 产品id串 */
  proLineIdStr = '';

  /** proLineIdStrRemain */
  proLineIdStrRemain = '';

  /** proLinePKStr */
  proLinePKStr = '';

  /** 产品线销售id */
  proLineSalesId = '';

  /** 销售姓名 */
  proLineSalesName = '';

  /** 产品线 */
  proLineStr = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程实例id */
  processInsId = '';

  /** 非正式客户id */
  prospectiveCustId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 注册地址 */
  regAddress = '';

  /** 注册资金（万） */
  regCapital = '';

  /** 注册到期日期 */
  regEndDt = '';

  /** 注册开始日期 */
  regStartDt = '';

  /** 相关业务需求 */
  relaReqId = '';

  /** 相关业务需求 */
  relaReqName = '';

  /** 获得需求时间 */
  reqAquirementDt = '';

  /** 需求获得方式ID */
  reqAquirementTypeId = '';

  /** 需求获得方式Name */
  reqAquirementTypeName = '';

  /** 需求备注 */
  reqRemark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 销售人员所在城市编码 */
  salesCityId = '';

  /** 销售人员所在城市 */
  salesCityName = '';

  /** 所属销售人员 */
  salesCode = '';

  /** 销售组 */
  salesGroup = '';

  /** 客户编号 */
  salesId = '';

  /** 销售人员名字 */
  salesName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 客户来源ID */
  sourceId = '';

  /** 客户来源Name */
  sourceName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** tag */
  tag = '';

  /** 临时客户编号 */
  tempCustCode = '';

  /** 已经恢复次数 */
  timesOfRecovery = '';

  /** 转移时间 */
  transTime = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 更新人姓名 */
  updaterName = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 访问状态 */
  visitStatus = '';

  /** 网址 */
  website = '';

  /** 微信公众号 */
  wechatPublicAccount = '';

  /** 办公地址 */
  workAddress = '';

  /** workitemId */
  workitemId = '';

  /** 邮政编码（办公地址） */
  zipCode = '';
}

class IfmCustQuery {
  /** 市场活动名称 */
  activityName = '';

  /** 大区 */
  areaId = '';

  /** 分公司id */
  branchId = '';

  /** 客户所在城市 */
  companyCityId = '';

  /** 注册日期到 */
  createDtEnd = '';

  /** 注册日期从 */
  createDtStart = '';

  /** 当前销售id */
  currentOwnerSalesId = '';

  /** 客户名称 */
  custName = '';

  /** 分公司 */
  departmentId = '';

  /** endIndex */
  endIndex = undefined;

  /** 非正式客户类型 */
  ifmCustType = '';

  /** 删除状态 */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 流程操作人 */
  participant = '';

  /** 销售 */
  salesName = '';

  /** startIndex */
  startIndex = undefined;
}

class Map {}

class MarketActivityQuery {
  /** 举办城市 */
  activityCityId = '';

  /** 市场活动编号 */
  activityCode = '';

  /** 预估费用额 */
  activityCost = '';

  /** 主键 */
  activityId = '';

  /** 市场活动名称 */
  activityName = '';

  /** custCount */
  custCount = '';

  /** 结束日期 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 说明 */
  remark = '';

  /** 开始日期 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';
}

class ModelContract {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** createByName */
  createByName = '';

  /** 创建日期 */
  createDt = '';

  /** createDtFrom */
  createDtFrom = '';

  /** createDtTo */
  createDtTo = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.EFFECT_BY           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  effectBy = '';

  /** effectByName */
  effectByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.EFFECT_DT           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  effectDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.FILE_CODE           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  fileCode = '';

  /** fileName */
  fileName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.FILE_PATH           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  filePath = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.MODEL_CONTRACT_ID           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  modelContractId = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.REMARK           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.STATUS           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  status = '';

  /** statusName */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.UN_EFFECT_BY           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  unEffectBy = '';

  /** unEffectByName */
  unEffectByName = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.UN_EFFECT_DT           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
  unEffectDt = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ModelContractDTO {
  /** 创建人名称 */
  createByName = '';

  /** 创建日期起 */
  createDtFrom = '';

  /** 创建日期到 */
  createDtTo = '';

  /** 生效人 */
  effectBy = '';

  /** 生效人name */
  effectByName = '';

  /** 生效日期 */
  effectDt = '';

  /** 内部编号 */
  fileCode = '';

  /** 文件名称 */
  fileName = '';

  /** 附件路径 */
  filePath = '';

  /** 范本合同表id */
  modelContractId = '';

  /** 备注 */
  remark = '';

  /** 状态 0:未生效 1:生效 2:失效 */
  status = '';

  /** 状态name */
  statusName = '';

  /** 失效人 */
  unEffectBy = '';

  /** 失效人name */
  unEffectByName = '';

  /** 失效日期 */
  unEffectDt = '';
}

class ModelContractQuery {
  /** 上传日期从 */
  createDtFrom = '';

  /** 上传日期到 */
  createDtTo = '';

  /** endIndex */
  endIndex = undefined;

  /** 文件名称 */
  fileName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class NonStaCoctApprDTO {
  /** 流程节点表的活动英文名称 */
  activityNameEn = '';

  /** 申请单编号 */
  applyCode = '';

  /** 申请见票付款 0 否 1 是 */
  applyTicketPay = '';

  /** 审核意见(暂不用) */
  approvalOpinion = '';

  /** 审批意见 */
  approvalRemark = '';

  /** 审批通过日期 */
  approveDt = '';

  /** 审批通过日期从 */
  approveDtFrom = '';

  /** 审批通过日期到 */
  approveDtTo = '';

  /** 当前审批步骤 */
  approveName = '';

  /** 销售所属大区 */
  areaName = '';

  /** 销售所属分公司 */
  branchName = '';

  /** 城市id */
  cityId = '';

  /** 合同类型 */
  contractType = '';

  /** 创建人name */
  createByName = '';

  /** 申请日期从 */
  createDtFrom = '';

  /** 申请日期到 */
  createDtTo = '';

  /** 现销售 */
  currentSales = '';

  /** 现销售Name */
  currentSalesName = '';

  /** 客户编号 */
  custCode = '';

  /** 客户服务费低于供应商服务费 0 否 1 是 */
  custFeeUnderSupplierFee = '';

  /** 客户服务费低于供应商服务费涉及城市 */
  custFeeUnderSupplierFeeCi = '';

  /** 客户服务费低于供应商服务费服务收费 */
  custFeeUnderSupplierFeeFe = '';

  /** 客户服务费低于供应商服务费签约人数 */
  custFeeUnderSupplierFeeNu = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 暂缓收取或免缴工本费、采暖费等费用 0 否 1 是 */
  deferredNoCostHeating = '';

  /** 暂缓收取或免缴工本费、采暖费等费用涉及城市 */
  deferredNoCostHeatingCity = '';

  /** 暂缓收取或免缴工本费、采暖费等费用服务收费 */
  deferredNoCostHeatingFee = '';

  /** 暂缓收取或免缴工本费、采暖费等费用签约人数 */
  deferredNoCostHeatingNum = '';

  /** 暂缓收取或免缴残障金 0 否 1 是 */
  deferredNoDisabledGold = '';

  /** 暂缓收取或免缴残障金涉及城市 */
  deferredNoDisabledGoldCity = '';

  /** 暂缓收取或免缴残障金服务收费 */
  deferredNoDisabledGoldFee = '';

  /** 暂缓收取或免缴残障金签约人数 */
  deferredNoDisabledGoldNum = '';

  /** 部门id */
  deptId = '';

  /** 上门服务 0 否 1 是 */
  doorToDoorService = '';

  /** 驻场客服 0 否 1 是 */
  fieldCustomerService = '';

  /** 驻场客服附件 */
  fieldCustomerServiceFid = '';

  /** 是否驻场客服Name */
  fieldCustomerServiceName = '';

  /** 5人以下客户非季度付费 0 否 1 是 */
  fivePeopleCust = '';

  /** 5人以下客户非季度付费附件 */
  fivePeopleCustFid = '';

  /** 是否5人以下客户非季度付费Name */
  fivePeopleCustName = '';

  /** 销售所属大区 */
  governingArea = '';

  /** 销售所属分公司 */
  governingBranch = '';

  /** 审批是否通过 */
  isApproval = '';

  /** 审批单总体是否通过 0 否 1 是 */
  isApprovalAll = '';

  /** 审批单总体是否通过Name */
  isApprovalAllName = '';

  /** 约定不开具发票 0 否 1 是 */
  noInvoice = '';

  /** 约定不开具发票附件 */
  noInvoiceFid = '';

  /** 是否约定不开具发票Name */
  noInvoiceName = '';

  /** 不缴纳公积金 0 否 1 是 */
  nonPaymentFund = '';

  /** 不缴纳公积金附件 */
  nonPaymentFundFid = '';

  /** 是否不缴纳公积金Name */
  nonPaymentFundName = '';

  /** 非标合同审批单表id */
  nonStaCoctApprId = '';

  /** 非标准账期 1 新建合同审批 2 已有合同更改回款日或收费频率 */
  nonStandardBillDate = '';

  /** 非标准账期附件 */
  nonStandardBillDateFid = '';

  /** 非标准账期Name */
  nonStandardBillDateName = '';

  /** 协助办理退休产品，低于成本价或非标准服务 0 否 1 是 */
  nonStandardService = '';

  /** 审批人 */
  participant = '';

  /** 代发工资时间为月末最后一个工作日 0 否 1 是 */
  payTimeLastDay = '';

  /** 代发工资时间为月末最后一个工作日附件 */
  payTimeLastDayFid = '';

  /** 是否代发工资时间为月末最后一个工作日Name */
  payTimeLastDayName = '';

  /** 工资发放地与个税缴纳地不一致 0 否 1 是 */
  paymentTaxDiffer = '';

  /** 工资发放地与个税缴纳地不一致附件 */
  paymentTaxDifferFid = '';

  /** 是否工资发放地与个税缴纳地不一致Name */
  paymentTaxDifferName = '';

  /** 流程实例创建人 */
  piCreateBy = '';

  /** 流程实例id */
  processInsId = '';

  /** 备注 */
  remark = '';

  /**  深圳集中投保 0 否 1 是 */
  shenzhenCentralInsurance = '';

  /** 深圳集中投保附件 */
  shenzhenCentralInsuranceFid = '';

  /** 是否深圳集中投保Name */
  shenzhenCentralInsuranceName = '';

  /** 签约方、付款方、发票抬头三者不一致 0 否 1 是 */
  signPayBillDiffer = '';

  /** 签约方、付款方、发票抬头三者不一致附件 */
  signPayBillDifferFid = '';

  /** 是否签约方、付款方、发票抬头三者不一致Name */
  signPayBillDifferName = '';

  /** 特殊工资卡 1 正常代发工资卡银行 2 其他银行 */
  specialSalaryCard = '';

  /** 0 初始、1 审批中、2 退回修改、3 审批通过、4 流程结束 */
  status = '';

  /** 审批单状态 */
  statusName = '';

  /** 落地发工资、落地报个税 0 否 1 是 */
  wagesTax = '';

  /** 与客户签订反委托代发工资协议，工资实发由客户自己发，个税由易才大户申报。 */
  wagesTax1 = '';

  /** 工资由客户自己发，个税通过易才大户申报 */
  wagesTax2 = '';

  /** 工资由客户自己发，要求易才提供通过客户报税系统代为申报 */
  wagesTax3 = '';

  /** 落地发工资、落地报个税附件 */
  wagesTaxFid = '';

  /** 是否落地发工资Name */
  wagesTaxName = '';

  /** 工作流id */
  workItemId = '';
}

class NonStaCoctApprQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class ProductLine {
  /** createDt */
  createDt = '';

  /** custId */
  custId = '';

  /** isValid */
  isValid = '';

  /** productLineMappingId */
  productLineMappingId = '';

  /** productline */
  productline = '';

  /** productlineCompanyId */
  productlineCompanyId = '';

  /** productlineId */
  productlineId = '';

  /** productlineSalerId */
  productlineSalerId = '';

  /** productlineSalerName */
  productlineSalerName = '';

  /** productlineSalerProviderId */
  productlineSalerProviderId = '';

  /** productlineSalerProvidername */
  productlineSalerProvidername = '';

  /** productlineStatus */
  productlineStatus = undefined;

  /** productlineType */
  productlineType = '';

  /** respCode */
  respCode = '';

  /** respMessage */
  respMessage = '';

  /** salerId */
  salerId = '';

  /** updateDt */
  updateDt = '';

  /** userName */
  userName = '';
}

class QuotationItem {
  /** add */
  add = false;

  /** approvePrice */
  approvePrice = undefined;

  /** 附加税 */
  at = undefined;

  /** 附加税的税率% */
  ator = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 预计数量 */
  countNum = undefined;

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 毛利率 */
  gmr = undefined;

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.IS_ORDER_UPDATE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isOrderUpdate = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** isYearsToPay */
  isYearsToPay = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 标准售价 */
  priceAt = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productCost = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productPrice = undefined;

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationGroupItemId */
  quotationGroupItemId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ITEM_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationItemId = undefined;

  /** quotationItemType */
  quotationItemType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 标准售价（含附加税） */
  salesPrice = undefined;

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 总成本 */
  totalCost = undefined;

  /** 总售价 */
  totalPrice = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;

  /** virtualProductId */
  virtualProductId = undefined;
}

class QuotationItemDetail {
  /** approvePrice */
  approvePrice = undefined;

  /** 附加税 */
  at = undefined;

  /** 附加税的税率% */
  ator = undefined;

  /** atr */
  atr = undefined;

  /** 预计数量 */
  countNum = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.QUOTATION_ITEM_DETAIL_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  detailType = undefined;

  /** 毛利率 */
  gmr = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.IF_FOR_THIRDPART	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifForThirdpart = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.IS_YEARS_TO_PAY	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isYearsToPay = undefined;

  /** 标准售价 */
  priceAt = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productCost = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_DESC	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productDesc = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_LINE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productLineId = undefined;

  /** productLineName */
  productLineName = '';

  /** productName */
  productName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  productPrice = undefined;

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationGroupItemDetailId */
  quotationGroupItemDetailId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** quotationItemDetailId */
  quotationItemDetailId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ITEM_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationItemId = undefined;

  /** quotationItemType */
  quotationItemType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_ITEM_DETAIL.QUOTATION_TEMPLATE_INFECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTemplateInfectId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_ITEM_DETAIL.QUOTATION_TEMPLT_CITY_FACTOR	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTempltCityFactor = undefined;

  /** quotationTempltName */
  quotationTempltName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SALES_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  salesPrice = undefined;

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** subLadder */
  subLadder = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SUPLMT_MED_END_DT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suplmtMedEndDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SUPLMT_MED_START_DT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suplmtMedStartDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SVC_AREA	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  svcArea = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.TEMPLATE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  templateId = undefined;

  /** templtScope */
  templtScope = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.TEMPLT_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  templtType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.THIRD_PARTY_PROVIDER_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  thirdPartyProviderId = undefined;

  /** thirdPartyProviderName */
  thirdPartyProviderName = '';

  /** 总成本 */
  totalCost = undefined;

  /** 总售价 */
  totalPrice = undefined;

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;

  /** virtualProductId */
  virtualProductId = undefined;
}

class QuotationLadder {
  /** add */
  add = false;

  /** 附加税 */
  at = undefined;

  /** 附加税的税率% */
  ator = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.BEGIN_NUMBER	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  beginNumber = undefined;

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.END_NUMBER	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  endNumber = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  price = undefined;

  /** 标准售价（含附加税） */
  priceAt = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** productId */
  productId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_TEMPLATE_INFECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTemplateInfectId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationType = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** salesPriceNoTax */
  salesPriceNoTax = undefined;

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;
}

class QuotationSpecial {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.BPO_PROJECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  bpoProjectId = undefined;

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  price = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_SPECIAL_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationType = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class QuotationSpecialProperty {
  /** add */
  add = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.AMT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  amt = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.CUSTOMIZE_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  customizeType = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_SPECIAL_PROPERTY_HIS	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialPropertyHis = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_SPECIAL_PROPERTY_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialPropertyId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  type = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SaleReportQuery {
  /** 统计年月（审批通过时间） */
  approveDt = '';

  /** 账单月份 */
  billYm = '';

  /** 创建时间到 */
  createDtEd = '';

  /** 创建时间从 */
  createDtSt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户名称 */
  custName = '';

  /** endIndex */
  endIndex = undefined;

  /** 现销售所属大区 */
  newAreaId = '';

  /** 现销售姓名 */
  newSaleName = '';

  /** 现销售所属分公司 */
  newgoverningBranch = '';

  /** 原销售所属大区 */
  oldAreaId = '';

  /** 原销售姓名 */
  oldSaleName = '';

  /** 原销售所属分公司 */
  oldgoverningBranch = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 客户服务情况 */
  queryType = '';

  /** startIndex */
  startIndex = undefined;
}

class SendMailDTO {
  /** 邮件内容 */
  mailContent = '';

  /** 邮件标题 */
  mailTitle = '';

  /** 邮件收件人，支持多个逗号分隔 */
  saleEmails = '';

  /** 销售id多个 */
  saleIds = '';
}

class ShareAreaDTO {
  /** list */
  list = [];

  /** newSaleId */
  newSaleId = '';

  /** operateType */
  operateType = undefined;
}

class ShareAreaProductLines {
  /** add */
  add = false;

  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 客户城市 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 创建日期到 */
  custDtEnd = '';

  /** 客户创建时间从 */
  custDtFrom = '';

  /** 客户id */
  custId = '';

  /** 删除状态 */
  custIsDeleted = '';

  /** 客户名称 */
  custName = '';

  /** 产品线获取日期到 */
  custProductLindDtEnd = '';

  /** 客户产品线创建时间从 */
  custProductLindDtFrom = '';

  /** 【客户所处状态】：1正在跟进、0删除区、2共享区 */
  custStatus = '';

  /** 【注册类型】：正式客户、陌拜客户、市场活动 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 删除时间到 */
  delDtEnd = '';

  /** 删除时间从 */
  delDtFrom = '';

  /** 部门id */
  departmentId = '';

  /** 部门名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 上传关联协议 */
  fileId = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 客户所属行业 */
  industryName = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否跨区 0或空否   1是  主要控制一些页面不限制分公司 一些限制 */
  isLocal = '';

  /** 判断销售是否就是当前登录人 */
  isMe = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 【市场活动】：市场活动名称； */
  marketName = '';

  /** 最长公示天数，用于判断销售是否可以拾取自己的,>=10可拾取 */
  maxShareDays = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 操作类型2 共享区   0删除区 */
  operateType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品线创建时间 */
  productLinCreateDt = '';

  /** 产品线共享时间 */
  productLinShareDt = '';

  /** 产品线id */
  productLineId = '';

  /** 产品线映射表ID */
  productLineMappingId = '';

  /** 逗号分隔的客户产品线ID，用户按客户分配，存储客户销售的所有产品线 */
  productLineMappingIds = '';

  /** 产品线名称 */
  productLineName = '';

  /** 公示区产品线格式如代理(20120618,20120818),派遣(20120618,20120818),Payroll(20120618,20120818),新派遣(20120618,20120818),租赁(20120618,20120818),保险(20120618,20120818),福利(20120618,20120818),一次性产品(20120618,20120818),咨询(20120618,20120818),招聘(20120618,20120818) */
  productLines = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 销售id */
  saleId = '';

  /** 销售名称 */
  saleName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 共享时间到 */
  shareDtEnd = '';

  /** 共享时间从 */
  shareDtFrom = '';

  /** 客户来源 */
  sourceName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ShareAreaProductLinesQuery {
  /** 大区id */
  areaId = '';

  /** 大区名称 */
  areaName = '';

  /** 客户城市 */
  cityName = '';

  /** 客户编号 */
  custCode = '';

  /** 创建日期到 */
  custDtEnd = '';

  /** 客户创建时间从 */
  custDtFrom = '';

  /** 客户id */
  custId = '';

  /** 删除状态 */
  custIsDeleted = '';

  /** 客户名称 */
  custName = '';

  /** 产品线获取日期到 */
  custProductLindDtEnd = '';

  /** 客户产品线创建时间从 */
  custProductLindDtFrom = '';

  /** 【客户所处状态】：1正在跟进、0删除区、2共享区 */
  custStatus = '';

  /** 【注册类型】：正式客户、陌拜客户、市场活动 */
  custType = '';

  /** 删除时间到 */
  delDtEnd = '';

  /** 删除时间从 */
  delDtFrom = '';

  /** 部门id */
  departmentId = '';

  /** 部门名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 上传关联协议 */
  fileId = '';

  /** 客户所属行业 */
  industryName = '';

  /** 是否跨区 0或空否   1是  主要控制一些页面不限制分公司 一些限制 */
  isLocal = '';

  /** 判断销售是否就是当前登录人 */
  isMe = '';

  /** 【市场活动】：市场活动名称； */
  marketName = '';

  /** 最长公示天数，用于判断销售是否可以拾取自己的,>=10可拾取 */
  maxShareDays = '';

  /** 操作类型2 共享区   0删除区 */
  operateType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 产品线创建时间 */
  productLinCreateDt = '';

  /** 产品线共享时间 */
  productLinShareDt = '';

  /** 产品线id */
  productLineId = '';

  /** 产品线映射表ID */
  productLineMappingId = '';

  /** 逗号分隔的客户产品线ID，用户按客户分配，存储客户销售的所有产品线 */
  productLineMappingIds = '';

  /** 产品线名称 */
  productLineName = '';

  /** 公示区产品线格式如代理(20120618,20120818),派遣(20120618,20120818),Payroll(20120618,20120818),新派遣(20120618,20120818),租赁(20120618,20120818),保险(20120618,20120818),福利(20120618,20120818),一次性产品(20120618,20120818),咨询(20120618,20120818),招聘(20120618,20120818) */
  productLines = '';

  /** 销售id */
  saleId = '';

  /** 销售名称 */
  saleName = '';

  /** 共享时间到 */
  shareDtEnd = '';

  /** 共享时间从 */
  shareDtFrom = '';

  /** 客户来源 */
  sourceName = '';

  /** startIndex */
  startIndex = undefined;
}

class UptCustLogDTO {
  /** 客户对象 */
  formalCust = new FormalCust();

  /** 日志类型 */
  type = '';
}

class cityQuery {
  /** 客户编码 */
  custCode = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class contractVO {
  /** 审批步骤 */
  activityNameCn = '';

  /** 流程节点表的活动英文名称 */
  activityNameEn = '';

  /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
  activityStatus = '';

  /** 预付款比例 */
  advancePaymentRatio = '';

  /** 代收代付 */
  agentBusiness = '';

  /** 约定到款月 */
  agereedAmtReceiveMon = '';

  /** 薪资发放日/约定薪资发放日 */
  agreedPayDt = '';

  /** 约定到款日 */
  agreedWageArriveDay = '';

  /** 非标合同审批单code */
  applyCode = '';

  /** 审批通过时间 */
  approveDt = '';

  /** 审批通过时间到 */
  approveDtEnd = '';

  /** 审批通过时间从 */
  approveDtStart = '';

  /** 页面填写的审核意见 */
  approveOpinion = '';

  /** 合同审核相关的附件 */
  approveRelatedAttachment = '';

  /** 合同审核相关的附件name */
  approveRelatedAttachmentName = '';

  /** 大区ID */
  areaId = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaType = '';

  /** 区域类型 1 本地 2 大区内 3 全国 */
  areaTypeName = '';

  /** 终稿 */
  attTypeDraftId = '';

  /** 终稿name */
  attTypeDraftName = '';

  /** 法务 */
  attTypeLegalId = '';

  /** 法务name */
  attTypeLegalName = '';

  /** 平均价格集合 */
  averageMoneys = '';

  /** 账单日期 */
  billDt = '';

  /** 城市 */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 提交时间 */
  commitTime = '';

  /** 签约人数集合 */
  compactNumbers = '';

  /** 竞争对手id */
  competitor = '';

  /** 客服竞争对手名称 */
  competitorName = '';

  /** 已经确认的工作流程 */
  confirmdWorkFlow = '';

  /** 联系人手机 */
  contactCell = '';

  /** 联系人电话 */
  contactTel = '';

  /** 签约人均金额 */
  contractAvgAmt = '';

  /** 合同类别 */
  contractCategery = '';

  /** 合同编号 */
  contractCode = '';

  /** 最终结束日期 */
  contractEndDate = '';

  /** 合同最终结束日期类型 */
  contractEndDateType = '';

  /** 合同文件名 */
  contractFileName = '';

  /** 合同附件备注 */
  contractFileRemark = '';

  /** 合同附件上传时间 */
  contractFileUploadDt = '';

  /** 签约人数 */
  contractHeadcount = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 合同甲方 */
  contractPartA = '';

  /** 合同乙方 */
  contractPartB = '';

  /** 合同产品线id集合 */
  contractProductLineIds = '';

  /** 客户盖章后，合同回收时间 */
  contractRetrieveDt = '';

  /** 合同起始日期 */
  contractStartDate = '';

  /** 合同启动时间止 */
  contractStartDateEnd = '';

  /** 合同启动日期起 */
  contractStartDateStart = '';

  /** 合同状态：0 初始；1 审批中；2 审批通过；3 退回修改；4 驳回终止 */
  contractStatus = '';

  /** 合同状态name */
  contractStatusName = '';

  /** 合同结束日期 */
  contractStopDate = '';

  /** 合同终止原因 */
  contractStopReason = '';

  /** 合同小类 */
  contractSubType = '';

  /** 合同类别（子类）名称 */
  contractSubTypeName = '';

  /** 合同服务状态：0新签1续签2过期3终止服务 */
  contractSvcState = '';

  /** 合同服务状态name */
  contractSvcStateName = '';

  /** 合同模板编号 */
  contractTemplateId = '';

  /** 新平台合同终止时填写的终止时间 */
  contractTerminationDate = '';

  /** 合同大类 */
  contractType = '';

  /** 合同类型名 */
  contractTypeName = '';

  /** 合同版本号 */
  contractVersion = '';

  /** 创建人 */
  createByName = '';

  /** 创建人 */
  createByParty = '';

  /** 创建日期到 */
  createDtEnd = '';

  /** 创建日期从 */
  createDtStart = '';

  /** 合同生成方式 */
  createType = '';

  /** 账期（天） */
  creditPeriod = '';

  /** 客服审批 */
  csApproval = '';

  /** 客服审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  csApprovalStatus = '';

  /** 客服联系人地址 */
  cstScAddress = '';

  /** 客服联系人手机 */
  cstScCall = '';

  /** 客服联系人 */
  cstScContact = '';

  /** 客服联系人邮件 */
  cstScEmail = '';

  /** 客服联系人电话 */
  cstScTel = '';

  /** 客服联系人传真 */
  cstScfax = '';

  /** 客服联系人职位 */
  cstScposition = '';

  /** 必须是同一个客户，当前执行的合同的编号，如果续签多次，这个编号是最新的合同编号 */
  currentExeContractId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户唯一号 */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户盖章时间 */
  custSealDt = '';

  /** 供应商ID */
  departmentId = '';

  /** 供应商名称 */
  departmentName = '';

  /** 草稿备注 */
  draftRemark = '';

  /** 联系人邮件 */
  email = '';

  /** EOS账号停用 */
  eosStatus = '';

  /** 预估首次账单日期 */
  estimateFirstBillDate = '';

  /** 预计12个月内可达到人数 */
  estimatedHeadcount = '';

  /** 执行成本 */
  executionCost = '';

  /** 预计增长人数 */
  expectedIncrease = '';

  /** 原预计增长人数 */
  expectedIncreaseOld = '';

  /** 联系人传真 */
  fax = '';

  /** 首次出账单的客户账单年月 */
  firstAccountMonth = '';

  /** 首次出账单时间(锁定时间) */
  firstBillDate = '';

  /** 首次大合同ID */
  firstContractId = '';

  /** 合同审批的首个法务 */
  firstLegalApproveId = '';

  /** 合同审批的首个法务名称 */
  firstLegalApproveName = '';

  /** 首次出账单的财务应收年月 */
  firstOughtMonth = '';

  /**  合同审批的首个易薪税审批人员 */
  firstWgApproveId = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属大区名称 */
  formerGoverningAreaName = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售所属分公司名称 */
  formerGoverningBranchName = '';

  /** 原销售 */
  formerSales = '';

  /** 原销售名字 */
  formerSalesName = '';

  /** 未来商机 */
  furtureOpportunity = '';

  /** 所属大区 */
  governingArea = '';

  /** 现销售所属大区名称 */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 现销售所属分公司名称 */
  governingBranchName = '';

  /** 毛利 */
  grossProfit = '';

  /** 集团公司编号 */
  groupId = '';

  /** 集团公司名称 */
  groupName = '';

  /** 是否有交接单 */
  hasTransferInfo = '';

  /** 人力资源联系人 */
  hrContract = '';

  /** 导入文件ID */
  importFileId = '';

  /** 导入文件名称 */
  importFileName = '';

  /** 收入 */
  income = '';

  /** 内支金额 */
  internalMoney = '';

  /** 开票金额 */
  invoiceMoney = '';

  /** 开票张数 */
  invoiceNum = '';

  /** 滞纳金比例是否为万分之五 1：是；0：否 */
  is5Per10000FineRate = '';

  /** 滞纳金比例是否为万分之五name */
  is5Per10000FineRateName = '';

  /** 是否有补充附件 */
  isAddedAttachment = '';

  /** 本次续签是否需要调整合同条款？ */
  isAdjustRenewContract = '';

  /** 是否派单 */
  isAssign = '';

  /** 是否要提交审核 */
  isCommitApprove = '';

  /** 是否为已有客户所推荐 1：是；0：否 */
  isCustRecommend = '';

  /** 是否为已有客户所推荐name */
  isCustRecommendName = '';

  /** 是否降价、垫付、账期延期 1:是,0:否 */
  isDefer = '';

  /** 是否降价、垫付、账期延期名称 */
  isDeferName = '';

  /** 是否开通EOS账号 */
  isEosAccount = '';

  /** 是否内支 */
  isInternalPayment = '';

  /** 是否代发薪资 */
  isIssuingSalary = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
  isJoinCompensation = '';

  /** 是否加入包含具体金额的对等条款或赔偿性条款name */
  isJoinCompensationName = '';

  /** 质控计算结果是否为垫付 1：是；0：否 */
  isPaymentQAResult = '';

  /** 质控计算结果是否为垫付name */
  isPaymentQAResultName = '';

  /** 服务人数小于20人，是否季度付款 1：是；0：否 */
  isQuarterlyPaymentLess20 = '';

  /** 服务人数小于20人，是否季度付款name */
  isQuarterlyPaymentLess20Name = '';

  /** 是否抢单 */
  isRob = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保name */
  isSameInsurName = '';

  /** 是否二次开发 1:是,0:否 */
  isSecondaryDev = '';

  /** 是否二次开发名称 */
  isSecondaryDevName = '';

  /** 法务审批 */
  legalApproval = '';

  /** 法务审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  legalApprovalStatus = '';

  /** 合同附件备注 */
  legalRemark = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 责任客服名字 */
  liabilityCsName = '';

  /** 会议记录id */
  meetingRecordId = '';

  /** 会议记录上传附件id */
  meetingRecordImportFileId = '';

  /** 会议记录上传附件名称 */
  meetingRecordImportFileName = '';

  /** 备注 */
  memo = '';

  /** 范本修改版合同备注 */
  modelModifyVersionRemark = '';

  /** 新销售 */
  newSales = '';

  /** 续签合同ID, 存放续签的大合同ID */
  nextContractId = '';

  /** 续签合同名称 */
  nextContractName = '';

  /** 下个法务 */
  nextLegalApproveId = '';

  /** 非标合同审批单 */
  nonStaCoctApprId = '';

  /** 服务订单数 */
  orderNumber = '';

  /** 父合同id编号 */
  parentContractId = '';

  /** 付款和收款要点 */
  payCollectPoint = '';

  /** 薪资发放月 */
  payMonth = '';

  /** 缴费类型 */
  payType = '';

  /** 客户付款方id集合 */
  payerIds = '';

  /** 付款方式 */
  paymentMode = '';

  /** 体检预估成本 */
  peExecutionCost = '';

  /** 体检毛利 */
  peGrossProfit = '';

  /** 体检收入 */
  peIncome = '';

  /** 垫款额度 */
  prepayAmt = '';

  /** 垫付审批 */
  prepayApproval = '';

  /** 垫款审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  prepayApprovalStatus = '';

  /** 流程定义id */
  processDefId = '';

  /** 对应的流程实例ID */
  processInstanceId = '';

  /** 产品线id集合 */
  productLineIdLogs = '';

  /** 产品线id集合 */
  productLineIds = '';

  /** 项目前期计划或实施要求 */
  projectPlanRequest = '';

  /** 全国项目交接表单 */
  projectRemark = '';

  /** 供应商类型 */
  providerType = '';

  /** QA审核意见 */
  qaApprove = '';

  /** 报价单集合id */
  quoIds = '';

  /** 被续签的旧合同号 */
  renewedContractNum = '';

  /** 客服反馈撤单预警日期大于等于 */
  reportElEvacuatedDate = '';

  /** 客服反馈撤单时间 */
  reportEvacuatedDate = '';

  /** 客服撤单详细原因说明 */
  reportEvacuatedExplantion = '';

  /** 客服撤单原因分类 */
  reportEvacuatedReason = '';

  /** 客服反馈撤单预警日期小于等于 */
  reportGlEvacuatedDate = '';

  /** 回访历史内容 */
  returnVisitMemo = '';

  /** 最后回访人Id */
  returnVisitorId = '';

  /** 统计标志位 */
  salFlag = '';

  /** 新增/存量标识 （手工） */
  salFlagManual = '';

  /** 1 纯新增,2 存量,3 纯新增/存量,4 纯新增+滚动存量,5 滚动存量,6 滚动存量+存量 */
  salFlagManualName = '';

  /** 新增/存量标识 （系统）：1历史纯新增、2滚动新增、3存量、4当月启动纯新增、-1未有首版账单 */
  salFlagName = '';

  /** 客户对应销售及分公司 */
  saleAndBranchName = '';

  /** 销售审批 */
  salesApprove = '';

  /** 销售报价审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
  salesApproveStatus = '';

  /** 销售所在主部门 */
  salesDeptName = '';

  /** 销售名字 */
  salesName = '';

  /** 所属销售团队类型 */
  salesTeamType = '';

  /** 客服竞争对手优势 */
  sctScComAdvancetage = '';

  /** 用章审核状态 0：初始态3：通过 -3：退回修改 -4：驳回终止 */
  sealApproveStatus = '';

  /** 公司盖章时间 */
  sealDt = '';

  /** 用章审核 */
  sealOpinion = '';

  /** 签约方公司抬头 */
  signBranchTitle = '';

  /** 签约方公司抬头id */
  signBranchTitleId = '';

  /** 新签标识（手工） */
  signFlagManual = '';

  /** 新签标识（手工）name */
  signFlagManualName = '';

  /** 签单分公司 */
  signProvider = '';

  /** 撤单原因 */
  stopReason = '';

  /** 终止服务系统操作时间 */
  stopSvcDt = '';

  /** 终止服务操作日期 查询条件：终止服务日期到 */
  stopSvcEndDt = '';

  /** 服务区域 */
  svcRegion = '';

  /** 税费 */
  tax = '';

  /** 总售价 */
  totalPrice = '';

  /** 交接单流程ID */
  tranferProcessId = '';

  /** 交接单id */
  transferId = '';

  /** 销售--客服交接单 */
  transferRemark = '';

  /** 交接上传文件名 */
  uploadFileName = '';

  /** 交接上传URL */
  uploadUrl = '';

  /** 工作流id */
  workitemId = '';
}

class customerServiceDTO {
  /** 统计年月（审批通过时间） */
  approveDt = '';

  /** 原销售所属分公司 */
  areaName = '';

  /** 账单月份 */
  billYm = '';

  /** 原销售所属大区 */
  brcName = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同名称 */
  contractName = '';

  /** contractamt */
  contractamt = '';

  /** 创建时间到 */
  createDtEd = '';

  /** 创建时间从 */
  createDtSt = '';

  /** 现销售所属大区 */
  currentAreaName = '';

  /** 现销售所属分公司 */
  currentBrcName = '';

  /** 现销售 */
  currentSalesName = '';

  /** 客户编号 */
  custCode = '';

  /** 客户名称 */
  custName = '';

  /** 服务人数 */
  empCount = '';

  /** 原销售 */
  formerSalesName = '';

  /** 现销售所属大区 */
  newAreaId = '';

  /** 现销售姓名 */
  newSaleName = '';

  /** 现销售所属分公司 */
  newgoverningBranch = '';

  /** 原销售所属大区 */
  oldAreaId = '';

  /** 原销售姓名 */
  oldSaleName = '';

  /** 原销售所属分公司 */
  oldgoverningBranch = '';

  /** 服务费 */
  serviceAmt = '';

  /** 已核销金额 */
  verfiyAmt = '';
}

class customerSimilarityDTO {
  /** 客户A编号 */
  aCode = '';

  /** 客户A名称 */
  aName = '';

  /** 客户A产品线 */
  aProductLine = '';

  /** 客户B编号 */
  bCode = '';

  /** 客户B名称 */
  bName = '';

  /** 客户B产品线 */
  bProductLine = '';
}

class debtReminderCriteriaQuery {
  /** agreedWageArriveDay */
  agreedWageArriveDay = '';

  /** amtReceiveMon */
  amtReceiveMon = '';

  /** billCreator */
  billCreator = '';

  /** 客户账单年月 */
  billMonth = '';

  /** billType */
  billType = '';

  /** billVersion */
  billVersion = '';

  /** cashDt */
  cashDt = '';

  /** 合同编号 */
  contractCode = '';

  /** contractId */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 现销售所属大区 */
  currentGoverningArea = '';

  /** currentGoverningAreaId */
  currentGoverningAreaId = '';

  /** 现销售所属分公司 */
  currentGoverningBranch = '';

  /** currentGoverningBranchId */
  currentGoverningBranchId = '';

  /** 现销售 */
  currentSales = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** debtReminderId */
  debtReminderId = '';

  /** email */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 预计回款日期  */
  expPaymentDate = '';

  /** finReceivableYm */
  finReceivableYm = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** formerGoverningAreaId */
  formerGoverningAreaId = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** formerGoverningBranchId */
  formerGoverningBranchId = '';

  /** 原销售 */
  formerSales = '';

  /** minorAdjustment */
  minorAdjustment = '';

  /** overdrafAmt */
  overdrafAmt = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 收款方分公司 */
  payeeBranch = '';

  /** payeeBranchId */
  payeeBranchId = '';

  /** payerBranch */
  payerBranch = '';

  /** payerBranchId */
  payerBranchId = '';

  /** 对应原因 */
  reason = '';

  /** reasonCreateBy */
  reasonCreateBy = '';

  /** reasonCreateDt */
  reasonCreateDt = '';

  /** 原因类型 */
  reasonType = '';

  /** receivableAmt */
  receivableAmt = '';

  /** receivableId */
  receivableId = '';

  /** receivableTemplate */
  receivableTemplate = '';

  /** receivableTempltId */
  receivableTempltId = '';

  /** 提醒时间 */
  remindDate = '';

  /** remindEndDt */
  remindEndDt = '';

  /** remindStartDt */
  remindStartDt = '';

  /** remindString */
  remindString = '';

  /** 提醒接收人 */
  reminderRecipient = '';

  /** reminderRecipientId */
  reminderRecipientId = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 处理状态 */
  statusName = '';

  /** verifyAmt */
  verifyAmt = '';

  /** verifyStatus */
  verifyStatus = '';
}

class debtReminderQuery {
  /** agreedWageArriveDay */
  agreedWageArriveDay = '';

  /** amtReceiveMon */
  amtReceiveMon = '';

  /** billCreator */
  billCreator = '';

  /** 客户账单年月 */
  billMonth = '';

  /** billType */
  billType = '';

  /** billVersion */
  billVersion = '';

  /** cashDt */
  cashDt = '';

  /** 合同编号 */
  contractCode = '';

  /** contractId */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** 现销售所属大区 */
  currentGoverningArea = '';

  /** 现销售所属分公司 */
  currentGoverningBranch = '';

  /** 现销售 */
  currentSales = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** debtReminderId */
  debtReminderId = '';

  /** email */
  email = '';

  /** endIndex */
  endIndex = undefined;

  /** 预计回款日期  */
  expPaymentDate = '';

  /** finReceivableYm */
  finReceivableYm = '';

  /** 原销售所属大区 */
  formerGoverningArea = '';

  /** 原销售所属分公司 */
  formerGoverningBranch = '';

  /** 原销售 */
  formerSales = '';

  /** minorAdjustment */
  minorAdjustment = '';

  /** overdrafAmt */
  overdrafAmt = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 收款方分公司 */
  payeeBranch = '';

  /** payerBranch */
  payerBranch = '';

  /** 对应原因 */
  reason = '';

  /** reasonCreateBy */
  reasonCreateBy = '';

  /** reasonCreateDt */
  reasonCreateDt = '';

  /** 原因类型 */
  reasonType = '';

  /** receivableAmt */
  receivableAmt = '';

  /** receivableId */
  receivableId = '';

  /** receivableTemplate */
  receivableTemplate = '';

  /** receivableTempltId */
  receivableTempltId = '';

  /** 提醒时间 */
  remindDate = '';

  /** remindString */
  remindString = '';

  /** 提醒接收人 */
  reminderRecipient = '';

  /** reminderRecipientId */
  reminderRecipientId = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 处理状态 */
  statusName = '';

  /** verifyAmt */
  verifyAmt = '';

  /** verifyStatus */
  verifyStatus = '';
}

class quotationDTO {
  /** activityNameEn */
  activityNameEn = '';

  /** activityStatus */
  activityStatus = '';

  /** approveDt */
  approveDt = '';

  /** approveDtEnd */
  approveDtEnd = '';

  /** approveDtStart */
  approveDtStart = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_PROCESS_INS_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveProcessInsId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  approveStatus = undefined;

  /** approveStatusName */
  approveStatusName = '';

  /** attId */
  attId = '';

  /** attName */
  attName = '';

  /** auditOpinion */
  auditOpinion = '';

  /** 审批类型 */
  auditType = '';

  /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
  calculateType = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITE_QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  citeQuotationId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITY_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** 创建者name */
  createByName = '';

  /** createDtFrom */
  createDtFrom = '';

  /** createDtTo */
  createDtTo = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CURRENCY           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  currency = undefined;

  /** custCode */
  custCode = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CUST_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  custId = undefined;

  /** custName */
  custName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.DEPARTMENT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  departmentId = undefined;

  /** depthMark */
  depthMark = '';

  /** effectBy */
  effectBy = '';

  /** effectDt */
  effectDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.EXCHANGE_RATE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  exchangeRate = undefined;

  /** governingArea */
  governingArea = '';

  /** governingBranch */
  governingBranch = '';

  /** groupType */
  groupType = '';

  /** groupTypeName */
  groupTypeName = '';

  /** ifAf */
  ifAf = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_FIRST_AUDIT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifFirstAudit = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_NATIONWIDE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  ifNationwide = undefined;

  /** ifSs */
  ifSs = '';

  /** invalidBy */
  invalidBy = '';

  /** invalidDt */
  invalidDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IS_SPECIAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  isSpecialPrice = undefined;

  /** ladderQuotationType */
  ladderQuotationType = '';

  /** markType */
  markType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.NEW_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  newSaleId = undefined;

  /** newSaleName */
  newSaleName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.OLD_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  oldSaleId = undefined;

  /** participant */
  participant = '';

  /** processDefId */
  processDefId = '';

  /** processInsId */
  processInsId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_CODE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationCode = '';

  /** quotationGroupId */
  quotationGroupId = undefined;

  /** quotationHisId */
  quotationHisId = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationId = undefined;

  /** quotationItemCiList */
  quotationItemCiList = [];

  /** quotationItemCpList */
  quotationItemCpList = [];

  /** quotationItemDetailBusinessList */
  quotationItemDetailBusinessList = [];

  /** quotationItemDetailBusinessListUpdate */
  quotationItemDetailBusinessListUpdate = [];

  /** quotationItemDetailBusinessType */
  quotationItemDetailBusinessType = '';

  /** quotationItemDetailCiList */
  quotationItemDetailCiList = [];

  /** quotationItemDetailCiType */
  quotationItemDetailCiType = '';

  /** quotationItemDetailCpList */
  quotationItemDetailCpList = [];

  /** quotationItemDetailCpType */
  quotationItemDetailCpType = '';

  /** quotationItemDetailEmployerList */
  quotationItemDetailEmployerList = [];

  /** quotationItemDetailEmployerListUpdate */
  quotationItemDetailEmployerListUpdate = [];

  /** quotationItemDetailEmployerType */
  quotationItemDetailEmployerType = '';

  /** quotationItemDetailHealthList */
  quotationItemDetailHealthList = [];

  /** quotationItemDetailHealthListUpdate */
  quotationItemDetailHealthListUpdate = [];

  /** quotationItemDetailHealthType */
  quotationItemDetailHealthType = '';

  /** quotationItemDetailHmsList */
  quotationItemDetailHmsList = [];

  /** quotationItemDetailHmsType */
  quotationItemDetailHmsType = '';

  /** quotationItemDetailPeList */
  quotationItemDetailPeList = [];

  /** quotationItemDetailPeType */
  quotationItemDetailPeType = '';

  /** quotationItemDetailProductList */
  quotationItemDetailProductList = [];

  /** quotationItemDetailProductListUpdate */
  quotationItemDetailProductListUpdate = [];

  /** quotationItemDetailRpList */
  quotationItemDetailRpList = [];

  /** quotationItemDetailSaleList */
  quotationItemDetailSaleList = [];

  /** quotationItemDetailSaleListUpdate */
  quotationItemDetailSaleListUpdate = [];

  /** quotationItemDetailWelList */
  quotationItemDetailWelList = [];

  /** quotationItemDetailWelType */
  quotationItemDetailWelType = '';

  /** quotationItemHmsList */
  quotationItemHmsList = [];

  /** quotationItemPeList */
  quotationItemPeList = [];

  /** quotationItemRisk */
  quotationItemRisk = new QuotationItem();

  /** quotationItemRiskDetail */
  quotationItemRiskDetail = new QuotationItemDetail();

  /** quotationItemWelList */
  quotationItemWelList = [];

  /** quotationLadderList */
  quotationLadderList = [];

  /** quotationLadderListUpdate */
  quotationLadderListUpdate = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_NAME           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationName = '';

  /** quotationSpecial */
  quotationSpecial = new QuotationSpecial();

  /** quotationSpecialPropertyList */
  quotationSpecialPropertyList = [];

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_SPECIAL_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationSpecialType = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_COST           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalCost = undefined;

  /** 职场健康总售价 */
  quotationTotalOhPrice = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_SAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  quotationTotalSalPrice = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.REMARK           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  remark = '';

  /** remarkApp */
  remarkApp = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  status = undefined;

  /** statusName */
  statusName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SUPPLT_MED_INSUR_HEADCOUNT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  suppltMedInsurHeadcount = undefined;

  /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SVC_AREA           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
  svcArea = '';

  /** userId */
  userId = '';

  /** workitemId */
  workitemId = '';
}

export const crm = {
  BeforeSaleReportQueryBean,
  BusinessLogQuery,
  CommonResponse,
  Contract,
  ContractAttachmentDTO,
  ContractDTO,
  ContractFile,
  ContractQuery,
  ContractRetiree,
  CrmBusinessLog,
  CrmContractPayerDTO,
  CrmContractQuotationDTO,
  CrmLittleContractProductlineDTO,
  CrmPage,
  CustLogQuery,
  CustLoseReason,
  CustProductLine,
  CustReportQuery,
  CustTrack,
  Customer,
  CustomerLost,
  CustomerPayerDTO,
  CustomerPayerQuery,
  CustomerVisitQuery,
  DebtReminderCriteria,
  DropdownList,
  ExportQuery,
  FilterEntity,
  FormalCust,
  FormalCustVo,
  FormalQuery,
  GlobalResult,
  HsSlCustomerVisit,
  IfmCust,
  IfmCustQuery,
  Map,
  MarketActivityQuery,
  ModelContract,
  ModelContractDTO,
  ModelContractQuery,
  NonStaCoctApprDTO,
  NonStaCoctApprQuery,
  Page,
  ProductLine,
  QuotationItem,
  QuotationItemDetail,
  QuotationLadder,
  QuotationSpecial,
  QuotationSpecialProperty,
  SaleReportQuery,
  SendMailDTO,
  ShareAreaDTO,
  ShareAreaProductLines,
  ShareAreaProductLinesQuery,
  UptCustLogDTO,
  cityQuery,
  contractVO,
  customerServiceDTO,
  customerSimilarityDTO,
  debtReminderCriteriaQuery,
  debtReminderQuery,
  quotationDTO,
};
